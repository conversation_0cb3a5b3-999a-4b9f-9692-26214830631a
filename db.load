LOAD DATABASE
    FROM      mysql://root:rootpwd@db:3306/lvp-portal
    INTO ***************************************/lvp-portal

-- EXCLUDE the tables that don't exist in the new schema
EXCLUDING TABLE NAMES MATCHING 'doctrine_migration_versions', 'cron_job', 'cron_execution'

WITH include no drop, truncate, disable triggers, create no tables,
    create no indexes, preserve index names,
    data only

CAST type datetime to timestamp,
    type char to uuid,
    type longtext to json,
    type tinyint to boolean using tinyint-to-boolean

ALTER SCHEMA 'lvp-portal' RENAME TO 'public'

BEFORE LOAD DO
   $$create schema if not exists public;$$

-- Use COALESCE to safely handle empty tables
AFTER LOAD DO
   $$SELECT setval('collect_order_id_seq', COALESCE((SELECT MAX(id) FROM collect_order), 0) + 1, false);$$,
   $$SELECT setval('collecting_place_id_seq', COALESCE((SELECT MAX(id) FROM collecting_place), 0) + 1, false);$$,
   $$SELECT setval('contract_area_id_seq', COALESCE((SELECT MAX(id) FROM contract_area), 0) + 1, false);$$,
   $$SELECT setval('contract_area_validity_id_seq', COALESCE((SELECT MAX(id) FROM contract_area_validity), 0) + 1, false);$$,
   $$SELECT setval('contract_id_seq', COALESCE((SELECT MAX(id) FROM contract), 0) + 1, false);$$,
   $$SELECT setval('document_id_seq', COALESCE((SELECT MAX(id) FROM document), 0) + 1, false);$$,
   $$SELECT setval('document_type_id_seq', COALESCE((SELECT MAX(id) FROM document_type), 0) + 1, false);$$,
   $$SELECT setval('feature_id_seq', COALESCE((SELECT MAX(id) FROM feature), 0) + 1, false);$$,
   $$SELECT setval('state_id_seq', COALESCE((SELECT MAX(id) FROM state), 0) + 1, false);$$,
   $$SELECT setval('system_provider_id_seq', COALESCE((SELECT MAX(id) FROM system_provider), 0) + 1, false);$$,
   $$SELECT setval('unloading_point_id_seq', COALESCE((SELECT MAX(id) FROM unloading_point), 0) + 1, false);$$,
   $$SELECT setval('unloading_point_validity_id_seq', COALESCE((SELECT MAX(id) FROM unloading_point_validity), 0) + 1, false);$$,
   $$SELECT setval('user_access_id_seq', COALESCE((SELECT MAX(id) FROM user_access), 0) + 1, false);$$,
   $$SELECT setval('user_id_seq', COALESCE((SELECT MAX(id) FROM "user"), 0) + 1, false);$$;