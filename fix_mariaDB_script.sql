UPDATE contract
SET valid_from = '1970-01-01 00:00:00'
WHERE valid_from <= '1970-01-01 00:00:00' OR valid_from IS NULL;
UPDATE contract
SET valid_to = '9999-12-31 23:59:59'
WHERE valid_to <= '1970-01-01 00:00:00' OR valid_to IS NULL;
ALTER TABLE user ADD COLUMN locale VARCHAR(10) NOT NULL DEFAULT 'de';
UPDATE document SET amount = 0 WHERE amount REGEXP '[^0-9.-]';
ALTER TABLE document MODIFY COLUMN amount FLOAT;