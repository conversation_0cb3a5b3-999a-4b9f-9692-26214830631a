###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
/bin/.phpunit
###< phpunit/phpunit ###

tests.txt

###> symfony/webpack-encore-bundle ###
/node_modules/
/public/build/
npm-debug.log
yarn-error.log
###< symfony/webpack-encore-bundle ###

.DS_Store
.idea
###> friendsofphp/php-cs-fixer ###
/.php-cs-fixer.php
/.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

.cf-vars.*.local.yaml

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

# VSCode
.vscode

###> docker related stuffs ###
.devcontainer/data
###< docker related stuffs ###

###> composer ###
auth.json
###< composer ###
