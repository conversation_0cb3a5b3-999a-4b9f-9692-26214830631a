# Changelog

## [2.0.3](https://github.com/prezero/lvpportal-sf/compare/v2.0.2...v2.0.3) (2025-06-18)


### Continuous Integration

* add missing env vars to deploy scripts ([111b44e](https://github.com/prezero/lvpportal-sf/commit/111b44ecfb5e0b3f89516876e27052a35feeb7de))
* fix deploy dev image tag var ([563f135](https://github.com/prezero/lvpportal-sf/commit/563f1354de8c121471874ca9e3128798aababe70))
* use self-hosted runner ([#89](https://github.com/prezero/lvpportal-sf/issues/89)) ([b735377](https://github.com/prezero/lvpportal-sf/commit/b7353775b88d2818a1513d92c752c5a5c237c0d7))

## [2.0.2](https://github.com/prezero/lvpportal-sf/compare/v2.0.1...v2.0.2) (2025-06-17)


### Continuous Integration

* remove old workflows ([101e7ab](https://github.com/prezero/lvpportal-sf/commit/101e7abaf18ca835ec3688b07be2a8373991b32c))

## [2.0.1](https://github.com/prezero/lvpportal-sf/compare/v2.0.0...v2.0.1) (2025-06-17)


### Bug Fixes

* dockerfile ([e8e5472](https://github.com/prezero/lvpportal-sf/commit/e8e5472f154b7a0f6ce87c5350249a6e49f6733c))
* dockerfile ([d786265](https://github.com/prezero/lvpportal-sf/commit/d78626554689afc3932aad001f6492c7eb04aa3f))
* dockerfile ([6b8e8f3](https://github.com/prezero/lvpportal-sf/commit/6b8e8f30398ca25570886fba2674c0918043d147))
* dockerfile ([e26f5ad](https://github.com/prezero/lvpportal-sf/commit/e26f5ad6f2e5d8a3a3a71ca5ca5ad083a67fa451))
* empty contracts ([39fc777](https://github.com/prezero/lvpportal-sf/commit/39fc777f66e398c68a7d6e63d97fe0f3afd9bc47))
* empty contracts ([fd5e664](https://github.com/prezero/lvpportal-sf/commit/fd5e664bb1ec7ef932d51ec8f3e4cecc227a6ec7))
* feiertage ([d05ac40](https://github.com/prezero/lvpportal-sf/commit/d05ac40b1215afb9457cbe2f2dcedf773080b7cd))
* feiertage ([09d5e8a](https://github.com/prezero/lvpportal-sf/commit/09d5e8a7e91b340fbec99ee840edb3d98a28ff30))
* fix runner in the workflows ([e0c1330](https://github.com/prezero/lvpportal-sf/commit/e0c1330d7a38bee511b21cac67f47b9db962dffb))
* fix typo in deploy dev yaml ([789f72f](https://github.com/prezero/lvpportal-sf/commit/789f72fd0869c99f9caa5e7a92702d94b12d8338))
* fix typo in deploy dev yaml ([3261be3](https://github.com/prezero/lvpportal-sf/commit/3261be3444e2abdb5aa84eb1d698ded8b6b9f6f0))
* packages ([68a8446](https://github.com/prezero/lvpportal-sf/commit/68a84462da34aeea1223aa6acb2ad4b47d73d713))
* packages ([b8da401](https://github.com/prezero/lvpportal-sf/commit/b8da4013fe40d7a3ef62a55a03eb148d28be62c4))
* packages ([f1de5c7](https://github.com/prezero/lvpportal-sf/commit/f1de5c781b0918ffd2a75d9300753cfcd5b20e6c))
* packages ([fc1b97d](https://github.com/prezero/lvpportal-sf/commit/fc1b97d2173bd9e1c3a15dab2bd6912c34e92f66))
* packages ([b2ac907](https://github.com/prezero/lvpportal-sf/commit/b2ac907f6ce84a8eb9222737d321f1d66a0ae6fd))
* packages ([49ba27f](https://github.com/prezero/lvpportal-sf/commit/49ba27fc75a40543148e4f42c3d632fa1e3b6120))
* week booking button ([e4c1453](https://github.com/prezero/lvpportal-sf/commit/e4c1453dbbd1e656df999c935a8c2d77590e88c4))
* workflow ([4c48aea](https://github.com/prezero/lvpportal-sf/commit/4c48aeaf87546a188232c76f8af95dc17677aab1))
* workflow ([d78323c](https://github.com/prezero/lvpportal-sf/commit/d78323cf8863da8036dbc93e1ffd3cf7b713070a))


### Continuous Integration

* add release-please workflow ([a56b727](https://github.com/prezero/lvpportal-sf/commit/a56b727e56684d74ae30610c1979d1a38dd31787))
* implement CF image deploy ([#72](https://github.com/prezero/lvpportal-sf/issues/72)) ([d49f719](https://github.com/prezero/lvpportal-sf/commit/d49f7192f257288c44f993b8d130774d326ea2ef))
* Integrate release-please along with the necessary workflows ([194de7a](https://github.com/prezero/lvpportal-sf/commit/194de7a710948debace7d8608ea138bcb78c6723))
* update release-please to use googleapis instead of depricated google-github-actions ([cd3585e](https://github.com/prezero/lvpportal-sf/commit/cd3585ec8268f17d6215ee0bbc07dbfb4dec2532))
* use self-hosted runner ([082445b](https://github.com/prezero/lvpportal-sf/commit/082445b45da6b879f1bd4bf1bd05b80d02e78b07))
* use self-hosted runner ([386987c](https://github.com/prezero/lvpportal-sf/commit/386987cb7ef45a266f2ff84fd0e08d9cea00c505))

## [2.0.0] Initial
