parameters:
	ignoreErrors:
		-
			message: '#^Call to an undefined method Doctrine\\ORM\\EntityRepository\<App\\Entity\\Main\\Order\>\:\:findTransferOpenInBetween\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Command/SendOpenCollectOrdersCommand.php

		-
			message: '#^Dead catch \- Exception is never thrown in the try block\.$#'
			identifier: catch.neverThrown
			count: 1
			path: src/Command/SendOpenCollectOrdersCommand.php

		-
			message: '#^Undefined variable\: \$input$#'
			identifier: variable.undefined
			count: 1
			path: src/Command/SendOpenCollectOrdersCommand.php

		-
			message: '#^Class App\\Controller\\Admin\\CollectingPlaceCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/CollectingPlaceCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\ContractAreaCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/ContractAreaCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\ContractAreaValidityCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/ContractAreaValidityCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\ContractCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/ContractCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\OrderCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/OrderCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\StateCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/StateCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\SystemProviderCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/SystemProviderCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\UnloadingPointCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/UnloadingPointCrudController.php

		-
			message: '#^Class App\\Controller\\Admin\\UserCrudController extends generic class EasyCorp\\Bundle\\EasyAdminBundle\\Controller\\AbstractCrudController but does not specify its types\: TEntity$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Admin/UserCrudController.php

		-
			message: '#^Call to an undefined method Doctrine\\ORM\\EntityRepository\<App\\Entity\\Main\\Order\>\:\:findInBetween\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Controller/AjaxController.php

		-
			message: '#^Function getTotalHours not found\.$#'
			identifier: function.notFound
			count: 2
			path: src/Controller/AjaxController.php

		-
			message: '#^If condition is always true\.$#'
			identifier: if.alwaysTrue
			count: 1
			path: src/Controller/AjaxController.php

		-
			message: '#^Inner named functions are not supported by PHPStan\. Consider refactoring to an anonymous function, class method, or a top\-level\-defined function\. See issue \#165 \(https\://github\.com/phpstan/phpstan/issues/165\) for more details\.$#'
			identifier: function.inner
			count: 1
			path: src/Controller/AjaxController.php

		-
			message: '#^Parameter \$user of method App\\Services\\AccessHelper\:\:checkUserAccessCombination\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 2
			path: src/Controller/AjaxController.php

		-
			message: '#^Parameter \$user of method App\\Services\\AccessHelper\:\:checkUserAccessForCollectingPlace\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 2
			path: src/Controller/AjaxController.php

		-
			message: '#^Parameter \$user of method App\\Services\\Mailer\:\:sendCancelOrder\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/AjaxController.php

		-
			message: '#^Parameter \$user of method App\\Services\\Mailer\:\:sendOrderList\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/AjaxController.php

		-
			message: '#^Method App\\Controller\\Api\\CollectingPlaceController\:\:viewCollectingPlaceNotExist\(\) has parameter \$collectingPlaceId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractAreaController\:\:viewContractAreaNotExist\(\) has parameter \$contractId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractAreaController\:\:viewStateNotFound\(\) has parameter \$state with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractAreaValidityController\:\:viewNoContractArea\(\) has parameter \$contractId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractAreaValidityController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractAreaValidityController\:\:viewValidityNotExists\(\) has parameter \$validityUuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractAreaValidityController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractController\:\:viewContractNotExist\(\) has parameter \$contractId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractController\:\:viewContractNumberMissing\(\) has parameter \$contractNumber with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractController.php

		-
			message: '#^Method App\\Controller\\Api\\ContractController\:\:viewProviderNotExist\(\) has parameter \$providerId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/ContractController.php

		-
			message: '#^PHPDoc tag @example has invalid value \(\("Request\: curl \-u api\:api \-F "file\=@example\.pdf" localhost/file/1b6e1c78\-d326\-11eb\-ac71\-0242ac1c0004"\)\)\: Unexpected token "file", expected ''\)'' at offset 81 on line 4$#'
			identifier: phpDoc.parseError
			count: 2
			path: src/Controller/Api/DocumentController.php

		-
			message: '#^PHPDoc tag @example has invalid value \(\("Request\: curl \-u api\:api \-F "file\=@example\.pdf" localhost/file/1b6e1c78\-d326\-11eb\-ac71\-0242ac1c0004\?update\=1"\)\)\: Unexpected token "file", expected ''\)'' at offset 200 on line 5$#'
			identifier: phpDoc.parseError
			count: 2
			path: src/Controller/Api/DocumentController.php

		-
			message: '#^Parameter \$documentId of method App\\Controller\\Api\\DocumentController\:\:viewDocumentExists\(\) expects string, Ramsey\\Uuid\\UuidInterface\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/Api/DocumentController.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:setCanceled\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Controller/Api/LogisticsController.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:setCanceled\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Controller/Api/OrderController.php

		-
			message: '#^Method App\\Controller\\Api\\SystemProviderController\:\:viewSystemProviderNotExist\(\) has parameter \$materialId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/SystemProviderController.php

		-
			message: '#^Method App\\Controller\\Api\\UnloadingPointController\:\:viewUnloadingPointNotExist\(\) has parameter \$unloadingPointId with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Api/UnloadingPointController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:__construct\(\) has parameter \$repository with generic class Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepository but does not specify its types\: T$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getDetailViewOptions\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getDetailViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getDetailViewOptions\(\) has parameter \$request with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getDetailViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getFormOptions\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getFormOptions\(\) has parameter \$mode with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getFormOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getFormOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getListFindBy\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getListSorting\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getListViewOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getListViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:getListViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processDelete\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processDelete\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processDetails\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processDetails\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processList\(\) has parameter \$newEntity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processList\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processLock\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processLock\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processReset\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:processReset\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:sortListView\(\) has parameter \$column with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:sortListView\(\) has parameter \$list with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:sortListView\(\) has parameter \$order with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\BaseCrudController\:\:sortListView\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/BaseCrudController.php

		-
			message: '#^Parameter \$user of method App\\Services\\AccessHelper\:\:checkUserAccessForCollectingPlace\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 2
			path: src/Controller/ContactController.php

		-
			message: '#^Call to method addValidityMessage\(\) on an unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Call to method getDtObject\(\) on an unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 3
			path: src/Controller/DefaultController.php

		-
			message: '#^Call to method setIsOrderAble\(\) on an unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 2
			path: src/Controller/DefaultController.php

		-
			message: '#^Call to method setOrderCountMax\(\) on an unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 2
			path: src/Controller/DefaultController.php

		-
			message: '#^Call to method setPublicHolidayName\(\) on an unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Call to method setisPublicHoliday\(\) on an unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:compliance\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:dataPrivacy\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:imprint\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:orderNewClearance\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:orderNewClearanceList\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:orderOverview\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Method App\\Controller\\DefaultController\:\:termsAndConditions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^PHPDoc tag @param for parameter \$collectingPlace with type App\\Entity\\Main\\CollectingPlace\|null is incompatible with native type string\.$#'
			identifier: parameter.phpDocType
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^PHPDoc tag @var for variable \$calendarDay contains unknown class App\\Controller\\CalendarDay\.$#'
			identifier: class.notFound
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^PHPDoc tag @var with type App\\Entity\\Main\\Order is not subtype of native type null\.$#'
			identifier: varTag.nativeType
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$day of method App\\Services\\CalendarValidator\:\:checkAndSetOrderLimit\(\) expects App\\Services\\CalendarDay, App\\Controller\\CalendarDay given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$day of method App\\Services\\CalendarValidator\:\:dayToOld\(\) expects App\\Services\\CalendarDay, App\\Controller\\CalendarDay given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$day of method App\\Services\\CalendarValidator\:\:inAreaTimeRange\(\) expects App\\Services\\CalendarDay, App\\Controller\\CalendarDay given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$day of method App\\Services\\CalendarValidator\:\:inValidityTimeRange\(\) expects App\\Services\\CalendarDay, App\\Controller\\CalendarDay given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$day of method App\\Services\\CalendarValidator\:\:inWeekOrderLimit\(\) expects App\\Services\\CalendarDay, App\\Controller\\CalendarDay given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$day of method App\\Services\\CalendarValidator\:\:isAlreadyTransfered\(\) expects App\\Services\\CalendarDay, App\\Controller\\CalendarDay given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Parameter \$user of method App\\Services\\AccessHelper\:\:checkUserAccessCombination\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Variable \$area in PHPDoc tag @var does not match assigned variable \$areaRepo\.$#'
			identifier: varTag.differentVariable
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Variable \$area in PHPDoc tag @var does not match assigned variable \$collectingPlaceRepo\.$#'
			identifier: varTag.differentVariable
			count: 1
			path: src/Controller/DefaultController.php

		-
			message: '#^Call to an undefined method App\\Repository\\Files\\DocumentDataRepository\:\:getFile\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Call to an undefined method App\\Repository\\Files\\DocumentDataRepository\:\:getMimeType\(\)\.$#'
			identifier: method.notFound
			count: 2
			path: src/Controller/DocumentController.php

		-
			message: '#^Constructor of class App\\Controller\\DocumentController has an unused parameter \$requestStack\.$#'
			identifier: constructor.unusedParameter
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:getListFindBy\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:getListViewOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:getListViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:getListViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\DocumentController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Parameter \$user of method App\\Services\\AccessHelper\:\:checkUserAccessCombination\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/DocumentController.php

		-
			message: '#^Parameter \$user of method App\\Services\\AccessHelper\:\:getCollectingPlaceList\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 2
			path: src/Controller/DocumentController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:__construct\(\) has parameter \$repository with generic class Doctrine\\Bundle\\DoctrineBundle\\Repository\\ServiceEntityRepository but does not specify its types\: T$#'
			identifier: missingType.generics
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getDetailViewOptions\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getDetailViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getDetailViewOptions\(\) has parameter \$request with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getDetailViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getFormOptions\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getFormOptions\(\) has parameter \$mode with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getFormOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getFormOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getListFindBy\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getListSorting\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getListViewOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getListViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:getListViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processDelete\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processDelete\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processDetails\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processDetails\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processList\(\) has parameter \$newEntity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processList\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processLock\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processLock\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processReset\(\) has parameter \$routeParams with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:processReset\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:sortListView\(\) has parameter \$column with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:sortListView\(\) has parameter \$list with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:sortListView\(\) has parameter \$order with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\BaseCrudController\:\:sortListView\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/BaseCrudController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:details\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getDetailViewOptions\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getDetailViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getDetailViewOptions\(\) has parameter \$request with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getDetailViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getFormOptions\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getFormOptions\(\) has parameter \$mode with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getFormOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getFormOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getListViewOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getListViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:getListViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:lock\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:processList\(\) has parameter \$formType with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:processList\(\) has parameter \$newEntity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:processList\(\) has parameter \$redirectRoute with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\CollectingPlaceController\:\:processList\(\) has parameter \$routeParams with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/CollectingPlaceController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:details\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:detailsCollectingplaces\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:detailsContingents\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:detailsContracts\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getCollectingPlaceOptions\(\) has parameter \$details with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getCollectingPlaceOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getCollectingPlaceOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getCollectingPlaceOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractAreaOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractAreaOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractAreaOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractAreaValidityOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractAreaValidityOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractAreaValidityOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getContractOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getFormOptions\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getFormOptions\(\) has parameter \$mode with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getFormOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getFormOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getUserOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getUserOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:getUserOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapEntityToDto\(\) has parameter \$collectingPlace with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapEntityToDto\(\) has parameter \$contractArea with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:mapEntityToDto\(\) has parameter \$request with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaceUsers\(\) has parameter \$collectingPlace with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaceUsers\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaceUsersAdd\(\) has parameter \$collectingPlace with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaceUsersAdd\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaceUsersDelete\(\) has parameter \$collectingPlace with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaceUsersDelete\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionCollectingplaces\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionUsers\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionUsersAdd\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Method App\\Controller\\Management\\ContractAreaController\:\:permissionUsersDelete\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ContractAreaController.php

		-
			message: '#^Constructor of class App\\Controller\\Management\\ReportingController has an unused parameter \$requestStack\.$#'
			identifier: constructor.unusedParameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getCollectingPlaceOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getCollectingPlaceOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getCollectingPlaceOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getContractAreaOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getContractAreaOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getContractAreaOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getUserOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getUserOptions\(\) has parameter \$options with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:getUserOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingCollectingPlaceDetails\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingCollectingPlaceDetailsContractArea\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingCollectingPlaceDetailsUsers\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingContractAreaDetails\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingContractAreaDetailsCollectingPlaces\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingContractAreaDetailsUsers\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingUserDetails\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingUserDetailsCollectingPlaces\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:managementReportingUserDetailsContractArea\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\ReportingController\:\:mapEntityToDto\(\) has parameter \$request with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/ReportingController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:delete\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:details\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getDetailViewOptions\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getDetailViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getDetailViewOptions\(\) has parameter \$request with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getDetailViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getFormOptions\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getFormOptions\(\) has parameter \$mode with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getFormOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getFormOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getListViewOptions\(\) has parameter \$entityList with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getListViewOptions\(\) has parameter \$options with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:getListViewOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:lock\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:mapDtoToEntity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:mapDtoToEntity\(\) has parameter \$dto with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:mapDtoToEntity\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:mapEntityToDto\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:mapEntityToDto\(\) has parameter \$entity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processList\(\) has parameter \$formType with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processList\(\) has parameter \$newEntity with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processList\(\) has parameter \$redirectRoute with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processList\(\) has parameter \$routeParams with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processReset\(\) has parameter \$redirectRoute with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processReset\(\) has parameter \$routeParams with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:processReset\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\Management\\UserController\:\:reset\(\) has parameter \$uuid with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Controller/Management/UserController.php

		-
			message: '#^Method App\\Controller\\ManagementController\:\:orderReport\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/ManagementController.php

		-
			message: '#^Method App\\Controller\\SecurityController\:\:newpassword\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/SecurityController.php

		-
			message: '#^Method App\\Controller\\SecurityController\:\:passwordreset\(\) should return array\<string\>\|Symfony\\Component\\HttpFoundation\\Response but returns array\<string, Symfony\\Component\\Form\\FormView\>\.$#'
			identifier: return.type
			count: 1
			path: src/Controller/SecurityController.php

		-
			message: '#^Method App\\Entity\\Main\\User\:\:setPasswordResetHash\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Controller/SecurityController.php

		-
			message: '#^Method App\\Controller\\UserProfileController\:\:userProfileChangeLocale\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/UserProfileController.php

		-
			message: '#^Method App\\Controller\\UserProfileController\:\:userProfileChangeMail\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/UserProfileController.php

		-
			message: '#^Method App\\Controller\\UserProfileController\:\:userProfileNewPassword\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Controller/UserProfileController.php

		-
			message: '#^Parameter \$user of method App\\Services\\Mailer\:\:sendPasswordReset\(\) expects App\\Entity\\Main\\User, Symfony\\Component\\Security\\Core\\User\\UserInterface\|null given\.$#'
			identifier: argument.type
			count: 1
			path: src/Controller/UserProfileController.php

		-
			message: '#^Method App\\Entity\\Main\\CollectingPlace\:\:setName2\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/DataFixtures/CollectingPlaceFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 3
			path: src/DataFixtures/CollectingPlaceFixtures.php

		-
			message: '#^Missing parameter \$state \(string\) in call to method App\\DataFixtures\\CollectingPlaceFixtures\:\:createCollectingPlace\(\)\.$#'
			identifier: argument.missing
			count: 3
			path: src/DataFixtures/CollectingPlaceFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 3
			path: src/DataFixtures/CollectingPlaceFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 2
			path: src/DataFixtures/ContractAreaFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 2
			path: src/DataFixtures/ContractAreaFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 1
			path: src/DataFixtures/ContractAreaValidityFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 1
			path: src/DataFixtures/ContractAreaValidityFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 6
			path: src/DataFixtures/ContractFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 6
			path: src/DataFixtures/ContractFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 2
			path: src/DataFixtures/DocumentFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 2
			path: src/DataFixtures/DocumentFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 5
			path: src/DataFixtures/OrderFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 5
			path: src/DataFixtures/OrderFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 1
			path: src/DataFixtures/SystemProviderFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 1
			path: src/DataFixtures/SystemProviderFixtures.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPoint\:\:setName2\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/DataFixtures/UnloadingPointFixtures.php

		-
			message: '#^Missing parameter \$class \(class\-string\<object\>\) in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)\.$#'
			identifier: argument.missing
			count: 3
			path: src/DataFixtures/UnloadingPointFixtures.php

		-
			message: '#^Missing parameter \$state \(string\) in call to method App\\DataFixtures\\UnloadingPointFixtures\:\:createUnloadingPoint\(\)\.$#'
			identifier: argument.missing
			count: 2
			path: src/DataFixtures/UnloadingPointFixtures.php

		-
			message: '#^Unable to resolve the template type T in call to method Doctrine\\Common\\DataFixtures\\AbstractFixture\:\:getReference\(\)$#'
			identifier: argument.templateType
			count: 3
			path: src/DataFixtures/UnloadingPointFixtures.php

		-
			message: '#^Property App\\Dto\\CollectingPlaceDto\:\:\$locked type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/CollectingPlaceDto.php

		-
			message: '#^Property App\\Dto\\ReportingCollectingPlaceDto\:\:\$locked type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/ReportingCollectingPlaceDto.php

		-
			message: '#^Property App\\Dto\\ReportingUserDto\:\:\$active type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/ReportingUserDto.php

		-
			message: '#^Property App\\Dto\\ReportingUserDto\:\:\$assignedRoles type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/ReportingUserDto.php

		-
			message: '#^Property App\\Dto\\ReportingUserDto\:\:\$locked type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/ReportingUserDto.php

		-
			message: '#^Property App\\Dto\\ReportingUserDto\:\:\$reachableRoles type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/ReportingUserDto.php

		-
			message: '#^Property App\\Dto\\ReportingUserDto\:\:\$roles type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/ReportingUserDto.php

		-
			message: '#^Property App\\Dto\\UserDto\:\:\$active type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/UserDto.php

		-
			message: '#^Property App\\Dto\\UserDto\:\:\$assignedRoles type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/UserDto.php

		-
			message: '#^Property App\\Dto\\UserDto\:\:\$locked type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/UserDto.php

		-
			message: '#^Property App\\Dto\\UserDto\:\:\$reachableRoles type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/UserDto.php

		-
			message: '#^Property App\\Dto\\UserDto\:\:\$roles type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Dto/UserDto.php

		-
			message: '#^Method App\\Entity\\Common\\HasCreateModifyStamps\:\:setCreatedAt\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasCreateModifyStamps.php

		-
			message: '#^Method App\\Entity\\Common\\HasCreateModifyStamps\:\:setCreatedBy\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasCreateModifyStamps.php

		-
			message: '#^Method App\\Entity\\Common\\HasCreateModifyStamps\:\:setModifiedAt\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasCreateModifyStamps.php

		-
			message: '#^Method App\\Entity\\Common\\HasCreateModifyStamps\:\:setModifiedBy\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasCreateModifyStamps.php

		-
			message: '#^Method App\\Entity\\Common\\HasDeleted\:\:setDeleted\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasDeleted.php

		-
			message: '#^Method App\\Entity\\Common\\HasLocked\:\:setLocked\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasLocked.php

		-
			message: '#^Method App\\Entity\\Common\\HasUuid\:\:setUuid\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Common/HasUuid.php

		-
			message: '#^Method App\\Entity\\Files\\DocumentData\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Method App\\Entity\\Files\\DocumentData\:\:getFile\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Method App\\Entity\\Files\\DocumentData\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Method App\\Entity\\Files\\DocumentData\:\:setFile\(\) has parameter \$file with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Property App\\Entity\\Files\\DocumentData\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Property App\\Entity\\Files\\DocumentData\:\:\$file has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Property App\\Entity\\Files\\DocumentData\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Files/DocumentData.php

		-
			message: '#^Method App\\Entity\\Main\\CollectingPlace\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Method App\\Entity\\Main\\CollectingPlace\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Method App\\Entity\\Main\\CollectingPlace\:\:setState\(\) has parameter \$state with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Method App\\Entity\\Main\\Contract\:\:setCollectingPlace\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^PHPDoc tag @param has invalid value \(State\)\: Unexpected token "\\n     ", expected variable at offset 23 on line 2$#'
			identifier: phpDoc.parseError
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\ManyToOne constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\OneToMany constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Property App\\Entity\\Main\\CollectingPlace\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Property App\\Entity\\Main\\CollectingPlace\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Property App\\Entity\\Main\\CollectingPlace\:\:\$userAccessList with generic interface Doctrine\\Common\\Collections\\Collection does not specify its types\: TKey, T$#'
			identifier: missingType.generics
			count: 1
			path: src/Entity/Main/CollectingPlace.php

		-
			message: '#^Method App\\Entity\\Main\\Contract\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Contract.php

		-
			message: '#^Method App\\Entity\\Main\\Contract\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Contract.php

		-
			message: '#^Method App\\Entity\\Main\\Document\:\:setContract\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/Contract.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:setContract\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/Contract.php

		-
			message: '#^Property App\\Entity\\Main\\Contract\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Contract.php

		-
			message: '#^Property App\\Entity\\Main\\Contract\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Contract.php

		-
			message: '#^Method App\\Entity\\Main\\Contract\:\:setContractArea\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Method App\\Entity\\Main\\ContractArea\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Method App\\Entity\\Main\\ContractArea\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Method App\\Entity\\Main\\ContractArea\:\:setState\(\) has parameter \$state with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Method App\\Entity\\Main\\ContractAreaValidity\:\:setContractArea\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:setContractArea\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^PHPDoc tag @param has invalid value \(State\)\: Unexpected token "\\n     ", expected variable at offset 23 on line 2$#'
			identifier: phpDoc.parseError
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\ManyToOne constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\OneToMany constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Property App\\Entity\\Main\\ContractArea\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Property App\\Entity\\Main\\ContractArea\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Property App\\Entity\\Main\\ContractArea\:\:\$userAccessList with generic interface Doctrine\\Common\\Collections\\Collection does not specify its types\: TKey, T$#'
			identifier: missingType.generics
			count: 1
			path: src/Entity/Main/ContractArea.php

		-
			message: '#^Method App\\Entity\\Main\\ContractAreaValidity\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/ContractAreaValidity.php

		-
			message: '#^Method App\\Entity\\Main\\ContractAreaValidity\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/ContractAreaValidity.php

		-
			message: '#^Property App\\Entity\\Main\\ContractAreaValidity\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/ContractAreaValidity.php

		-
			message: '#^Property App\\Entity\\Main\\ContractAreaValidity\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/ContractAreaValidity.php

		-
			message: '#^Method App\\Entity\\Main\\Document\:\:getAdditionals\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Method App\\Entity\\Main\\Document\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Method App\\Entity\\Main\\Document\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Method App\\Entity\\Main\\Document\:\:setAdditionals\(\) has parameter \$additionals with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Property App\\Entity\\Main\\Document\:\:\$additionals has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Property App\\Entity\\Main\\Document\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Property App\\Entity\\Main\\Document\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Document.php

		-
			message: '#^Method App\\Entity\\Main\\Document\:\:setDocumentType\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/DocumentType.php

		-
			message: '#^Method App\\Entity\\Main\\DocumentType\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/DocumentType.php

		-
			message: '#^Method App\\Entity\\Main\\DocumentType\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/DocumentType.php

		-
			message: '#^Property App\\Entity\\Main\\DocumentType\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/DocumentType.php

		-
			message: '#^Property App\\Entity\\Main\\DocumentType\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/DocumentType.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:getCanceled\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Order.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Order.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Order.php

		-
			message: '#^Method App\\Entity\\Main\\Order\:\:getTransfered\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/Order.php

		-
			message: '#^Property App\\Entity\\Main\\Order\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Order.php

		-
			message: '#^Property App\\Entity\\Main\\Order\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/Order.php

		-
			message: '#^Call to an undefined method App\\Entity\\Main\\ContractArea\:\:addState\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Call to an undefined method App\\Entity\\Main\\ContractArea\:\:removeState\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Method App\\Entity\\Main\\State\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Method App\\Entity\\Main\\State\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\OneToMany constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Property App\\Entity\\Main\\State\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Property App\\Entity\\Main\\State\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Property App\\Entity\\Main\\State\:\:\$publicHolidays has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/State.php

		-
			message: '#^Method App\\Entity\\Main\\SystemProvider\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/SystemProvider.php

		-
			message: '#^Method App\\Entity\\Main\\SystemProvider\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/SystemProvider.php

		-
			message: '#^Property App\\Entity\\Main\\SystemProvider\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/SystemProvider.php

		-
			message: '#^Property App\\Entity\\Main\\SystemProvider\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/SystemProvider.php

		-
			message: '#^Method App\\Entity\\Main\\Contract\:\:setUnloadingPoint\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPoint\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPoint\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPoint\:\:setState\(\) has parameter \$state with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPointValidity\:\:setUnloadingPoint\(\) invoked with 0 parameters, 1 required\.$#'
			identifier: arguments.count
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^PHPDoc tag @param has invalid value \(State\)\: Unexpected token "\\n     ", expected variable at offset 23 on line 2$#'
			identifier: phpDoc.parseError
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\ManyToOne constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Property App\\Entity\\Main\\UnloadingPoint\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Property App\\Entity\\Main\\UnloadingPoint\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/UnloadingPoint.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPointValidity\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/UnloadingPointValidity.php

		-
			message: '#^Method App\\Entity\\Main\\UnloadingPointValidity\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/UnloadingPointValidity.php

		-
			message: '#^Property App\\Entity\\Main\\UnloadingPointValidity\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/UnloadingPointValidity.php

		-
			message: '#^Property App\\Entity\\Main\\UnloadingPointValidity\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/UnloadingPointValidity.php

		-
			message: '#^Method App\\Entity\\Main\\User\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Method App\\Entity\\Main\\User\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Method App\\Entity\\Main\\User\:\:setRoles\(\) has parameter \$roles with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Parameter \$targetEntity of attribute class Doctrine\\ORM\\Mapping\\OneToMany constructor expects class\-string\|null, string given\.$#'
			identifier: argument.type
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Property App\\Entity\\Main\\User\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Property App\\Entity\\Main\\User\:\:\$id \(int\|null\) is never assigned int so it can be removed from the property type\.$#'
			identifier: property.unusedType
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Property App\\Entity\\Main\\User\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Property App\\Entity\\Main\\User\:\:\$roles has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Property App\\Entity\\Main\\User\:\:\$userAccessList with generic interface Doctrine\\Common\\Collections\\Collection does not specify its types\: TKey, T$#'
			identifier: missingType.generics
			count: 1
			path: src/Entity/Main/User.php

		-
			message: '#^Method App\\Entity\\Main\\UserAccess\:\:getCreatedAt\(\) should return DateTime\|null but returns DateTimeInterface\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/UserAccess.php

		-
			message: '#^Method App\\Entity\\Main\\UserAccess\:\:getModifiedAt\(\) should return DateTime\|null but returns DateTimeInterface\|null\.$#'
			identifier: return.type
			count: 1
			path: src/Entity/Main/UserAccess.php

		-
			message: '#^Property App\\Entity\\Main\\UserAccess\:\:\$createdBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/UserAccess.php

		-
			message: '#^Property App\\Entity\\Main\\UserAccess\:\:\$modifiedBy has no type specified\.$#'
			identifier: missingType.property
			count: 1
			path: src/Entity/Main/UserAccess.php

		-
			message: '#^Method App\\EventListener\\CommonTraitSubscriber\:\:setCreatedFields\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/CommonTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\CommonTraitSubscriber\:\:setUpdatedFields\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/CommonTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\CommonTraitSubscriber\:\:setUuid\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/CommonTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\DeletedTraitSubscriber\:\:setCreatedFields\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/DeletedTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\DeletedTraitSubscriber\:\:setUpdatedFields\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/DeletedTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\LockedTraitSubscriber\:\:setCreatedFields\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/LockedTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\LockedTraitSubscriber\:\:setUpdatedFields\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/LockedTraitSubscriber.php

		-
			message: '#^Method App\\EventListener\\UserSubscriber\:\:checkUser\(\) has parameter \$args with generic class Doctrine\\Persistence\\Event\\LifecycleEventArgs but does not specify its types\: TObjectManager$#'
			identifier: missingType.generics
			count: 1
			path: src/EventListener/UserSubscriber.php

		-
			message: '#^Comparison operation "\<" between int\<80400, 80599\> and 70400 is always false\.$#'
			identifier: smaller.alwaysFalse
			count: 1
			path: src/Kernel.php

		-
			message: '#^Class App\\Repository\\Files\\DocumentDataRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Files/DocumentDataRepository.php

		-
			message: '#^Class App\\Repository\\Files\\DocumentDataRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Files/DocumentDataRepository.php

		-
			message: '#^Class App\\Repository\\Files\\DocumentDataRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Files/DocumentDataRepository.php

		-
			message: '#^Class App\\Repository\\Files\\DocumentDataRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Files/DocumentDataRepository.php

		-
			message: '#^Class App\\Repository\\Main\\ContractAreaValidityRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Class App\\Repository\\Main\\ContractAreaValidityRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Class App\\Repository\\Main\\ContractAreaValidityRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Class App\\Repository\\Main\\ContractAreaValidityRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractAreaValidityRepository\:\:getValidities\(\) has parameter \$contractArea with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractAreaValidityRepository\:\:getValidities\(\) has parameter \$week with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractAreaValidityRepository\:\:getValidity\(\) has parameter \$contractArea with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractAreaValidityRepository\:\:getValidity\(\) has parameter \$week with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractAreaValidityRepository\:\:getValidityCount\(\) has parameter \$contractArea with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractAreaValidityRepository\:\:getValidityCount\(\) has parameter \$week with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractAreaValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractRepository\:\:getContractByData\(\) has parameter \$collectingPlace with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractRepository\:\:getContractByData\(\) has parameter \$contractArea with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractRepository.php

		-
			message: '#^Method App\\Repository\\Main\\ContractRepository\:\:getContractByData\(\) has parameter \$systemProvider with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/ContractRepository.php

		-
			message: '#^Class App\\Repository\\Main\\DocumentRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/DocumentRepository.php

		-
			message: '#^Class App\\Repository\\Main\\DocumentRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/DocumentRepository.php

		-
			message: '#^Class App\\Repository\\Main\\DocumentTypeRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/DocumentTypeRepository.php

		-
			message: '#^Class App\\Repository\\Main\\DocumentTypeRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/DocumentTypeRepository.php

		-
			message: '#^Class App\\Repository\\Main\\DocumentTypeRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/DocumentTypeRepository.php

		-
			message: '#^Class App\\Repository\\Main\\DocumentTypeRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/DocumentTypeRepository.php

		-
			message: '#^Class App\\Repository\\Main\\FeatureRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/FeatureRepository.php

		-
			message: '#^Class App\\Repository\\Main\\FeatureRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/FeatureRepository.php

		-
			message: '#^Class App\\Repository\\Main\\FeatureRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/FeatureRepository.php

		-
			message: '#^Class App\\Repository\\Main\\FeatureRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/FeatureRepository.php

		-
			message: '#^Method App\\Repository\\Main\\OrderRepository\:\:findInBetween\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/OrderRepository.php

		-
			message: '#^Method App\\Repository\\Main\\OrderRepository\:\:findTransferOpenInBetween\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/OrderRepository.php

		-
			message: '#^Class App\\Repository\\Main\\StateRepository extends generic class Doctrine\\ORM\\EntityRepository but does not specify its types\: T$#'
			identifier: missingType.generics
			count: 1
			path: src/Repository/Main/StateRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointValidityRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointValidityRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointValidityRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UnloadingPointValidityRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\UnloadingPointValidityRepository\:\:getValidities\(\) has parameter \$unloadingPoint with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\UnloadingPointValidityRepository\:\:getValidities\(\) has parameter \$week with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\UnloadingPointValidityRepository\:\:getValidity\(\) has parameter \$unloadingPoint with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\UnloadingPointValidityRepository\:\:getValidity\(\) has parameter \$week with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\UnloadingPointValidityRepository\:\:getValidityCount\(\) has parameter \$unloadingPoint with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Method App\\Repository\\Main\\UnloadingPointValidityRepository\:\:getValidityCount\(\) has parameter \$week with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Repository/Main/UnloadingPointValidityRepository.php

		-
			message: '#^Access to an undefined property App\\Repository\\Main\\UserAccessRepository\:\:\$_em\.$#'
			identifier: property.notFound
			count: 4
			path: src/Repository/Main/UserAccessRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserAccessRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserAccessRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserAccessRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserAccessRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserAccessRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserAccessRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserAccessRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserAccessRepository.php

		-
			message: '#^Access to an undefined property App\\Repository\\Main\\UserRepository\:\:\$_em\.$#'
			identifier: property.notFound
			count: 2
			path: src/Repository/Main/UserRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserRepository has PHPDoc tag @method for method findBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserRepository has PHPDoc tag @method for method findBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#1 \$criteria with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserRepository.php

		-
			message: '#^Class App\\Repository\\Main\\UserRepository has PHPDoc tag @method for method findOneBy\(\) parameter \#2 \$orderBy with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Repository/Main/UserRepository.php

		-
			message: '#^Call to function is_null\(\) with App\\Entity\\Main\\CollectingPlace will always evaluate to false\.$#'
			identifier: function.impossibleType
			count: 2
			path: src/Services/AccessHelper.php

		-
			message: '#^Call to function is_null\(\) with App\\Entity\\Main\\ContractArea will always evaluate to false\.$#'
			identifier: function.impossibleType
			count: 1
			path: src/Services/AccessHelper.php

		-
			message: '#^Call to function is_null\(\) with App\\Entity\\Main\\User will always evaluate to false\.$#'
			identifier: function.impossibleType
			count: 1
			path: src/Services/AccessHelper.php

		-
			message: '#^Method App\\Services\\AccessHelper\:\:getCollectingPlaceList\(\) has parameter \$locked with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/AccessHelper.php

		-
			message: '#^Method App\\Services\\AccessHelper\:\:getCollectingPlaceList\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/AccessHelper.php

		-
			message: '#^Method App\\Services\\AccessHelper\:\:getContractAreaList\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/AccessHelper.php

		-
			message: '#^Right side of && is always true\.$#'
			identifier: booleanAnd.rightAlwaysTrue
			count: 1
			path: src/Services/AccessHelper.php

		-
			message: '#^Method App\\Services\\CalendarDay\:\:getValidityMessages\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CalendarDay.php

		-
			message: '#^Property App\\Services\\CalendarDay\:\:\$validityMessages type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CalendarDay.php

		-
			message: '#^Method App\\Services\\CalendarValidator\:\:__construct\(\) has parameter \$validities with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CalendarValidator.php

		-
			message: '#^Method App\\Services\\CalendarValidator\:\:getValidity\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Services/CalendarValidator.php

		-
			message: '#^Method App\\Services\\CalendarValidator\:\:initCancelledCollects\(\) has parameter \$cancelledCollects with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CalendarValidator.php

		-
			message: '#^Property App\\Services\\CalendarValidator\:\:\$cancelledCollects type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CalendarValidator.php

		-
			message: '#^Parameter \$value of function curl_setopt expects bool, int given\.$#'
			identifier: argument.type
			count: 1
			path: src/Services/CaptchaVerifier.php

		-
			message: '#^Method App\\Services\\CollectingPlaceHelper\:\:createPlace\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CollectingPlaceHelper.php

		-
			message: '#^Method App\\Services\\CollectingPlaceHelper\:\:getContractAreaList\(\) has parameter \$locked with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/CollectingPlaceHelper.php

		-
			message: '#^Method App\\Services\\CollectingPlaceHelper\:\:getContractAreaList\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CollectingPlaceHelper.php

		-
			message: '#^Method App\\Services\\CollectingPlaceHelper\:\:saveCollectingPlace\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/CollectingPlaceHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaHelper\:\:createContract\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractAreaHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaHelper\:\:getCollectingPlaceList\(\) has parameter \$locked with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/ContractAreaHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaHelper\:\:getCollectingPlaceList\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractAreaHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaHelper\:\:saveContract\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractAreaHelper.php

		-
			message: '#^Call to function is_null\(\) with App\\Entity\\Main\\ContractArea will always evaluate to false\.$#'
			identifier: function.impossibleType
			count: 1
			path: src/Services/ContractAreaValidityHelper.php

		-
			message: '#^Call to function is_null\(\) with App\\Entity\\Main\\ContractAreaValidity will always evaluate to false\.$#'
			identifier: function.impossibleType
			count: 1
			path: src/Services/ContractAreaValidityHelper.php

		-
			message: '#^Call to function is_null\(\) with Doctrine\\Common\\Collections\\Collection will always evaluate to false\.$#'
			identifier: function.impossibleType
			count: 1
			path: src/Services/ContractAreaValidityHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaValidityHelper\:\:createValidity\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractAreaValidityHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaValidityHelper\:\:isValidForDate\(\) has no return type specified\.$#'
			identifier: missingType.return
			count: 1
			path: src/Services/ContractAreaValidityHelper.php

		-
			message: '#^Method App\\Services\\ContractAreaValidityHelper\:\:saveValidity\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractAreaValidityHelper.php

		-
			message: '#^Method App\\Services\\ContractHelper\:\:createContract\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractHelper.php

		-
			message: '#^Method App\\Services\\ContractHelper\:\:saveContract\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/ContractHelper.php

		-
			message: '#^Method App\\Services\\DateTimeHelper\:\:getDatesFromRange\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/DateTimeHelper.php

		-
			message: '#^Method App\\Services\\DateTimeHelper\:\:getInfoStringsWeek\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/DateTimeHelper.php

		-
			message: '#^Method App\\Services\\DateTimeHelper\:\:getWeekFromMonToSat\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/DateTimeHelper.php

		-
			message: '#^Call to an undefined method App\\Entity\\Main\\Document\:\:setWeighingNumber\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Services/DocumentHelper.php

		-
			message: '#^Method App\\Services\\DocumentHelper\:\:createDocument\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/DocumentHelper.php

		-
			message: '#^Method App\\Services\\DocumentHelper\:\:saveDocument\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/DocumentHelper.php

		-
			message: '#^Property App\\Services\\FileExtensionHelper\:\:\$extensionMapping type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/FileExtensionHelper.php

		-
			message: '#^Method App\\Services\\GermanyHolidayResolver\:\:resolveHoliday\(\) has parameter \$states with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/GermanyHolidayResolver.php

		-
			message: '#^Method App\\Services\\GermanyHolidayResolver\:\:resolveHoliday\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/GermanyHolidayResolver.php

		-
			message: '#^Method App\\Services\\HolidayResolverInterface\:\:resolveHoliday\(\) has parameter \$states with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/HolidayResolverInterface.php

		-
			message: '#^Method App\\Services\\HolidayResolverInterface\:\:resolveHoliday\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/HolidayResolverInterface.php

		-
			message: '#^Method App\\Services\\Mailer\:\:__construct\(\) has parameter \$emailManager with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:__construct\(\) has parameter \$emailOrder with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:__construct\(\) has parameter \$emailQuestion with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:__construct\(\) has parameter \$emailStorno with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendContactMessage\(\) has parameter \$data with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendErrorMessages\(\) has parameter \$errors with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendMail\(\) has parameter \$data with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendMail\(\) has parameter \$emailsTo with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendOrderList\(\) has parameter \$dateFrom with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendOrderList\(\) has parameter \$dateTo with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendOrderList\(\) has parameter \$orderList with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendOrderList\(\) has parameter \$weekStart with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\Mailer\:\:sendPasswordReset\(\) has parameter \$newUser with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^PHPDoc tag @param for parameter \$emailManager with type string is incompatible with native type array\.$#'
			identifier: parameter.phpDocType
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^PHPDoc type for property App\\Services\\Mailer\:\:\$emailManager with type string is incompatible with native type array\.$#'
			identifier: property.phpDocType
			count: 1
			path: src/Services/Mailer.php

		-
			message: '#^Method App\\Services\\OrderCollectCalculator\:\:getCancelledCollectOrders\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/OrderCollectCalculator.php

		-
			message: '#^Method App\\Services\\OrderCollectCalculator\:\:getCollectOrders\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/OrderCollectCalculator.php

		-
			message: '#^Method App\\Services\\OrderCollectCalculator\:\:getMaximumOrderCount\(\) has parameter \$week with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/OrderCollectCalculator.php

		-
			message: '#^Call to an undefined method Symfony\\Component\\Security\\Core\\User\\UserInterface\:\:getUuid\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: src/Services/OrderHelper.php

		-
			message: '#^Method App\\Services\\OrderHelper\:\:createOrder\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/OrderHelper.php

		-
			message: '#^Method App\\Services\\OrderHelper\:\:saveOrder\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/OrderHelper.php

		-
			message: '#^Method App\\Services\\OrderHelper\:\:send\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/OrderHelper.php

		-
			message: '#^Method App\\Services\\PublicHolidayHelper\:\:__construct\(\) has parameter \$resolvers with no value type specified in iterable type iterable\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/PublicHolidayHelper.php

		-
			message: '#^Method App\\Services\\PublicHolidayHelper\:\:checkHolidayDay\(\) has parameter \$states with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/PublicHolidayHelper.php

		-
			message: '#^Method App\\Services\\PublicHolidayHelper\:\:checkHolidayDay\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/PublicHolidayHelper.php

		-
			message: '#^Method App\\Services\\PublicHolidayHelper\:\:findAllPublicHolidayInBetween\(\) has parameter \$states with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/PublicHolidayHelper.php

		-
			message: '#^Method App\\Services\\PublicHolidayHelper\:\:findAllPublicHolidayInBetween\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/PublicHolidayHelper.php

		-
			message: '#^Method App\\Services\\SecurityHelper\:\:getRoleHierarchy\(\) has parameter \$user with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/SecurityHelper.php

		-
			message: '#^Method App\\Services\\SecurityHelper\:\:getRoleHierarchy\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/SecurityHelper.php

		-
			message: '#^Method App\\Services\\SecurityHelper\:\:getTranslatedRoleHierarchy\(\) has parameter \$user with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: src/Services/SecurityHelper.php

		-
			message: '#^Method App\\Services\\SystemProviderHelper\:\:createProvider\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/SystemProviderHelper.php

		-
			message: '#^Method App\\Services\\SystemProviderHelper\:\:saveProvider\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/SystemProviderHelper.php

		-
			message: '#^Method App\\Services\\UnloadingPointHelper\:\:createPlace\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/UnloadingPointHelper.php

		-
			message: '#^Method App\\Services\\UnloadingPointHelper\:\:saveUnloadingPoint\(\) has parameter \$values with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/UnloadingPointHelper.php

		-
			message: '#^Class App\\Services\\WeekDayIterator implements generic interface Iterator but does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: src/Services/WeekDayIterator.php

		-
			message: '#^Property App\\Services\\WeekDayIterator\:\:\$array type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: src/Services/WeekDayIterator.php
