set -o allexport # export all defined variables

composer install -n

php bin/console cache:clear -e "dev" --no-debug
php bin/console doctrine:database:drop -e "dev" --force --no-debug --connection=default
php bin/console doctrine:database:create -e "dev" --no-debug --connection=default
php bin/console doctrine:migration:migrate -e "dev" -n --no-debug --em=default
php bin/console doctrine:schema:update --force --em=default

php bin/console doctrine:database:drop -e "dev" --force --no-debug --connection=files
php bin/console doctrine:database:create -e "dev" --no-debug --connection=files
php bin/console doctrine:migration:migrate -e "dev" -n --no-debug --em=files
php bin/console doctrine:schema:update --force --em=files

php bin/console doctrine:fixtures:load -e "dev" -n --no-debug --em=default