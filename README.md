# LVP Portal
LVP Portal symfony application

## Initial Setup
1. Clone the repo
    ```sh
    <NAME_EMAIL>:prezero/lvpportal-sf.git
    ```
2. Open the project in any IDE of your choice.
3. This project makes usage of private PreZero GitHub npm package registries and various composer bundles.
   For this purpose a GitHub personal access token (PAT) with ``full repo scopes`` and additional ``read:packages`` scope is needed,
   see this link for generating a new token: [Creating a personal access token](https://docs.github.com/en/enterprise-cloud@latest/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token)
4. Copy the ``.env`` and rename it to ``.env.local``
5. Add your PAT to the key ``PREZERO_NPM_TOKEN`` in ``.env.local`` file.
6. In project root, create a copy of ``auth.json.dist`` file and rename it to ``auth.json``. Then in the file replace the placeholder ```<YOURTOKEN>``` with your PAT.
7. Now it is time to configure your Git username and email address.
   Git associates your identity with every commit you make. Run the following command to configure your git credentials in the project directory.
    ```shell
   git config user.name "Your Name" 
   git config user.email "<EMAIL>" 
    ```
   You can also save your information globally (not only in Project level) by passing the --global option.
    ```shell
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
    ```
   You can view all of your settings and where they are coming with the command:
    ```shell
   git config --list
    ```
8. Login to docker with the personal access token as the password.
   **Read this again !! Please use the token as your password and not your plain password which is normally used in the website.**
   **Only after the login is successful, continue to the next step.**
   ```shell
   make docker-login-credentials
    ```
   (Hint: docker stores credentials in its own keystore locally. To logout run docker logout ghcr.io)
9. Now set up the project containers with the following command:
    ```shell
   make setup-containers
   ```
10. After the project containers are up and running, install composer packages and dependencies using the command below: **Please read the note as well**
    ```shell
    make setup-composer
    ```
    **Note: During the documentation process, the 'make setup-composer' step occasionally caused significant issues when
    installing composer packages. However, re-running this step resolved the problem. If you encounter an error while executing
    this step, simply run it again to fix the issue.**
11. After the project containers are up and running, provision the database using the following command:
    ```shell
    make setup-data
    ```
12. For installing fixtures, just log in the Backend container and run bin/console doctrine:fixtures:load
```shell
    make docker-ssh
```
Inside the container terminal run
```shell
    bin/console doctrine:fixtures:load
```
13. Application should be accessible on host machine port 8000 (http://localhost:8000)
13. Login to application with predefined user ```<EMAIL>```, Password ```$78Nine.```

## Start project
If it's your first time working on the project, you will need to do the initial setup first: [Initial setup](#initial-setup).

* Else if you want to start the project after you stopped the container you need to execute:
  ```sh
    make start
  ```
* **(OPTIONAL) Provision project database and fixtures:**
  ```sh
    make setup-data
  ```
If you want to load the database from live environments (Dev/QS/Prod), please refer to the Confluence documentation at
[TODO](http://localhost:8080).
Ensure you have the necessary StackIT Infrastructure access to download the database dumps.

## Setup development environment in VS Code
1. Follow step 1 to 7 of [Initial setup](#initial-setup)
2. Install ```ms-vscode-remote.remote-containers``` extension in VS Code
3. Open/Reopen folder in container with the extension in the step before
4. Install composer packages:
    ```sh
    make composer-install
    ```
5. Setup data:
    ```sh
    make setup-data
    ```

## Simulating prod in local development environment
1. To simulate PROD in the local development environment:
- Navigate to the Project directory.
- Open the .env file.
- Locate the APP_ENV variable.
- Change its value from "dev" to "prod".
2. After making these changes:
- Remove the containers by running the following command (in project root)
    ```sh
    make docker-down
    ```
- Restart them to apply the updated environment settings.
    ```sh
    make start && make setup-data
    ```
