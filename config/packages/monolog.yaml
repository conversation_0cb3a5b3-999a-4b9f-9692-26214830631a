monolog:
  channels:
    - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists

  handlers:
    main:
      type: stream
      path: "php://stdout"
      level: '%env(STDOUT_LOG_LEVEL)%'
      channels: [ "!event", ]
      formatter: app.monolog.json_formatter
    console:
      type: console
      process_psr_3_messages: false
      channels: [ "!event", "!doctrine", "!console" ]

services:
  app.monolog.json_formatter:
    class: Monolog\Formatter\JsonFormatter
    autoconfigure: true
    autowire: true
    calls:
      - setMaxNormalizeDepth: [15]
