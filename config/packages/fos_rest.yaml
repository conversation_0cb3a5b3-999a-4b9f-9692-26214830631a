# Read the documentation: https://symfony.com/doc/master/bundles/FOSRestBundle/index.html
fos_rest:
    body_listener: true
    versioning: true
#    param_fetcher_listener:  true
#    allowed_methods_listener:  true
#    routing_loader: false
#    view:
#        view_response_listener:  true
#    exception:
#        codes:
#            App\Exception\MyException: 403
#        messages:
#            App\Exception\MyException: Forbidden area.
#    format_listener:
#        rules:
#            - { path: ^/api, prefer_extension: true, fallback_format: json, priorities: [ json, html ] }
