# config/packages/doctrine.yaml
doctrine:
  dbal:
    default_connection: default
    connections:
      default:
        url: '%env(resolve:DATABASE_URL)%'
        server_version: '17'
        logging: true
        profiling_collect_backtrace: '%kernel.debug%'
        use_savepoints: true
      files:
        url: '%env(resolve:DATABASE_URL_FILES)%'
        server_version: 'mariadb-10.9.3'
    types:
      uuid: Ramsey\Uuid\Doctrine\UuidType
  orm:
    default_entity_manager: default
    entity_managers:
      default:
        connection: default
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        mappings:
          AppMain:
            is_bundle: false
            type: attribute
            dir: '%kernel.project_dir%/src/Entity/Main'
            prefix: 'App\Entity\Main'
            alias: AppMain
      files:
        connection: files
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        mappings:
          AppFiles:
            is_bundle: false
            type: attribute
            dir: '%kernel.project_dir%/src/Entity/Files'
            prefix: 'App\Entity\Files'
            alias: AppFiles

when@test:
  doctrine:
    dbal:
      dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
  doctrine:
    orm:
      auto_generate_proxy_classes: false
      proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
      query_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      result_cache_driver:
        type: pool
        pool: doctrine.result_cache_pool

  framework:
    cache:
      pools:
        doctrine.result_cache_pool:
          adapter: cache.app
        doctrine.system_cache_pool:
          adapter: cache.system
