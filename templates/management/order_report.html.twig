{% extends('base.html.twig') %}
{% block title %}{{'order.report.title'|trans}}{% endblock %}
{% block body %}
    <main>
        <div class="container">
            <div class="row">
                <div class="col-sm-12 d-flex justify-content-center">
                    <h1 class="mb-5">{{'order.report.evaluation'|trans}} ({{ pagination.getTotalItemCount }})</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <aside>
                        {{ form_start(form) }}

                            <div class="form-group">
                                <label for="anfallstelle">{{'order.report.unloading_point'|trans}}</label>
                                {{ form_widget(form.unloadingPoint, { 'attr': { 'class':'form-control'}}) }}
                            </div>

                            <div class="form-group">
                                <label for="datumvon">{{'order.report.date_from'|trans}}</label>
                                {{ form_widget(form.dateFrom, { 'attr': { 'class':'form-control'}}) }}
                            </div>

                            <div class="form-group">
                                <label for="datumbis">{{'order.report.date_to'|trans}}</label>
                                {{ form_widget(form.dateTo, { 'attr': { 'class':'form-control'}}) }}
                            </div>

                            <div class="form-group">
                                <label for="submit">&nbsp;</label>
                                {{ form_widget(form.submit, { 'attr': { 'class':'form-control btn-secondary btn-center btn-text-only'}} ) }}
                            </div>

                        {{ form_end(form) }}

                    </aside>
                </div>
                <div class="col-md-9">
                    <div class="actions clearfix mb-5">

                        {{ form_start(exportForm, { 'attr': { 'id':'export-form' }}) }}

                            {{ form_widget(exportForm.dateFrom) }}

                            {{ form_widget(exportForm.dateTo) }}

                            {{ form_widget(exportForm.unloadingPoint) }}

                            <a id="export-submit-button" href="#" class="btn-prime btn-icon float-right m-0"><i class="fas fa-download"></i> {{'order.report.export'|trans}}</a>

                        {{ form_end(exportForm) }}

                    </div>

                    <div class="table-responsive">
                        <table id="order-overview" class="table table-striped">
                            <thead>
                            <tr>
                                <th><a href="{{ path('management_order_report', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'date', order: sortOrder.date })) }}"
                                    >{% if sortOrder.date == 'ASC' %}
                                        <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.date == 'DESC' %}
                                        <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'order.date'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'status', order: sortOrder.status })) }}"
                                    >{% if sortOrder.status == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.status == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'order.status'|trans }}</a></th>
                                <th><a href="{{ path('management_order_report', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'contractArea', order: sortOrder.contractArea })) }}"
                                    >{% if sortOrder.contractArea == 'ASC' %}
                                        <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.contractArea == 'DESC' %}
                                        <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'collectingplace.contract_area'|trans }}</a>
                                </th>
                                <th><a href="{{ path('management_order_report', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'ul_dsdid', order: sortOrder.ul_dsdid })) }}"
                                    >{% if sortOrder.ul_dsdid == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                        {% elseif sortOrder.ul_dsdid == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                        {% endif %} {{ 'order.report.unloading_point'|trans }}</a>
                                </th>
                                <th><a href="{{ path('management_order_report', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'cp_dsdid', order: sortOrder.cp_dsdid })) }}"
                                    >{% if sortOrder.cp_dsdid == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                        {% elseif sortOrder.cp_dsdid == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                        {% endif %} {{ 'collectingplace.collecting_place'|trans }}</a>
                                </th>
                                <th><a href="{{ path('management_order_report', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'providerName', order: sortOrder.providerName })) }}"
                                    >{% if sortOrder.providerName == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                        {% elseif sortOrder.providerName == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                        {% endif %} {{ 'System deliverer'|trans }}</a>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for order in pagination %} {# orderList #}
                                <tr>
                                    <td>{{ order.date|date('d.m.Y') }}</td>
                                    <td>{{ order.canceled ? 'Storniert' : (( order.transferStatus is not null and order.transferStatus != 'S' ) ? 'Fehler bei der Übertragung' : ( order.transfered ? 'Übertragen' : 'Erfasst' )) }}</td>
                                    <td>{{ order.contractArea }}</td>
                                    <td>{{ order.ul_dsdid }} -
                                        {{ order.ul_name1 }} {{ order.ul_name2 }} <br>
                                        {{ order.ul_street }}
                                        {{ order.ul_houseNumber }},
                                        {{ order.ul_postalCode }}
                                        {{ order.ul_city }} </td>
                                    <td>{{ order.cp_dsdid }} -
                                        {{ order.cp_name1 }} {{ order.cp_name2 }} <br>
                                        {{ order.cp_street }}
                                        {{ order.cp_houseNumber }},
                                        {{ order.cp_postalCode }}
                                        {{ order.cp_city }} </td>
                                    <td>{{ order.providerName }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-center">{{ knp_pagination_render(pagination) }}</div>
                </div>
            </div>
        </div>
    </main>

{% endblock %}
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('orderoverview') }}
{% endblock %}
