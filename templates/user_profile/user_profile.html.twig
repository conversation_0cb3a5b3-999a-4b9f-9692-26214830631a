{% extends('base.html.twig') %}
{% block title %}{{ 'user.profile_header'|trans }}{% endblock %}
{% block body %}
    <main>
        <div class="container">
            <div class="row">
                <div class="col-sm-12 d-flex justify-content-center">
                    <h1 class="mb-5">{{ 'user.profile_title'|trans }}</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <aside>
                        <ul>
                            {#<li><a href="user-profile.html"><i class="fas fa-address-book"></i> Stammdaten</a></li>#}
                            <li {% if 'password'== profile %}class="active"{% endif %}>
                                <a href="{{ path('app_userprofile_userprofilenewpassword') }}"><i class="fas fa-lock"></i> {{ 'user.change_password'|trans }}</a>
                            </li>
                            <li {% if 'email'== profile %}class="active"{% endif %}>
                                <a href="{{ path('app_userprofile_userprofilechangemail') }}"><i class="fas fa-envelope"></i> {{ 'user.change_email'|trans }}</a>
                            </li>
                            {% if language_switch_enabled %}
                                <li {% if 'locale'== profile %}class="active"{% endif %}>
                                    <a href="{{ path('app_userprofile_userprofilechangelocale') }}">
                                        <i class="fas fa-globe"></i>
                                        {{ 'user.change_locale'|trans }}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </aside>
                </div>

                <div class="col-md-9">
                    {% block main_content %}{% endblock %}
                </div>
            </div>

        </div>
    </main>
{% endblock %}
