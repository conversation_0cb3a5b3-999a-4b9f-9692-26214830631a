{% extends('user_profile/user_profile.html.twig') %}
{% block main_content %}
<div class="row">
    <div class="box-rounded w-98">
        <h2 class="text-uppercase">{{ 'user.change_password'|trans }}</h2>
        <p>{{ 'user.change_password_text'|trans }}</p>

        {{ form_start(form) }}
            <div class="form-group">
                {#<button type="submit" class="btn-prime float-right d-none d-sm-inline-block">Okay</button>#}
                {{ form_widget(form.submit, { 'attr': { 'class':'btn-prime float-left d-none d-sm-inline-block'}}) }}
            </div>
        {{ form_end(form) }}
    </div>
</div>
{% endblock %}
