{% extends('user_profile/user_profile.html.twig') %}
{% block main_content %}
<div class="row">
    <div class="box-rounded w-98">
        <h2 class="text-uppercase">{{ 'user.change_email'|trans }}</h2>
        <p>{{ 'user.change_email_text'|trans }}</p>

        {{ form_start(form) }}

            {% if form.vars.errors|length %}
            <div class="mb-3 alert alert-danger">
                {% for error in form.vars.errors %}
                    {{ error.messageTemplate|trans }}
                {% endfor %}
            </div>
            {% endif %}

            <div class="form-group">
                <label for="email">{{ 'user.new_email'|trans }}</label>
                {{ form_widget(form.new_email, { 'attr': { 'class':'form-control'}}) }}
            </div>

            <div class="form-group">
                <label for="password">{{ 'user.current_password'|trans }}</label>
                {{ form_widget(form.current_password, { 'attr': { 'class':'form-control'}}) }}
            </div>

            <div class="form-group">
                {{ form_widget(form.submit, { 'attr': { 'class':'btn-prime float-left d-none d-sm-inline-block'}}) }}
            </div>

        {{ form_end(form) }}
    </div>
</div>
{% endblock %}
