{# @var ea \EasyCorp\Bundle\EasyAdminBundle\Context\AdminContext #}
{% extends '@EasyAdmin/page/content.html.twig' %}

{% block page_title 'overview'|trans %}

{% block page_content %}

    <p>{{ 'dashboard.overview_text'|trans|raw }}</p>

    <br><br>
    <p>
        <strong>{{ 'data'|trans }}</strong>
    </p>

    <p>
        {{ 'dashboard.data_text'|trans|raw }}
        <br><br>
        <u>{{ 'customer'|trans }}</u><br>
        {{ 'dashboard.customer_text'|trans }}
        <br><br>

        <u>{{ 'collectingplace'|trans }}</u><br>
        {{ 'dashboard.collecting_place_text'|trans|raw }}
        <br>
        <br>

        <u>{{ 'users'|trans }}</u><br>
        {{ 'dashboard.user_text'|trans|raw }}
    <ul>
        <li>{{ 'customer'|trans }}</li>
        <li>{{ 'manager'|trans }}</li>
    </ul>
    {{ 'dashboard.user_roles_text'|trans|raw }}
    <br><br>
    {{ 'dashboard.user_reset_password_text'|trans|raw }}
    </p>



    <br><br>
    <p>
        <strong>{{ 'orders'|trans }}</strong>
    </p>

    <p>
        {{ 'dashboard.order_text'|trans|raw }}
        <br><br>

        <u>{{ 'orders'|trans }}</u><br>
        {{ 'dashboard.order_status_text'|trans|raw }}
    </p>



    <br><br>
    <p>
        <strong>{{ 'interface'|trans }}</strong>
    </p>

    <p>
        {{ 'dashboard.interface_text'|trans|raw }}
        <br><br>

        <u>{{ 'management.contract_areas'|trans }}</u><br>
        {{ 'dashboard.contract_areas_text'|trans }}
        <br><br>

        <u>{{ 'contracts'|trans }}</u><br>
        {{ 'dashboard.contracts_text'|trans|raw }}
        <br><br>

        <u>{{ 'unloading_points'|trans }}</u><br>
        {{ 'dashboard.unloading_points_text'|trans }}
        <br><br>

        <u>{{ 'system_provider'|trans }}</u><br>
        {{ 'dashboard.system_provider_text'|trans }}

    </p>



    <br><br>
    <p>
        <strong>{{ 'system_data'|trans }}</strong>
    </p>

    <p>
        {{ 'dashboard.system_data_text'|trans|raw }}
        <br><br>

        <u>{{ 'states'|trans }}</u><br>
        {{ 'dashboard.states_text'|trans }}
    </p>


    <br><br>
    <p>
        <strong>{{ 'customer_view'|trans }}</strong>
    </p>

    <p>
        {{ 'dashboard.customer_view_text'|trans }}
    </p>

{% endblock %}
