{% extends('base.html.twig') %}
{% block title %}{{ 'security.new_password_header'|trans }}{% endblock %}
{% block body %}
  <main class="background-blue background-person">
    <div class="container">
      <div class="row">
        <div class="col-sm-12 d-flex justify-content-center">
          <h1 class="text-white mb-5">{{ 'index.login_password_reset'|trans }}</h1>
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12 d-flex justify-content-center">
          <div class="box">
            <h2 class="text-center mb-4">{{ 'security.new_password_input_text'|trans }}</h2>
            {{ form_start(form) }}

            {% if errorMessage is defined %}
              <div class="mb-3 alert alert-danger">
                {{ errorMessage|trans }}
              </div>
            {% endif %}

            <div class="form-group">
              <label for="email">{{ 'security.new_password_lable'|trans }}</label>
              {{ form_widget(form.password_1, { 'attr': { 'class':'form-control', 'pattern':'(?=^.{8,}$)((?=.*\\d)|(?=.*[@$.+_!#%&]))(?![.\\n])(?=.*[A-Z])(?=.*[a-z]).*$' } }) }}
              {{ form_errors(form.password_1, { 'attr': { 'class':'form-control alert alert-danger' } }) }}
            </div>

            <div class="form-group">
              <label for="email">{{ 'security.new_password_second_input'|trans }}</label>
              {{ form_widget(form.password_2, { 'attr': { 'class':'form-control', 'pattern':'(?=^.{8,}$)((?=.*\\d)|(?=.*[@$.+_!#%&]))(?![.\\n])(?=.*[A-Z])(?=.*[a-z]).*$' } }) }}
              {{ form_errors(form.password_2, { 'attr': { 'class':'form-control alert alert-danger' } }) }}
            </div>

            {{ form_widget(form.submit, { 'attr': { 'class':'btn-prime btn-center' } }) }}

            {{ form_end(form) }}
          </div>
        </div>
      </div>
    </div>
  </main>
{% endblock %}