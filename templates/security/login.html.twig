{% extends('base.html.twig') %}
{% block title %}{{ 'index.login_header'|trans }}{% endblock %}
{% block body %}
    <main class="background-blue background-person">
        <div class="container">
            <div class="row">
                <div class="col-sm-12 d-flex justify-content-center">
                    <h1 class="text-white mb-5">{{ 'index.login'|trans }}</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12 d-flex justify-content-center">
                    <div class="box">
                        <h2 class="text-center mb-4">{{ 'index.login_text'|trans }}</h2>
                        <form method="post">
                            {% if error %}
                                <div class="alert alert-danger">{{ error.messageKey|trans(error.messageData, 'security') }}</div>
                            {% endif %}

                            {% if app.user %}
                                <div class="mb-3">
                                    {% trans %}You are logged in as{% endtrans %} {{ app.user.username }}, <a href="{{ path('app_logout') }}">{{ 'logout'|trans }}</a>
                                </div>
                            {% endif %}

                            <div class="form-group">
                                <label for="email">{{ 'index.login_email'|trans }}</label>
                                <input type="email" class="form-control" value="{{ last_username }}" name="email" id="inputEmail"  required autofocus>
                            </div>
                            <div class="form-group">
                                <label for="password">{{ 'index.login_password'|trans }}</label>
                                <!--<input type="password" class="form-control" id="password">-->
                                <input type="password" name="password" id="inputPassword" class="form-control" required>
                            </div>
                            <input type="hidden" name="_csrf_token"
                                   value="{{ csrf_token('authenticate') }}"
                            >
                            <div class="form-group">
                                <p><a href="{{ path('app_security_passwordreset') }}">{{ 'index.login_password_reset'|trans }}</a></p>
                            </div>
                            <button type="submit" class="btn-prime btn-center">{{ 'index.login_submit'|trans }}</button>
                        </form>

                        <hr class="my-4">
                        <div style="color: #8bba13">
                            <h5 class="font-thin text-base green">{{ 'index.support'|trans }}</h5>
                        </div>
                        <p class="font-thin"><a href="{{ path('app_contact_contact') }}" class="">{{ 'index.to_the_contact_form'|trans }}</a></p>
                    </div>
                </div>
            </div>
        </div>
    </main>
{% endblock %}
