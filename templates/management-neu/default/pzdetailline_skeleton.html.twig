<div class="flex justify-start px-5 mb-5 font-extralight space-x-10">
    {% if block('item1') is defined and block('item2') is defined and block('item3') is defined %}
        <div class="{{width1|default('w-1/2')}} {{border|default('border-b-2')}} ">
            {{ block('item1')|raw }}
        </div>
        <div class="{{width2|default('w-1/2')}} {{border|default('border-b-2')}} ">
            {{ block('item2')|raw }}
        </div>
        <div class="{{width2|default('w-1/2')}} {{border|default('border-b-2')}} ">
            {{ block('item3')|raw }}
        </div>
    {% elseif block('item1') is defined and block('item2') is defined %}
        <div class="{{width1|default('w-1/2')}} {{border|default('border-b-2')}} ">
            {{ block('item1')|raw }}
        </div>
        <div class="{{width2|default('w-1/2')}} {{border|default('border-b-2')}} ">
            {{ block('item2')|raw }}
        </div>
    {% elseif (block('item1') is defined) %}
        <div class="{{width1|default('w-1/2')}} {{border|default('border-b-2')}} ">
            {{ block('item1')|raw }}
        </div>
        <div class="{{width2|default('w-1/2')}}"></div>
    {% elseif (block('item') is defined) %}
        <div class="w-full {{border|default('border-b-2')}} ">
            {{ block('item')|raw }}
        </div>
    {% endif %}
</div>
