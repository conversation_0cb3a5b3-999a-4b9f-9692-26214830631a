{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-user-details') }}
{% endblock %}

{% block popup %}
    {{  include('management-neu/user/edit_popup.html.twig', { title: 'user.details.edit'|trans, editForm: editForm }) }}
    {{  include('management-neu/default/pzdeletepopup.html.twig', { title: 'user.details.delete', id: 'delete_user_popup', message: 'user.message.delete', deleteForm: deleteForm }) }}
    {{  include('management-neu/default/pzapplypopup.html.twig', { title: 'user.details.lock', id: 'lock_user_popup', message: 'user.message.lock', icon: 'fa-lock', action: 'popup.lock', color: 'warning', form: lockForm }) }}
    {{  include('management-neu/default/pzapplypopup.html.twig', { title: 'user.details.unlock', id: 'unlock_user_popup', message: 'user.message.unlock', icon: 'fa-lock-open', action: 'popup.unlock', color: 'warning', form: lockForm }) }}
    {{  include('management-neu/default/pzapplypopup.html.twig', { title: 'user.details.reset', id: 'reset_user_popup', message: 'user.message.reset', icon: 'fa-key', action: 'popup.reset', form: resetForm }) }}
{% endblock %}

{% block body %}
    <section class="flex items-center container mx-auto px-4 mt-2 mb-10">
        <i class="fas fa-angle-left mr-2 text-xl text-pzgreen"></i>
        <a href="{{ path('management_user') }}" class="font-extralight text-pzgreen">{{ 'return'|trans }}</a>
    </section>

    <div class="container mx-auto px-4">
        {% embed 'management-neu/default/pzdetails_skeleton.html.twig' with { title: 'user.details'|trans } %}
            {% block actions %}
                {% if is_granted('ROLE_MANAGER') %}
                    {{ include('management-neu/default/pzbutton.html.twig', { id: 'edit_user_button', icon: 'fa-edit', title: 'user.details.edit'|trans })}}
                    {{ include('management-neu/default/pzbutton.html.twig', { id: 'reset_user_button', icon: 'fa-key', title: 'user.details.reset'|trans })}}

                    {% if item.locked %}
                        {{ include('management-neu/default/pzbutton.html.twig', { id: 'unlock_user_button', background: 'warning', icon: 'fa-lock-open', title: 'user.details.unlock'|trans })}}
                    {% else %}
                        {{ include('management-neu/default/pzbutton.html.twig', { id: 'lock_user_button', background: 'warning', icon: 'fa-lock', title: 'user.details.lock'|trans })}}
                    {% endif %}

                    {{ include('management-neu/default/pzbutton.html.twig', { id: 'delete_user_button', icon: 'fa-trash-alt', background: 'red', title: 'user.details.delete'|trans })}}
                {% endif %}
            {% endblock %}
            {% block content %}
                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'user.field.email'|trans, value: item.email })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailcheckboxlist.html.twig', { title: 'user.field.roles'|trans, value: item.reachableRoles })}}
                            {% endblock %}
                            {% block item3 %}
                                {% if language_switch_enabled %}
                                    {% set localeKey = item.locale is defined and item.locale ? 'locale.'~item.locale : '' %}
                                    {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'locale'|trans, value: localeKey|trans }) }}
                                {% endif %}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}
            {% endblock %}
        {% endembed %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-user-details') }}
{% endblock %}

