{% embed "management-neu/default/pzdialog.html.twig"
    with {
    'dialog_id': 'edit_user_popup',
    'title': title
} %}
    {% block content %}
        {{ form_start(editForm) }}
        <div class="px-10">
            <div class="flex justify-start mb-5 font-extralight gap-10">
                <div class="w-1/2">
                    {{ form_label(editForm.email) }}
                    {{ form_widget(editForm.email, { 'attr': { 'class':'w-full h-12 block rounded border-2 border-pzgrey max-w-full placeholder-pzgrey px-4 py-3'}}) }}
                </div>
                {% if language_switch_enabled %}
                <div class="w-1/2">
                    {{ form_label(editForm.locale) }}
                    {{ form_widget(editForm.locale, { 'attr': { 'class':'w-full h-12 block rounded border-2 border-pzgrey max-w-full px-4 py-3'}}) }}
                </div>
                {% endif %}
            </div>
            <div class="flex justify-start mb-5 font-extralight gap-10">
                <div class="w-1/2">
                    {{ form_label(editForm.roles) }}
                    {{ form_widget(editForm.roles) }}
                </div>
            </div>
            <div class="flex justify-end border-t-2 py-4 gap-5">
                <button type="button" class="pzdialog-close-button inline-block border-2 border-pzgrey rounded-md px-4 py-3 text-pzgrey font-normal text-xl hover:shadow-md"><i class="fas fa-times mr-2 text-pzgrey"></i>{{ 'cancel'|trans }}</button>
                <button type="submit" class="inline-block bg-pzgreen rounded-md px-4 py-3 text-white font-normal text-xl hover:shadow-md"><i class="fas fa-save mr-2 text-white"></i>{{ 'save'|trans }}</button>
            </div>
        </div>
        {{ form_end(editForm) }}
    {% endblock %}
{% endembed %}
