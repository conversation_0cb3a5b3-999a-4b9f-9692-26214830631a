{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-collectingplace-details') }}
{% endblock %}

{% block popup %}
    {{  include('management-neu/default/pzapplypopup.html.twig', { title: 'collectingplace.details.lock', id: 'lock_collectingplace_popup', message: 'collectingplace.message.lock', icon: 'fa-lock', action: 'popup.lock', color: 'warning', form: lockForm }) }}
    {{  include('management-neu/default/pzapplypopup.html.twig', { title: 'collectingplace.details.unlock', id: 'unlock_collectingplace_popup', message: 'collectingplace.message.unlock', icon: 'fa-lock-open', action: 'popup.unlock', color: 'warning', form: lockForm }) }}
{% endblock %}

{% block body %}
    <section class="flex items-center container mx-auto px-4 mt-2 mb-10">
        <i class="fas fa-angle-left mr-2 text-xl text-pzgreen"></i>
        <a href="{{ path('management_collectingplace') }}" class="font-extralight text-pzgreen">{{ 'return'|trans }}</a>
    </section>

    <div class="container mx-auto px-4">
        {% embed 'management-neu/default/pzdetails_skeleton.html.twig' with { title: 'collectingplace.details'|trans } %}
            {% block actions %}
                {% if is_granted('ROLE_MANAGER') %}
                    {% if item.locked %}
                        {{ include('management-neu/default/pzbutton.html.twig', { id: 'unlock_collectingplace_button', background: 'warning', icon: 'fa-lock-open', title: 'collectingplace.details.unlock'|trans })}}
                    {% else %}
                        {{ include('management-neu/default/pzbutton.html.twig', { id: 'lock_collectingplace_button', background: 'warning', icon: 'fa-lock', title: 'collectingplace.details.lock'|trans })}}
                    {% endif %}
                {% endif %}
            {% endblock %}
            {% block content %}
                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'collectingplace.field.dsdid'|trans, value: item.dsdId })}}
                            {% endblock %}
                            {% block item2 %}
                                {{ include('management-neu/default/pzdetailcheckboxlist.html.twig', { title: 'collectingplace.field.name'|trans, value: item.name ~ '('~item.city ~')' })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}
                {% embed 'management-neu/default/pzdetailsection_skeleton.html.twig' with { title: null } %}
                    {% block content %}
                        {% embed 'management-neu/default/pzdetailline_skeleton.html.twig' %}
                            {% block item1 %}
                                {{ include('management-neu/default/pzdetailvalue.html.twig', { title: 'collectingplace.field.esaid'|trans, value: item.esaId })}}
                            {% endblock %}
                        {% endembed %}
                    {% endblock %}
                {% endembed %}
            {% endblock %}
        {% endembed %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-collectingplace-details') }}
{% endblock %}

