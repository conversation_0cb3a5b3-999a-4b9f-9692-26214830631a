{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-collectingplace-list') }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            {{ 'management.collecting_places_management'|trans }}
        </h1>

        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="flex justify-between items-center bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light px-10 py-5 shadow-lg rounded-t-xl mb-5 font-extralight">
                <h3 class="text-white font-thin text-xl">{{ 'management.collecting_places'|trans }}</h3>
            </div>
            <div class="px-10 mb-5">
                <h3 class="text-xl font-extralight mb-2">{{ 'management.collecting_places_overview'|trans }}</h3>
                <p class="font-thin"></p>
            </div>

            <div class="px-10 mb-5">
                <div class="flex flex-col lg:flex-row justify-between mb-5">
                    <div class="mb-5"></div>
                </div>

                <div id="filters-table-collectingplace-list"></div>
                <table id="collectingplace-list" class="blackgrid theme-prezero"
                       data-source='{{ list|escape('html_attr') }}'
                       data-columns="{{ columns|json_encode|escape('html_attr') }}">
                </table>
            </div>
        </div>
    </section>

    <div class="hidden inline-grid grid grid-cols-2 items-center">
        <input type="checkbox" class="hidden border-2 border-pzgrey rounded mr-2 justify-self-end">
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-collectingplace-list') }}
{% endblock %}
