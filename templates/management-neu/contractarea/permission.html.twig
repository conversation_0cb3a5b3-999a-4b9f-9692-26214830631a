{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-contractarea-permission') }}
{% endblock %}

{% block popup %}
    {{  include('management-neu/user/edit_popup.html.twig', { title: 'user.details.add'|trans }) }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            Vertragsgebietsverwaltung
        </h1>

        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light px-10 py-5 shadow-lg rounded-t-xl mb-5 font-extralight">
                <h3 class="text-pzgreen font-thin text-xl">{{'collectingplace.assign_user'|trans}}</h3>
                <p class="text-white font-thin">{{'collectingplace.contract_area'|trans}} {{ contractArea.name }}</p>
            </div>

            {% if 'users' in app.request.get('_route') %}
            <div class="px-10">
                <div>
                    <div class="hidden sm:block">
                        <div class="border-b border-pzgrey-border mb-10">
                            <nav class="-mb-px flex" aria-label="Tabs">
                                <a href="{{ path('management_contractArea_permission_users', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                    {{'user'|trans}}
                                </a>
                                <a href="{{ path('management_contractArea_permission_collectingplaces', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                    {{'collectingplace.collecting_places'|trans}}
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col lg:flex-row justify-between mb-5">
                    <div class="mb-5">
                        <h3 class="text-xl font-extralight mb-2">{{'user'|trans}}</h3>
                        <p class="font-thin">{{'collectingplace.user_select_text'|trans}}</p>
                    </div>
                    <div>
                        <button id="add_user_button" class="inline-block bg-pzgreen rounded-md px-4 py-3 text-white font-normal text-lg hover:shadow-md"><i class="fas fa-user mr-2 text-white"></i>{{'user.details.add'|trans}}</button>
                    </div>
                </div>
            </div>

            <div class="px-10 mb-5">
                <div id="filters-table-contractArea-list"></div>
                <table id="contractArea-list" class="blackgrid-users theme-prezero"
                       data-source='{{ list|escape('html_attr') }}'
                       data-columns="{{ columns|json_encode|escape('html_attr') }}">
                </table>
            </div>
            {% elseif 'collectingplaces' in app.request.get('_route') %}
                <div class="px-10">
                    <div>
                        <div class="hidden sm:block">
                            <div class="border-b border-pzgrey-border mb-10">
                                <nav class="-mb-px flex" aria-label="Tabs">
                                    <a href="{{ path('management_contractArea_permission_users', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                        {{'user'|trans}}
                                    </a>
                                    <a href="{{ path('management_contractArea_permission_collectingplaces', { 'uuid': app.request.attributes.get('_route_params').uuid }) }}" class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg">
                                        {{'collectingplace.collecting_places'|trans}}
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col lg:flex-row justify-between mb-5">
                        <div class="mb-5">
                            <h3 class="text-xl font-extralight mb-2">{{'collectingplace.collecting_places'|trans}}</h3>
                            <p class="font-thin">
                                {{ 'collectingplace.collecting_places_contract_area_text'|trans({
                                    '%name%': contractArea.name
                                })|raw }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="px-10 mb-5">
                    <div id="filters-table-contractArea-list"></div>
                    <table id="contractArea-list" class="blackgrid-collectingplaces theme-prezero"
                           data-source='{{ list|escape('html_attr') }}'
                           data-columns="{{ columns|json_encode|escape('html_attr') }}">
                    </table>
                </div>
            {% endif %}

        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-contractarea-permission') }}
{% endblock %}
