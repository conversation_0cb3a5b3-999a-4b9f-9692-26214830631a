{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-contractarea-permission') }}
{% endblock %}

{% block popup %}
    {{  include('management-neu/user/edit_popup.html.twig', { title: 'user.details.add'|trans }) }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            {{'collectingplace.contract_territory_management'|trans}}
        </h1>

        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="bg-gradient-to-tr from-pzpetrol-dark via-pzpetrol-light to-pzblue-light px-10 py-5 shadow-lg rounded-t-xl mb-5 font-extralight">
                <h3 class="text-pzgreen font-thin text-xl">{{'collectingplace.assign_user'|trans}}</h3>
                <p class="text-white font-thin">{{'collectingplace.contract_area'|trans}} {{ contractArea.name }}</p>
                <p class="text-white font-thin">{{'collectingplace.collecting_place'|trans}} {{ collectingPlace.dsdId }} - {{ collectingPlace.name1 }} ({{ collectingPlace.city }})</p>
            </div>

            <div class="px-10">
                <div class="flex flex-col lg:flex-row justify-between mb-5">
                    <div class="mb-5">
                        <h3 class="text-xl font-extralight mb-2">Benutzer</h3>
                        <p class="font-thin">
                            {{ 'collectingplace.authorization'|trans({
                                '%dsdId%': collectingPlace.dsdId,
                                '%name1%': collectingPlace.name1,
                                '%city%': collectingPlace.city,
                                '%name%': contractArea.name
                            })|raw }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="px-10 mb-5">
                <div id="filters-table-contractArea-list"></div>
                <table id="contractArea-list" class="blackgrid-users theme-prezero"
                       data-source='{{ list|escape('html_attr') }}'
                       data-columns="{{ columns|json_encode|escape('html_attr') }}">
                </table>
            </div>

        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-contractarea-permission') }}
{% endblock %}
