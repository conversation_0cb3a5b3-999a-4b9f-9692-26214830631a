{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-reporting-details') }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            {{ 'management.reporting_by'|trans }} {{ object.name }}
        </h1>

        <div class="bg-white shadow-lg rounded-xl pb-1 mb-10 font-extralight">
            <div class="bg-pzgrey-light px-10 py-5 rounded-t-xl mb-5 font-extralight">
                <h3 class="text-black font-thin text-xl">{{ 'management.reporting'|trans }}</h3>
                <p class="text-black font-thin">
                    {{ title }}
                </p>
            </div>

            <div class="px-10">
                <div>
                    <div class="hidden sm:block">
                        <div class="border-b border-pzgrey-border mb-10">
                            <nav class="-mb-px flex" aria-label="Tabs">

                                <a href="{{ path(links[0].path, { 'uuid': app.request.attributes.get('_route_params').uuid }) }}"
                                    {% if links[0].active %}
                                        class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg"
                                    {% else %}
                                        class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg"
                                    {% endif %}
                                >
                                    {{ links[0].name }}
                                </a>
                                <a href="{{ path(links[1].path, { 'uuid': app.request.attributes.get('_route_params').uuid }) }}"
                                    {% if links[1].active %}
                                        class="border-pzgreen text-pzgreen w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg"
                                    {% else %}
                                        class="border-transparent text-pzgrey-border hover:text-pzgrey-dark hover:border-pzgrey-border w-1/4 py-4 px-1 text-center border-b-2 font-thin text-lg"
                                    {% endif %}
                                   >
                                    {{ links[1].name }}
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col lg:flex-row justify-between mb-5">
                    <div class="mb-5">
                        <h3 class="text-xl font-extralight mb-2">{{ table.name }}</h3>
                        <p class="font-thin">{{ table.text|raw }}</p>
                    </div>
                </div>
            </div>

            <div class="px-10 mb-5">
                <div id="filters-table-list"></div>
                <table id="list" class="blackgrid theme-prezero"
                       data-source='{{ list|escape('html_attr') }}'
                       data-columns="{{ columns|json_encode|escape('html_attr') }}">
                </table>
            </div>

        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-reporting-details') }}
{% endblock %}
