{% extends('management-neu/base.html.twig') %}

{% block stylesheets %}
    {{ parent() }}
    {{ encore_entry_link_tags('management-reporting') }}
{% endblock %}

{% block body %}
    <section class="container mx-auto px-4">
        <h1 class="font-thin text-lg mb-10 border-b-2 border-pzgreen">
            {{ 'management.reporting'|trans }}
        </h1>
        <div class="w-full mb-5 font-extralight">
            <label for="role">{{ 'management.reporting_by'|trans }}:</label>
            <select name="evaluation" id="evaluation" class="w-full lg:w-1/3 block h-12 rounded border-2 border-pzgrey  placeholder-pzgrey px-4 py-3">
                <option value="" disabled>{{ 'please_select'|trans }}</option>
                <option value="contractArea" {% if 'contractArea' in app.request.get('_route') %} selected {% endif%}>{{ 'management.contract_areas'|trans }}</option>
                <option value="collectingPlace" {% if 'collectingPlace' in app.request.get('_route') %} selected {% endif%}>{{ 'management.collecting_places'|trans }}</option>
                <option value="user" {% if 'user' in app.request.get('_route') %} selected {% endif%}>{{ 'user'|trans }}</option>
            </select>
        </div>

        <div class="bg-white shadow-pz-default rounded-xl pb-1 mb-10 font-extralight">
            <div class="bg-pzgrey-light px-10 py-5 rounded-t-xl mb-5 font-extralight">
                <h3 class="text-black font-thin text-xl">
                    {% if 'contractArea' in app.request.get('_route') %}
                        {{ 'management.contract_areas'|trans }}
                    {% elseif 'collectingPlace' in app.request.get('_route') %}
                        {{ 'management.collecting_places'|trans }}
                    {% elseif 'user' in app.request.get('_route') %}
                        {{ 'user'|trans }}
                    {% endif %}
                </h3>
            </div>

            <div class="px-10 mb-5">
                <div id="filters-table-list"></div>
                <table id="list" class="blackgrid theme-prezero"
                       data-source='{{ list|escape('html_attr') }}'
                       data-columns="{{ columns|json_encode|escape('html_attr') }}">
                </table>
            </div>
        </div>
    </section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('management-reporting') }}
{% endblock %}
