<div class="item"
     data-systemprovider="{{ orderEntry.systemProvider.name }}"
     data-systemprovider-id="{{ orderEntry.systemProvider.uuid }}"
     data-collect-registration-id="{{ orderEntry.uuid }}"
     data-disposal-number="{{ orderEntry.disposalNumber }}"
     data-date="{{ orderEntry.date|date('Y-m-d') }}"
     data-day="{{ orderEntry.date|date('d') }}"
     data-dayname="{{ orderEntry.date|date('D')|trans|upper }}"
     data-driver-message="{{ orderEntry.driverMessage }}"
     data-dispo-message="{{ orderEntry.dispoMessage }}"
     data-disabled="{{ orderEntry.date >= earliestBookingDate ? "false" : "true" }}"
     data-transferable="{{ day.isTransferable ? "true" : "false" }}"
     data-status="{% if orderEntry.canceled is empty %}{% if orderEntry.transferStatus == 'R' %}accepted{% else %}{% if orderEntry.transfered is empty %}pending{% else %}accepted{% endif %}{% endif %}{% else %}cancel{% endif %}"
>
    <h6>{{ orderEntry.systemProvider.name }}</h6>
    <p>{{'ordercalender.disponumber'|trans}} {{ orderEntry.disposalNumber }}</p>
    {% if orderEntry.driverMessage or orderEntry.dispoMessage %}<i class="fas fa-comment-dots"></i>{% endif %}
</div>

