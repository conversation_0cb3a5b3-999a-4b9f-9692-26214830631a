{% extends('base.html.twig') %}
{% block title %}{{'order.index_header'|trans}}{% endblock %}
{% block popup %}
    {% set date = earliestBookingDate < week[0].dtObject ? week[0].dtObject|date("d.m.Y") : earliestBookingDate|date("d.m.Y") %}
    {% set calendarWeek =  week[0].dtObject|date("W") ~ ' (' ~ week[0].dtObject|date("d.m") ~ '. -' ~ week[5].dtObject|date("d.m.Y") ~ ')' %}
    {% set collecting_place = collectingPlace.dsdid ~' - '~ collectingPlace.name1 ~ ' (' ~ collectingPlace.city ~')' %}
    <div class="popup" {% if showForm %}style="display:block;"{% endif %}>
        <div class="inner">
            <div class="header">
                <i class="fas fa-times-circle close"></i>
                <div class="info">
                    <b>{{ week[2].dtObject|date("F")|trans }} {{ week[2].dtObject|date("Y") }}</b><br>
                    {{'calendar.week'|trans}} {{ week[0].dtObject|date("W") }}<br>
                    ({{ week[0].dtObject|date("d.m") }}. - {{ week[5].dtObject|date("d.m.Y") }})
                </div>
                <div class="date" id="popup-dayinfo">
                    {% if showForm %}
                        {{ formDate|date("d") }}<span>{{ formDate|date("F")|trans }}</span>
                    {% else %}
                        22<span>MI</span>
                    {% endif %}
                </div>
            </div>
            <div class="content">

                <div class="form-group">
                    <div id="cancel-text" class="form-control alert-danger d-none" style="height: inherit;">{{'order.is_canceled'|trans}}</div>
                </div>

                <div class="form-group">
                    <label for="collectingPlace">{{'collectingplace'|trans}}</label>
                    <input type="text" name="collectingPlace" class="form-control" readonly="readonly" value="{{ collectingPlace.dsdid }} - {{ collectingPlace.name1 }} ({{ collectingPlace.city }})">
                </div>
                <div class="form-group">
                    <label for="contractArea">{{'contact.contract_area'|trans}}</label>
                    <input type="text" name="area" class="form-control" readonly="readonly" value="{{ area.name }}">
                </div>
                {{ form_start(form) }}
                    <div class="form-group">
                        <label for="disponummer">{{'contact.disponumber'|trans}}</label>
                        {{ form_widget(form.disposalNumber, { 'attr': { 'class':'form-control', 'readonly':'readonly', 'placeholder':'System generated' }}) }}
                    </div>
                    <div class="form-group">
                        <label for="systemgeber">{{'System deliverer'|trans}}*</label>
                        {{ form_widget(form.systemProvider, { 'attr': { 'class':'form-control' }}) }}
                    </div>
                    <div class="form-group">
                        <label for="fahrerinfo">{{'order.driver_message'|trans}}</label>
                        {{ form_widget(form.driverMessage, { 'attr': { 'class':'form-control','cols':'25', 'rows':'2', 'maxlength':'50' }}) }}
                    </div>
                    {#
                    <div class="form-group">
                        <label for="dispoinfo">Dispoinfo (max. 50 Zeichen)</label>
                        {{ form_widget(form.dispoMessage, { 'attr': { 'class':'form-control','cols':'25', 'rows':'2', 'maxlength':'50' }}) }}
                    </div>
                    #}

                    <div class="form-group">
                        <div id="contact-text" class="form-control alert-warning d-none" style="height: inherit;">{{'order.edit_is_not_possible'|trans|raw}}</div>
                    </div>

                     <div class="form-group">
                        <div id="cancel-info" class="form-control alert-warning d-none" style="height: inherit;">{{'order.cancel_info'|trans}}</div>
                    </div>

                    <button id="delete-button" type="button" class="btn-grey btn-icon float-left mt-0 mr-0 d-none" data-collect-registration-id=""><i class="fas fa-trash-alt"></i> {{'delete'|trans}}</button>
                    <button id="submit-button" type="submit" class="btn-secondary btn-icon float-right mt-0 mr-0 d-none"><i class="fas fa-check"></i> {{'transfer'|trans}}</button>
                    <button id="contact-button" type="button" class="btn-prime btn-icon float-right mt-0 mr-0 d-none" data-disposal-number="">{{'contact'|trans}}</button>
                    <button id="cancel-button" type="button" class="btn-red btn-icon float-left mt-0 mr-0 d-none" data-collect-registration-id="" data-disposal-number=""  
                    data-systemprovider-name="" data-date-to-cancel="" data-date-to-cancel-day=""><i class="fas fa-window-close"></i> {{'cancel_order'|trans}}</button>

                {{ form_end(form) }}

            </div>
        </div>
    </div>
    <div class="popup-cancelOrder">
        <div class="inner text-center">
            <h3 class="font-weight-normal">{{'order.process_cancellation'|trans}}</h3>
            <p>
                {{ 'order.cancel_popup'|trans({
                    '%dsdid%': collectingPlace.dsdid,
                    '%name1%': collectingPlace.name1,
                    '%city%': collectingPlace.city,
                    '%areaName%': area.name
                })|raw }}
            </p>
            <button id="cancelOrder-button" type="cancel" class="btn-red btn-icon float-right mt-0 mr-0"
                    data-place-id="{{ collectingPlace.uuid }}"
                    data-collect-registration-id="" 
                    data-disposal-number=""  
                    data-systemprovider-name="" 
                    data-date-to-cancel="" 
                    data-date-to-cancel-day=""
                    data-timelimit-to-cancel=""><i class="fas fa-window-close"></i>{{'cancel_order'|trans}}</button>
            <button type="ok" class="btn-grey btn-icon float-left mt-0 mr-0">{{'cancel'|trans}}</button>

        </div>
    </div>
    <div class="popup-transfer">
        <div class="inner text-center">
            <h3 class="font-weight-normal">{{'order.request_submit'|trans}}</h3>
            <p>
                {{ 'order.submit_popup'|trans({
                    '%date%': date,
                    '%calendarWeek%': calendarWeek,
                    '%collectingPlace%': collecting_place,
                    '%areaName%': area.name
                })|raw }}
            </p>
            <button id="transfer-save-button" type="save" class="btn-secondary btn-icon float-right mt-0 mr-0"
                    data-place-id="{{ collectingPlace.uuid }}"
                    data-from="{{ earliestBookingDate < week[0].dtObject ? week[0].dtObject|date("Y-m-d") : earliestBookingDate|date("Y-m-d") }}"
                    data-to="{{ week[5].dtObject|date('Y-m-d') }}">Beauftragen</button>
            <button type="ok" class="btn-grey btn-icon float-left mt-0 mr-0">{{'cancel'|trans}}</button>

        </div>
    </div>
    <div class="popup-thanks">
        <div class="inner text-center">
            <h3 class="font-weight-normal">{{ 'order.thank_for_request'|trans }}</h3>
            <p>
                {{ 'order.thanks_popup'|trans({
                    '%date%': date,
                    '%calendarWeek%': calendarWeek,
                    '%collectingPlace%': collecting_place,
                    '%areaName%': area.name
                })|raw }}
            </p>
            <button type="ok" class="btn-prime btn-center">{{ 'ok'|trans }}</button>
        </div>
    </div>
    <div class="popup-successCancel">
        <div class="inner text-center">
            <h3 class="font-weight-normal">{{ 'order.thank_for_request'|trans }}</h3>
            <p>
                {{ 'order.success_cancel'|trans({
                    '%collectingPlace%': collecting_place,
                    '%areaName%': area.name
                })|raw }}
            </p>
            <button type="ok" class="btn-prime btn-center">{{ 'ok'|trans }}</button>
        </div>
    </div> 
    <div class="popup-errorCancel">
        <div class="inner text-center">
            <h3 class="error font-weight-normal">{{ 'order.error_occurred'|trans }}</h3>
            <p>
                {{ 'order.error_cancel'|trans|raw }}
            </p>
            <button type="ok" class="btn-prime btn-center">{{ 'ok'|trans }}</button>
        </div>
    </div>
    <div class="popup-error">
        <div class="inner text-center">
            <h3 class="error font-weight-normal">{{ 'order.error_occurred'|trans }}</h3>
                {{ 'order.error'|trans({
                    '%calendarWeek%': calendarWeek
                })|raw }}
            </p>
            <button type="ok" class="btn-prime btn-center">{{ 'ok'|trans }}</button>
        </div>
    </div>
{% endblock %}
{% block body %}
<main>
    <div class="container">
        <div class="row">
            <div class="col-sm-12 d-flex justify-content-left">
                <a href="{{ path('app_default_ordernewclearance') }}"><i class="fas fa-angle-left"></i> {{ 'back_to_selection'|trans }}</a>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 d-flex justify-content-center">
                <div class="text-center mb-5">
                    <h1>{{ 'order.register_header'|trans }}</h1>
                    {% if collectingPlace %}
                        <p class="lead">{{ 'collectingplace'|trans }}: {{ collectingPlace.dsdid }} - {{ collectingPlace.name1 }}, {{ collectingPlace.street }} {{ collectingPlace.houseNumber }}, {{ collectingPlace.postalCode }} {{ collectingPlace.city }}</p>
                    {% endif %}
                    {% if area %}
                        <p class="lead">{{ 'collectingplace.collecting_place'|trans }}: {{ area.name }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="box-rounded pl-0 pr-0 pb-0">
                    <div class="row">
                        <div class="col-sm-3 text-center order-2 order-sm-1">
                            <p><b>{{ 'calendar.week_abbreviation'|trans }}  {{ week[0].dtObject|date("W") }}</b> ({{ week[0].dtObject|date("d.m") }}. - {{ week[5].dtObject|date("d.m.Y") }})</p>
                        </div>
                        <div class="col-sm-6 text-center order-1 order-sm-2">
                            <h2 class="mb-4">{{ week[2].dtObject|date("F")|trans }} {{ week[2].dtObject|date("Y") }}</h2>
                        </div>
                        <div class="col-sm-3 text-center order-3 order-sm-3">
                            <p>{{ 'total_per_week'|trans }} <span class="badge badge-secondary font-weight-normal">{{ orderAmountWeek }}</span> {{ 'from'|trans }}  {{ orderAmountWeekLimit }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                                <table class="table table-calendar">
                                    <thead>
                                    <tr>
                                        {% if previousLink %}
                                            <th><a href="{{ path('app_default_ordernewclearancelist', {'collectingPlace': collectingPlace.uuid,'addWeekCount': weekDown}) }}"><i class="fas fa-chevron-circle-left"></i></a></th>
                                        {% else %}
                                            <th><a href="#"><i class="fas fa-chevron-circle-left"></i></a></th>
                                        {% endif %}

                                        {% for day in week %}
                                            {% if not day.isPublicHoliday and day.dtObject | date('md') >= earliestBookingDate | date('md') %}
                                                <th>{{ day.dtObject|date("d.m") }} <span>{{ day.dtObject|date("D")|trans }}</span></th>
                                            {% else %}
                                                <th data-disabled="true">{{ day.dtObject |date("d.m") }}
                                                    <span>{{ day.dtObject |date("D")|trans }}&nbsp;</span>
                                                </th>
                                            {% endif %}
                                        {% endfor %}

                                        {% if futureLink %}
                                            <th><a href="{{ path('app_default_ordernewclearancelist', {'collectingPlace': collectingPlace.uuid,'addWeekCount': weekUp}) }}"><i class="fas fa-chevron-circle-right"></i></a></th>
                                        {% else %}
                                            <th><a href="#"><i class="fas fa-chevron-circle-right"></i></a></th>
                                        {% endif %}
                                    </tr>
                                    </thead>
                                    <tbody>
                                    
				    <!-- START loop available bookings -->
                                    {% set btn_transferable = false %}
                                    {% for order in collectOrders %}
                                        {% if btn_transferable == false %}
                                            {% for o in order %}
                                                {% if o is not null and o.transfered is empty and o.transferStatus != 'R' and o.date|date('Ymd') >= earliestBookingDate|date('Ymd') %}
                                                    {% set btn_transferable = true %}
                                                {% endif %}
                                            {% endfor %}
                                        {% endif %}

                                        <tr>
                                            <td></td>
                                            <td> <!-- Mon -->
                                                {% if week[0].isPublicHoliday and loop.first %}

                                                    {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[0] } only %}

                                                {% elseif order['Mon']|length %}

                                                    {% include 'default/order-calendar-snippet-booking.html.twig' with {'orderEntry': order['Mon'], 'day': week[0], 'earliestBookingDate': earliestBookingDate } only %}

                                                {% elseif not week[0].isPublicHoliday %}

                                                    {% if loop.index == collectOrders.lastElementMon + 1 %}

                                                        {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[0], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {% endif %}

                                                {% endif %}
                                            </td>
                                            <td> <!-- Tue -->
                                                {% if week[1].isPublicHoliday and loop.first %}

                                                    {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[1] } only %}

                                                {% elseif order['Tue']|length %}

                                                    {% include 'default/order-calendar-snippet-booking.html.twig' with {'orderEntry': order['Tue'], 'day': week[1], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {# % for msg in week[1].validityMessages %}<span>{{ msg }}</span>{% endfor % #}

                                                {% elseif not week[1].isPublicHoliday %}

                                                    {% if loop.index == collectOrders.lastElementTue + 1 %}

                                                        {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[1], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {% endif %}

                                                {% endif %}
                                            </td>
                                            <td> <!-- Wed -->
                                                {% if week[2].isPublicHoliday and loop.first %}

                                                    {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[2] } only %}

                                                {% elseif order['Wed']|length %}

                                                    {% include 'default/order-calendar-snippet-booking.html.twig' with {'orderEntry': order['Wed'], 'day': week[2], 'earliestBookingDate': earliestBookingDate } only %}

                                                {% elseif not week[2].isPublicHoliday %}

                                                    {% if loop.index == collectOrders.lastElementWed + 1 %}

                                                        {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[2], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {% endif %}

                                                {% endif %}
                                            </td>
                                            <td> <!-- Thu -->
                                                {% if week[3].isPublicHoliday and loop.first %}

                                                    {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[3] } only %}

                                                {% elseif order['Thu']|length %}

                                                    {% include 'default/order-calendar-snippet-booking.html.twig' with {'orderEntry': order['Thu'], 'day': week[3], 'earliestBookingDate': earliestBookingDate } only %}

                                                {% elseif not week[3].isPublicHoliday %}

                                                    {% if loop.index == collectOrders.lastElementThu + 1 %}

                                                        {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[3], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {% endif %}

                                                {% endif %}
                                            </td>
                                            <td> <!-- Fri -->
                                                {% if week[4].isPublicHoliday and loop.first %}

                                                    {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[4] } only %}

                                                {% elseif order['Fri']|length %}

                                                    {% include 'default/order-calendar-snippet-booking.html.twig' with {'orderEntry': order['Fri'], 'day': week[4], 'earliestBookingDate': earliestBookingDate } only %}

                                                {% elseif not week[4].isPublicHoliday %}

                                                    {% if loop.index == collectOrders.lastElementFri + 1 %}

                                                        {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[4], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {% endif %}

                                                {% endif %}
                                            </td>
                                            <td><!-- Sat -->
                                                {% if week[5].isPublicHoliday and loop.first %}

                                                    {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[5] } only %}

                                                {% elseif order['Sat']|length %}

                                                    {% include 'default/order-calendar-snippet-booking.html.twig' with {'orderEntry': order['Sat'], 'day': week[5], 'earliestBookingDate': earliestBookingDate } only %}

                                                {% elseif not week[5].isPublicHoliday %}

                                                    {% if 1 == 2 and loop.index == collectOrders.lastElementSat + 1 %}

                                                        {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[5], 'earliestBookingDate': earliestBookingDate } only %}

                                                    {% endif %}

                                                {% endif %}
                                            </td>
                                            <td></td>
                                        </tr>

                                        {% if loop.index == collectOrders.maxCount %}

                                            {% include 'default/order-calendar-snippet-lastrow.html.twig' with {'week': week, 'collectOrders' : collectOrders, 'loopFirst' : loop.first, 'earliestBookingDate': earliestBookingDate } only %}

                                        {% endif %}

                                    {% else %}

                                        {% include 'default/order-calendar-snippet-lastrow.html.twig' with {'week': week, 'collectOrders' : collectOrders, 'loopFirst' : true, 'earliestBookingDate': earliestBookingDate } only %}

                                    {% endfor %}

                                    <!-- END loop available bookings. -->

                                    </tbody>
                                </table>
                            </div>
                            <div class="legend">
                                <ul>
                                    <li data-status="pending">{{ 'order.created'|trans }}</li>
                                    <li data-status="accepted">{{ 'order.transferred'|trans }}</li>
                                    <li data-status="cancel">{{ 'order.cancel'|trans }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <button id="contact-button-list" type="button" class="btn-prime float-left d-none d-sm-inline-block" data-collectingplace="{{ collectingPlace.uuid }}" data-contractarea="{{ area.uuid }}">{{ 'contact.prezero'|trans }}</button>
                {% if (week[0].dtObject|date('YW') >= earliestBookingDate|date('YW')) or ((week[week|length-1].dtObject|date('Y') > week[0].dtObject|date('Y')) and (week[week|length-1].dtObject|date('YW') >= earliestBookingDate|date('YW'))) %}
                <button type="transfer" class="btn-prime float-right d-none d-sm-inline-block" {% if btn_transferable == false %} disabled {% endif %}>{{ 'order.request_pickup'|trans }}</button>
                {% endif %}
            </div>
        </div>
    </div>
</main>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('ordernewclist') }}
{% endblock %}
