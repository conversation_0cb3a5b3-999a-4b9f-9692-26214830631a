{% extends('base.html.twig') %}
{% block title %}{{'order.index_header'|trans}}{% endblock %}
{% block body %}
<main>
    <div class="container">
        <div class="row">
            <div class="col-sm-12 d-flex justify-content-left">
                <a href="{{ path('app_default_orderoverview') }}"><i class="fas fa-angle-left"></i>{{'go_to_overview'|trans}}</a>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 d-flex justify-content-center">
                <h1 class="mb-5">{{'order.register_header'|trans}}</h1>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="box-rounded">

                    {{ form_start(form) }}

                        <div class="form-group mb-5">
                            <h2>{{'order.collecting_place.select_question'|trans}}</h2>
                            <label for="anfallstelle">{{'order.collecting_place.select_text'|trans}}</label>

                            {{ form_widget(form.collectingPlace, { 'attr': { 'class':'form-control' } }) }}

                        </div>

                        <div class="form-group mb-5">

                            <label for="vertrag">{{'order.contract_area.select_text'|trans}}</label>

                            {% if form.contract_contractArea.vars.errors|length > 0 %}
                                <div class="alert alert-danger">
                                    {% for error in form.contract_contractArea.vars.errors %}
                                        {{ error.message }}<br />
                                    {% endfor %}
                                </div>
                            {% endif %}

                            {{ form_widget(form.contract_contractArea, { 'attr': { 'class':'form-control' } }) }}

                        </div>

                        <div class="form-group">
                            <h2>{{'order.pickup.time_period_question'|trans}}</h2>
                            <label for="zeitraum">{{'select.time_period'|trans}}</label>

                            {{ form_widget(form.calendarWeeks, { 'attr': { 'class':'form-control' } }) }}

                        </div>

                        {{ form_widget(form.submit, { 'attr': { 'class':'btn-prime float-right' } }) }}

                    {{ form_end(form) }}
                </div>
            </div>
            <div class="col-md-6">
                <div class="box-rounded">
                    <div class="row">
                        <div class="col-sm-12">
                            <h2>{{'order.collecting_place.infos'|trans}}</h2>
                            <p>
                                {% if firstCollectingPlace %}
                                    <span id="collect-place-dsdid">{{ firstCollectingPlace.dsdid }}</span><br>
                                    <span id="collect-place-name">{{ firstCollectingPlace.name1 }}</span><br>
                                    <span id="collect-place-street">{{ firstCollectingPlace.street }}</span>
                                    <span id="collect-place-housenumber">{{ firstCollectingPlace.houseNumber }}</span><br>
                                    <span id="collect-place-postalcode">{{ firstCollectingPlace.postalCode }}</span>
                                    <span id="collect-place-city">{{ firstCollectingPlace.city }}</span><br>
                                {#<br>
                                    Öffnungszeiten:<br>
                                    Mo. - Fr. 8.00 - 18.00 Uhr#}
                                {% else %}
                                    <span id="no-collecting-place">{{'order.collecting_place.no_infos_text'|trans}}</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>

                {#
                <div class="box-rounded">
                    <div class="row">
                        <div class="col-sm-8">
                            <h3 class="text-uppercase font-weight-normal">Ansprechpartner</h3>
                            <p>
                                Max Mustermann<br>
                                Musterstraße 5<br>
                                12345 Musterstadt<br>
                                <br>
                                <b><a href="tel:+495719744123"><i class="fas fa-phone"></i> +49(0)571 9744123</a></b>
                            </p>
                        </div>
                        <div class="col-6 col-sm-4">
                            <img src="{{ asset('build/img/thumbnail.jpg') }}" class="rounded-circle">
                        </div>
                    </div>
                </div>  #}
            </div>
        </div>
    </div>
</main>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('ordernew') }}
{% endblock %}
