<tr>
    <td></td>
    <td> <!-- Mon -->
        {% if week[0].isPublicHoliday and loopFirst %}

            {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[0] } only %}

        {% elseif not week[0].isPublicHoliday and collectOrders.isMonInLastRow %}

            {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[0], 'earliestBookingDate': earliestBookingDate } only %}

        {% endif %}
    </td>
    <td> <!-- Tue -->
        {% if week[1].isPublicHoliday and loopFirst %}

            {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[1] } only %}

        {% elseif not week[1].isPublicHoliday and collectOrders.isTueInLastRow %}

            {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[1], 'earliestBookingDate': earliestBookingDate } only %}

        {% endif %}
    </td>
    <td> <!-- Wed -->
        {% if week[2].isPublicHoliday and loopFirst %}

            {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[2] } only %}

        {% elseif not week[2].isPublicHoliday and collectOrders.isWedInLastRow %}

            {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[2], 'earliestBookingDate': earliestBookingDate } only %}

        {% endif %}
    </td>
    <td> <!-- Thu -->
        {% if week[3].isPublicHoliday and loopFirst %}

            {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[3] } only %}

        {% elseif not week[3].isPublicHoliday and collectOrders.isThuInLastRow %}

            {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[3], 'earliestBookingDate': earliestBookingDate } only %}

        {% endif %}
    </td>
    <td> <!-- Fri -->
        {% if week[4].isPublicHoliday and loopFirst %}

            {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[4] } only %}

        {% elseif not week[4].isPublicHoliday and collectOrders.isFriInLastRow %}

            {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[4], 'earliestBookingDate': earliestBookingDate } only %}

        {% endif %}
    </td>
    <td><!-- Sat -->
        {% if week[5].isPublicHoliday and loopFirst %}

            {% include 'default/order-calendar-snippet-holiday.html.twig' with {'holiday': week[5] } only %}

        {% elseif not week[5].isPublicHoliday and collectOrders.isSatInLastRow and 1 == 2 %}

            {% include 'default/order-calendar-snippet-addentry.html.twig' with {'day': week[5], 'earliestBookingDate': earliestBookingDate } only %}

        {% endif %}
    </td>
    <td></td>
</tr>

