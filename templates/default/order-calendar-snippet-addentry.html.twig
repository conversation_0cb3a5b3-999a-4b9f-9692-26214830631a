<div class="add"
     data-day="{{ day.dtObject|date("d") }}"
     data-dayname="{{ day.dtObject|date("D")|trans|upper }}"
     data-date="{{ day.dtObject|date("Y-m-d") }}"
     data-isorderable="{{ day.isOrderAble }}">
    <div class="mb-2">
        {% if day.isOrderAble and day.dtObject >= earliestBookingDate %}
        <i class="fas fa-plus-circle"></i>
        {% endif %}
    </div>
    <span class="badge badge-secondary font-weight-normal">
        {{ 'ordercalender.ordercount'|trans({
            '%count%': day.orderCount,
            '%countMax%': day.orderCountMax
        })|raw }}
    </span>
</div>

