{% extends('base.html.twig') %}
{% block title %}{{ 'order.report.title'|trans }}{% endblock %}
{% block body %}
    <main>
        <div class="container">
            <div class="row">
                <div class="col-sm-12 d-flex justify-content-center">
                    <h1 class="mb-5">{{ 'order.report'|trans }} ({{ pagination.getTotalItemCount }})</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">
                    <aside>

                        {{ form_start(form) }}

                            <div class="form-group">
                                <label for="anfallstelle">{{ 'collectingplace.collecting_place'|trans }}</label>
                                {{ form_widget(form.collectingPlace, { 'attr': { 'class':'form-control'}}) }}
                            </div>

                            <div class="form-group">
                                <label for="datumvon">{{ 'order.report.date_from'|trans }}</label>
                                {{ form_widget(form.dateFrom, { 'attr': { 'class':'form-control'}}) }}
                            </div>

                            <div class="form-group">
                                <label for="datumbis">{{ 'order.report.date_to'|trans }}</label>
                                {{ form_widget(form.dateTo, { 'attr': { 'class':'form-control'}}) }}
                            </div>

                            <div class="form-group">
                                <label for="submit">&nbsp;</label>
                                {{ form_widget(form.submit, { 'attr': { 'class':'form-control btn-secondary btn-center btn-text-only'}} ) }}
                            </div>

                        {{ form_end(form) }}

                    </aside>
                </div>
                <div class="col-md-9">
                    <div class="actions clearfix mb-5">
                        <a href="{{ path('app_default_ordernewclearance') }}" class="btn-secondary btn-icon float-left m-0"><i class="fas fa-calendar-plus"></i>{{ 'order.register_header'|trans }}</a>

                        {{ form_start(exportForm, { 'attr': { 'id':'export-form' }}) }}

                            {{ form_widget(exportForm.dateFrom) }}

                            {{ form_widget(exportForm.dateTo) }}

                            {{ form_widget(exportForm.collectingPlace) }}

                            <a id="export-submit-button" href="#" class="btn-prime btn-icon float-right m-0"><i class="fas fa-download"></i> {{ 'order.report.export'|trans }}</a>

                        {{ form_end(exportForm) }}

                    </div>

                    <div class="table-responsive">
                        <table id="order-overview" class="table table-striped">
                            <thead>
                            <tr>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'date', order: sortOrder.date })) }}"
                                    >{% if sortOrder.date == 'ASC' %}
                                        <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.date == 'DESC' %}
                                        <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'order.date'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'status', order: sortOrder.status })) }}"
                                    >{% if sortOrder.status == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.status == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'order.status'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'contractArea', order: sortOrder.contractArea })) }}"
                                    >{% if sortOrder.contractArea == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                        {% elseif sortOrder.contractArea == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                        {% endif %} {{ 'contact.contract_area'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'dsdid', order: sortOrder.dsdid })) }}"
                                    >{% if sortOrder.dsdid == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                        {% elseif sortOrder.dsdid == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                        {% endif %} {{ 'collectingplace'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'id', order: sortOrder.id })) }}"
                                    >{% if sortOrder.id == 'ASC' %}
                                            <i class="fas fa-arrow-up"></i>
                                        {% elseif sortOrder.id == 'DESC' %}
                                            <i class="fas fa-arrow-down"></i>
                                        {% endif %} {{ 'contact.disponumber'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'providerName', order: sortOrder.providerName })) }}"
                                    >{% if sortOrder.providerName == 'DESC' %}
                                        <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.providerName == 'ASC' %}
                                        <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'System deliverer'|trans }}</a>
                                </th>
                                <th><a href="{{ path('app_default_orderoverview', app.request.query.all|merge(app.request.attributes.get('_route_params'))|merge({orderBy: 'driverMessage', order: sortOrder.driverMessage })) }}"
                                    >{% if sortOrder.driverMessage == 'DESC' %}
                                            <i class="fas fa-arrow-up"></i>
                                    {% elseif sortOrder.driverMessage == 'ASC' %}
                                            <i class="fas fa-arrow-down"></i>
                                    {% endif %} {{ 'Driver message'|trans }}</a>
                                </th>
                            </tr>
                            </thead>
                            <tbody>

                            {% for order in pagination %} {# orderList #}
                                <tr>
                                    <td>{{ order.date|date('d.m.Y') }}</td>
                                    <td>{{ order.canceled ? ('canceled'|trans) : ( order.transfered ? ('requested'|trans) : ('created'|trans) ) }}</td>
                                    <td>{{ order.contractArea }}</td>
                                    <td>{{ order.dsdid }} - {{ order.name1 }} {{ order.name2 }}
                                        {{ order.street }}
                                        {{ order.houseNumber }},
                                        {{ order.postalCode }}
                                        {{ order.city }} </td>
                                    <td>{{ order.disposalNumber }}</td>
                                    <td>{{ order.providerName }}</td>
                                    <td>{{ order.driverMessage }}</td>
                                </tr>
                            {% endfor %}
                           
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-center">{{ knp_pagination_render(pagination) }}</div>
                </div>
            </div>
        </div>
    </main>

{% endblock %}
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('orderoverview') }}
{% endblock %}
