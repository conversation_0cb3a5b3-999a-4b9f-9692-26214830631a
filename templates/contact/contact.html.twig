{% extends('base.html.twig') %}
{% block title %}{{ 'contact.title'|trans }}{% endblock %}
{% block body %}
    <main>
        <div class="container">
            <div class="row">
                <div class="col-sm-12 d-flex justify-content-center">
                    <h1 class="mb-5">{{ 'contact.header'|trans }}</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3">&nbsp;</div>
                <div class="col-md-6">
                    <div class="box-rounded">
                        {{ include('default/flash.html.twig') }}
                        <h2 class="font-weight-normal">{{ 'contact_form'|trans }}</h2>

                        {{ form_start(form) }}

                        <div style="display:none !important;" aria-hidden="true">
                            {{ form_widget(form.time) }}
                            {{ form_widget(form.email2) }}
                            {{ form_widget(form.telephone2) }}
                        </div>

                        <div class="form-group">
                            <label for="request_types">{{ 'contact.request_type'|trans }} <span
                                        id="request_types_required">*</span></label>
                            {{ form_widget(form.request_types, { 'attr': { 'class':'form-control'}}) }}
                        </div>

                        <div class="form-group">
                            <label for="anfallstelle">{{ 'contact.collecting_place'|trans }} <span
                                        id="anfallstelle_required">*</span></label>
                            {{ form_widget(form.collecting_place, { 'attr': { 'class':'form-control'}}) }}
                        </div>

                        {% if form.contract_area is defined %}
                            <div class="form-group">
                                <label for="vertragsgebiet">{{ 'contact.contract_area'|trans }} <span
                                            id="vertragsgebiet_required">*</span></label>
                                {{ form_widget(form.contract_area, { 'attr': { 'class':'form-control'}}) }}
                            </div>
                        {% endif %}

                        {% if form.disponumber is defined %}
                            <div class="form-group">
                                <label for="disponummer">{{ 'contact.disponumber'|trans }} <span
                                            id="disponummer_required">*</span></label>
                                {{ form_widget(form.disponumber, { 'attr': { 'class':'form-control'}}) }}
                            </div>
                        {% endif %}

                        {% if form.sender_email is defined %}
                            <div class="form-group">
                                <label for="absendeemail">{{ 'contact.sender_email'|trans }} <span
                                            id="absendeemail_required">*</span></label>
                                {{ form_widget(form.sender_email, { 'attr': { 'class':'form-control'}}) }}
                            </div>
                        {% endif %}

                        {% if form.company is defined %}
                            <div class="form-group">
                                <label for="company">{{ 'contact.company'|trans }} <span id="company_required">*</span></label>
                                {{ form_widget(form.company, { 'attr': { 'class':'form-control'}}) }}
                            </div>
                        {% endif %}

                        <div class="form-group">
                            <label for="nachricht">{{ 'contact.message'|trans }} <span id="nachricht_required">*</span></label>
                            {{ form_widget(form.message, { 'attr': { 'class':'form-control'}}) }}
                        </div>

                        <br><br>
                        <div id="captcha" class="col-xs-12 col-sm-8 col-sm-offset-2 text-center h-captcha"
                             data-sitekey="{{ app.request.server.get('CAPTCHA_SITE_KEY') }}"></div>

                        <div class="form-group">
                            {{ form_widget(form.submit, { 'attr': { 'class':'btn-prime float-left d-none d-sm-inline-block'}}) }}
                        </div>

                        {{ form_end(form) }}

                    </div>
                </div>
                <div class="col-md-3">&nbsp;</div>
            </div>
        </div>
    </main>
{% endblock %}
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('contact') }}
    <script src="https://js.hcaptcha.com/1/api.js" async defer></script>
{% endblock %}
