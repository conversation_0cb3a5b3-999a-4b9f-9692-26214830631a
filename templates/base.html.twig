<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <title>{% block title %}PreZero{% endblock %}</title>
        {% block stylesheets %}
            {{ encore_entry_link_tags('app') }}
            <link rel="stylesheet" href="{{ asset('build/main.css') }}">
        {% endblock %}
    </head>
    <body>
        {% block popup %}{% endblock %}
        <div class="wrapper">
            <header>
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="inner-header">
                                <a href="{% if not is_granted('ROLE_MANAGER') and not is_granted('ROLE_REPORT') %}{{ path('app_default_index') }}{% else %}{{ path('management_order_report') }}{% endif %}" class="logo"><img src="{{ asset('build/img/logo-prezero.svg') }}"></a> <!-- /order-overview.html -->
                                
                                {% if is_granted('IS_AUTHENTICATED_FULLY') %}
                                    <a href="{{ path('app_logout') }}" class="logout"><i class="fas fa-sign-out-alt"></i></a>
                                    <a href="{{ path('app_userprofile_userprofile') }}" class="profile"><i class="fas fa-user"></i> {{ 'profile'|trans }}</a>
                                    {% if not is_granted('ROLE_MANAGER') and not is_granted('ROLE_REPORT') %}<a href="{{ path('app_contact_contact') }}" class="contact"><i class="far fa-paper-plane"></i> {{ 'contact'|trans }}</a>{% endif %}

                                    {% if is_granted('ROLE_MANAGER') %}<a href="{{ path('management') }}" class="admin"><i class="fas fa-cog"></i></a>{% endif %}
                                    <ul>
                                        {% if is_granted('ROLE_MANAGER') or is_granted('ROLE_REPORT') %}<li><a href="{{ path('management_order_report') }}"><i class="far fa-paper-plane"></i> {{ 'reporting'|trans }}</a></li>{% endif %}
                                        {% if is_granted('ROLE_ORDER') %}
                                            {% if is_granted('ROLE_MANAGER') or is_granted('ROLE_REPORT') %} <b>|</b> {% endif %}
                                            <li><a href="{{ path('app_default_orderoverview') }}"><i class="fas fa-list"></i> {{ 'order.report'|trans }}</a></li>
                                            <li><a href="{{ path('app_default_ordernewclearance') }}"><i class="far fa-calendar-plus"></i> {{ 'order.register_header'|trans }}</a></li>
                                        {% endif %}
                                        {% if is_granted('ROLE_DOCUMENTS') %}<li><a href="{{ path('app_default_documentsview') }}"><i class="far fa-file-pdf"></i> {{ 'weighing_slip'|trans }}</a></li>{% endif %}
                                    </ul>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            {#
            <div class='browsererror'>
                <b><i class='far fa-bell'></i> Wartungsarbeiten <i class='far fa-bell'></i></b> <br>
                Am <u>Montag, den 04.04.2022</u> steht das Portal aufgrund von Wartungsarbeiten nicht zur Verfügung. <br>
                Bitte berücksichtigen Sie die Wartungsarbeiten bei Ihren Anmeldungen. Ab Dienstag, den 05.04.2022 steht das Portal wieder uneingeschränkt zur Verfügung.
            </div>
            #}

            {% block body %}{% endblock %}

            <footer>
                <div class="footer-top">
                    <div class="container">
                        <div class="row">
                            <div class="col-sm-4">
                                <p>
                                    <a title="Gewerbe &amp; Industrie" target="_blank" href="https://prezero.com/gewerbe-industrie/">{{ 'commerce_and_industry'|trans }}</a>
                                </p>
                            </div>
                            <div class="col-sm-4">
                                <p>
                                    <a title="Privathaushalte" target="_blank" href="https://container.online/">{{ 'private_households'|trans }}</a>
                                </p>
                            </div>
                            <div class="col-sm-4">
                                <p>
                                    <a title="Entsorgung konfigurieren" target="_blank" href="https://container.online/PreZero.Webstore.Web/Home/Business">{{ 'configure_disposal'|trans }}</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer-middle">
                    <div class="container">
                        <div class="row">
                            <div class="col-sm-3">
                                <p><a href="{{ path('app_default_orderoverview') }}" class="logo"><img src="{{ asset('build/img/logo-prezero.svg') }}"></a></p>
                            </div>
                            <div class="col-sm-3">
                                <p><b>PreZero Recycling Deutschland<br> GmbH & KG</b></p>
                            </div>
                            <div class="col-sm-3">
                                <p>
                                    Auf der Plaße 1 <br>
                                    32469 Petershagen 
                                </p>
                            </div>
                            <div class="col-sm-3">
                                <p>
                                    <a href="mailto:<EMAIL>" class="green"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <ul>
                                    <li><a href="{{ path('app_contact_contact') }}" class="grey">{{ 'contact'|trans }}</a></li>
                                    <li><a href="{{ path('app_default_dataprivacy') }}" class="grey">{{ 'privacy_policy'|trans }}</a></li>
                                    <li><a href="{{ path('app_default_compliance') }}" class="grey">{{ 'compliance'|trans }}</a></li>
                                    <li><a href="{{ path('app_default_imprint') }}" class="grey">{{ 'legal_notice'|trans }}</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
        {% block javascripts %}
            {{ encore_entry_script_tags('app') }}
        {% endblock %}
    </body>
</html>

<body>
