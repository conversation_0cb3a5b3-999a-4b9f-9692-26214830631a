<?php

declare(strict_types=1);

namespace App\Message;

use Symfony\Component\Uid\Uuid;

abstract readonly class AbstractMessage
{
    public string $messageId;
    public \DateTimeImmutable $messageTimestamp;

    public function __construct(
    ) {
        $this->messageId = Uuid::v4()->toRfc4122();
        $this->messageTimestamp = new \DateTimeImmutable();
    }

    public function getMessageId(): string
    {
        return $this->messageId;
    }

    public function getMessageTimestamp(): \DateTimeImmutable
    {
        return $this->messageTimestamp;
    }
}
