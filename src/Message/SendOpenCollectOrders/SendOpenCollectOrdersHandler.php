<?php

declare(strict_types=1);

namespace App\Message\SendOpenCollectOrders;

use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Output\NullOutput;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;

#[AsMessageHandler]
readonly class SendOpenCollectOrdersHandler
{
    public function __construct(
        private KernelInterface $kernel,
    ) {
    }

    public function __invoke(SendOpenCollectOrders $message): void
    {
        $application = new Application(kernel: $this->kernel);
        $application->setAutoExit(boolean: false);

        $input = new ArrayInput(parameters: [
            'command' => 'app:sendOpenCollectOrders',
        ]);

        $output = new NullOutput();
        $application->run(input: $input, output: $output);
    }
}
