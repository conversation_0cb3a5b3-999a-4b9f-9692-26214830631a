<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Files\DocumentData as OldDocumentData;
use App\Entity\Main\Document as MainDocument;
use App\Entity\Main\DocumentData as NewDocumentData;
use App\Repository\Main\DocumentRepository;
use App\Services\FileStore;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 's3:migrate-files',
    description: 'Migrates files from the old DB blob storage to S3'
)]
class S3MigrateFilesCommand
{
    public function __construct(private readonly FileStore $fileStore, private readonly ManagerRegistry $managerRegistry)
    {
    }

    public function __invoke(SymfonyStyle $io): int
    {
        $io->title(message: 'S3 File Migration');
        $startTime = new \DateTime();
        $oldFileManager = $this->managerRegistry->getManager('files');
        $defaultManager = $this->managerRegistry->getManager('default');
        $oldDocRepo = $oldFileManager->getRepository(OldDocumentData::class);
        $newDocRepo = $defaultManager->getRepository(NewDocumentData::class);
        /** @var DocumentRepository $mainDocRepo */
        $mainDocRepo = $defaultManager->getRepository(MainDocument::class);
        /** @var EntityManagerInterface $oldFileManager */
        $oldFileManager = $this->managerRegistry->getManager('files');
        $totalDocuments = (int) $oldFileManager->createQueryBuilder()->select('count(d.id)')->from(from: OldDocumentData::class, alias: 'd')->getQuery()->getSingleScalarResult();
        $io->note(message: "Found {$totalDocuments} total documents to migrate.");
        if (0 === $totalDocuments) {
            $io->success(message: 'No documents to migrate.');

            return Command::SUCCESS;
        }
        $io->progressStart(max: $totalDocuments);
        $batchSize = 500;
        $offset = 0;
        $successfullyCreated = 0;
        $successfullyUpdated = 0;
        $skippedMissingParent = 0;
        $skippedBatchDuplicate = 0;
        $skippedNoStream = 0;
        $skippedFailed = 0;

        while (true) {
            $batchOfDocuments = $oldDocRepo->findBy([], ['id' => 'DESC'], $batchSize, $offset);
            $docCountInBatch = count(value: $batchOfDocuments);

            if (0 === $docCountInBatch) {
                break;
            }

            /** @var array<string, bool> $processedInThisBatch */
            $processedInThisBatch = [];

            foreach ($batchOfDocuments as $oldDoc) {
                $uuid = strtolower(string: trim(string: $oldDoc->getUuid()->toString()));
                $fileStream = null;
                $dbStream = null;

                try {
                    if (isset($processedInThisBatch[$uuid])) {
                        ++$skippedBatchDuplicate;
                        $io->progressAdvance();
                        continue;
                    }

                    $mainDoc = $mainDocRepo->findOneBy(criteria: ['uuid' => Uuid::fromString(uuid: $uuid)]);
                    if (!$mainDoc) {
                        ++$skippedMissingParent;
                        $io->progressAdvance();
                        continue;
                    }

                    $newDoc = $newDocRepo->findOneBy(['uuid' => Uuid::fromString(uuid: $uuid)]);
                    $isUpdate = true;

                    if (!$newDoc) {
                        $newDoc = new NewDocumentData();
                        $newDoc->setUuid(uuid: Uuid::fromString(uuid: $uuid));
                        $isUpdate = false;
                    }

                    $oldS3Path = $isUpdate ? $newDoc->getS3FilePath() : null;

                    $s3Key = $this->fileStore->getS3Key(uuid: $uuid);

                    $dbStream = $oldDoc->getFile();

                    if (!is_resource(value: $dbStream)) {
                        $io->warning(message: 'Skipping UUID: '.$uuid.' (no file stream or invalid resource)');
                        ++$skippedNoStream;
                        $io->progressAdvance();
                        continue;
                    }

                    $fileStream = fopen(filename: 'php://temp', mode: 'r+');
                    if (false === $fileStream) {
                        throw new \RuntimeException(message: 'Failed to open temp stream for migration.');
                    }

                    stream_copy_to_stream(from: $dbStream, to: $fileStream);

                    rewind(stream: $fileStream);

                    $this->fileStore->upload(s3Path: $s3Key, contentStream: $fileStream);

                    if ($isUpdate && $oldS3Path && $oldS3Path !== $s3Key) {
                        $this->fileStore->delete(s3Path: $oldS3Path);
                    }

                    $newDoc->setMimeType(mimeType: $oldDoc->getMimeType());
                    $newDoc->setS3FilePath(s3FilePath: $s3Key);
                    $newDoc->setCreatedAt(createdAt: \DateTime::createFromInterface(object: $oldDoc->getCreatedAt()));
                    $newDoc->setCreatedBy(createdBy: $oldDoc->getCreatedBy());
                    $newDoc->setModifiedAt(modifiedAt: $oldDoc->getModifiedAt() ? \DateTime::createFromInterface(object: $oldDoc->getModifiedAt()) : null);
                    $newDoc->setModifiedBy(modifiedBy: $oldDoc->getModifiedBy());

                    $mainDoc->setDocumentData(documentData: $newDoc);
                    $defaultManager->persist($newDoc);
                    $defaultManager->persist($mainDoc);

                    if ($isUpdate) {
                        ++$successfullyUpdated;
                    } else {
                        ++$successfullyCreated;
                    }

                    $processedInThisBatch[$uuid] = true;
                    $io->progressAdvance();
                } catch (\Exception $e) {
                    $io->error(message: 'Failed to process UUID: '.$uuid.' - '.$e->getMessage());
                    ++$skippedFailed;
                    $io->progressAdvance();
                } finally {
                    if (is_resource(value: $fileStream)) {
                        fclose(stream: $fileStream);
                    }
                    $oldFileManager->detach($oldDoc);
                }
            }

            $defaultManager->flush();
            $defaultManager->clear();
            $oldFileManager->clear();
            gc_collect_cycles();

            $offset += $docCountInBatch;
        }
        $endTime = new \DateTime();
        $duration = $endTime->diff(targetObject: $startTime);
        $io->progressFinish();
        $io->success(message: 'Migration complete!');
        $totalSkipped = $skippedMissingParent
            + $skippedNoStream
            + $skippedFailed;
        $io->section(message: 'Migration Summary');
        $io->table(
            headers: ['Metric', 'Value'],
            rows: [
                ['Start Time', $startTime->format(format: 'Y-m-d H:i:s')],
                ['End Time', $endTime->format(format: 'Y-m-d H:i:s')],
                ['Total Duration', $duration->format(format: '%Hh %Im %Ss')],
                ['---', '---'],
                ['Total Documents Found (in old DB)', $totalDocuments],
                ['New Files Created', $successfullyCreated],
                ['Existing Files Updated (Overwritten)', $successfullyUpdated],
                ['Total Skipped', $totalSkipped],
            ]
        );
        if ($totalSkipped > 0) {
            $io->section(message: 'Skipped Reason Breakdown');
            $io->table(
                headers: ['Reason', 'Count'],
                rows: [
                    ['Missing Parent Document', $skippedMissingParent],
                    ['Source DB Duplicate (Older File)', $skippedBatchDuplicate],
                    ['No File Stream', $skippedNoStream],
                    ['Migration Error', $skippedFailed],
                ]
            );
        }

        return Command::SUCCESS;
    }
}
