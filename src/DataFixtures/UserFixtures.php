<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\DependencyInjection\ParameterBag\ContainerBagInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

/**
 * Class UserFixtures.
 */
class UserFixtures extends Fixture implements FixtureGroupInterface
{
    private readonly string $passwd;

    public const USER_REFERENCE = 'user';

    /**
     * AppFixtures constructor.
     */
    public function __construct(private UserPasswordHasherInterface $passwordHasher, ContainerBagInterface $params)
    {
        $this->passwordHasher = $passwordHasher;
        $this->passwd = $params->get('app.fixtures_pass');
    }

    public function load(ObjectManager $manager): void
    {
        $user = new User();
        $user->setEmail(email: '<EMAIL>');
        $user->setRoles(roles: ['ROLE_ADMIN']);
        $user->setPassword(password: $this->passwordHasher->hashPassword(
            $user,
            $this->passwd
        ));
        $manager->persist($user);

        $user = new User();
        $user->setEmail(email: '<EMAIL>');
        $user->setRoles(roles: ['ROLE_MANAGER']);
        $user->setPassword(password: $this->passwordHasher->hashPassword(
            $user,
            $this->passwd
        ));
        $manager->persist($user);

        $user = new User();
        $user->setEmail(email: '<EMAIL>');
        $user->setRoles(roles: ['ROLE_USER']);
        $user->setPassword(password: $this->passwordHasher->hashPassword(
            $user,
            $this->passwd
        ));
        $manager->persist($user);

        $manager->flush();

        // other fixtures can get this object using the UserFixtures::USER_REFERENCE constant
        $this->addReference(name: self::USER_REFERENCE, object: $user);
    }

    public static function getGroups(): array
    {
        return ['user'];
    }
}
