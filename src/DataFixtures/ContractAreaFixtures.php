<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\ContractArea;
use App\Entity\Main\State;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class ContractAreaFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    private ?ObjectManager $manager = null;

    public const CONTRACT_AREA_REFERENCE = 'contractArea';

    public const CONTRACT_AREA_REFERENCE_2 = 'contractArea2';

    public function load(ObjectManager $manager): void
    {
        $this->manager = $manager;

        $area = $this->createContractArea(
            name: 'BB124-DSZ2019LS-008', contractAreaId: '00AAAAAA01171EEB8C948CCF2437E35E',
            validForm: new \DateTime(datetime: '2019-01-01'), validTo: new \DateTime(datetime: '9999-12-31'),
            state: $this->getReference(name: StateFixtures::STATE_REFERENCE_BB, class: State::class)
        );

        $this->addReference(name: self::CONTRACT_AREA_REFERENCE, object: $area);

        $area2 = $this->createContractArea(
            name: 'NW002-2008P0-141', contractAreaId: '00E0180136721EDB8C88598CB3EFEDC7',
            validForm: new \DateTime(datetime: '2019-01-01'), validTo: new \DateTime(datetime: '9999-12-31'),
            state: $this->getReference(name: StateFixtures::STATE_REFERENCE_NW, class: State::class)
        );

        // other fixtures can get this object using the ContractAreaFixtures::CONTRACT_AREA_REFERENCE constant
        $this->addReference(name: self::CONTRACT_AREA_REFERENCE_2, object: $area2);

        $manager->flush();
    }

    protected function createContractArea(string $name, string $contractAreaId, \DateTime $validForm, \DateTime $validTo, State $state): ContractArea
    {
        $area = new ContractArea();
        $area->setName(name: $name);
        $area->setContractAreaId(contractAreaId: $contractAreaId);
        $area->setValidFrom(validFrom: $validForm);
        $area->setValidTo(validTo: $validTo);
        $area->setState(state: $state);

        $this->manager->persist($area);

        return $area;
    }

    public function getDependencies(): array
    {
        return [
            StateFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['contract', 'area', 'collectRegistration'];
    }
}
