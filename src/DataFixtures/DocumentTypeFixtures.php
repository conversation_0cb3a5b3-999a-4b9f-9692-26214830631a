<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\DocumentType;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;
use <PERSON>\Uuid\Uuid;

class DocumentTypeFixtures extends Fixture implements FixtureGroupInterface
{
    public const DOCUMENT_TYPE_REFERENCE = 'documentType';

    public function load(ObjectManager $manager): void
    {
        $documentType = new DocumentType();

        $documentType->setUuid(uuid: Uuid::uuid1());

        $documentType->setName(name: 'Wiegeschein');

        $manager->persist($documentType);

        $this->setReference(name: self::DOCUMENT_TYPE_REFERENCE, object: $documentType);

        $manager->flush();
    }

    public static function getGroups(): array
    {
        return ['document'];
    }
}
