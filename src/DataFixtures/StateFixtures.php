<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\State;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Persistence\ObjectManager;

/**
 * Class UserFixtures.
 */
class StateFixtures extends Fixture implements FixtureGroupInterface
{
    public const STATE_REFERENCE = 'state';
    public const STATE_REFERENCE_BB = 'state_bb';
    public const STATE_REFERENCE_NW = 'state_nw';
    public const STATE_REFERENCE_ST = 'state_st';

    public function load(ObjectManager $manager): void
    {
        $state = new State();
        $state->setName(name: 'Bayern');
        $state->setShortName(shortName: 'BY');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Baden-Württemberg');
        $state->setShortName(shortName: 'BW');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Berlin');
        $state->setShortName(shortName: 'BE');
        $manager->persist($state);

        $state_bb = new State();
        $state_bb->setName(name: 'Brandenburg');
        $state_bb->setShortName(shortName: 'BB');
        $manager->persist($state_bb);

        $state = new State();
        $state->setName(name: 'Freie Hansestadt Bremen');
        $state->setShortName(shortName: 'HB');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Hamburg');
        $state->setShortName(shortName: 'HH');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Hessen');
        $state->setShortName(shortName: 'HE');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Mecklenburg-Vorpommern');
        $state->setShortName(shortName: 'MV');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Niedersachsen');
        $state->setShortName(shortName: 'NS');
        $manager->persist($state);

        $state_nw = new State();
        $state_nw->setName(name: 'Nordrhein-Westfalen');
        $state_nw->setShortName(shortName: 'NW');
        $manager->persist($state_nw);

        $state = new State();
        $state->setName(name: 'Rheinland-Pfalz');
        $state->setShortName(shortName: 'RP');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Saarland');
        $state->setShortName(shortName: 'SL');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Sachsen');
        $state->setShortName(shortName: 'SN');
        $manager->persist($state);

        $state_st = new State();
        $state_st->setName(name: 'Sachsen-Anhalt');
        $state_st->setShortName(shortName: 'ST');
        $manager->persist($state_st);

        $state = new State();
        $state->setName(name: 'Schleswig-Holstein');
        $state->setShortName(shortName: 'SH');
        $manager->persist($state);

        $state = new State();
        $state->setName(name: 'Thüringen');
        $state->setShortName(shortName: 'TH');
        $manager->persist($state);

        $manager->flush();

        $this->addReference(name: self::STATE_REFERENCE, object: $state);
        $this->addReference(name: self::STATE_REFERENCE_BB, object: $state_bb);
        $this->addReference(name: self::STATE_REFERENCE_NW, object: $state_nw);
        $this->addReference(name: self::STATE_REFERENCE_ST, object: $state_st);
    }

    public static function getGroups(): array
    {
        return ['area', 'collectRegistration'];
    }
}
