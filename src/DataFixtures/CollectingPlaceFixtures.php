<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\State;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

/**
 * Class UserFixtures.
 */
class CollectingPlaceFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    private ?ObjectManager $manager = null;

    public const COLLECTINGPLACE_REFERENCE = 'CollectingPlace';

    public const COLLECTINGPLACE_REFERENCE_2 = 'CollectingPlace2';

    public const COLLECTINGPLACE_REFERENCE_3 = 'CollectingPlace3';

    public const COLLECTINGPLACE_REFERENCE_4 = 'CollectingPlace4';

    public function load(ObjectManager $manager): void
    {
        $this->manager = $manager;

        $collectingPlace = $this->createCollectingPlace(sapId: '5600', name: '<PERSON><PERSON><PERSON>', street: 'Almerfeldweg', housnmb: '55 - 61', plc: '59929', city: 'Brilon', state: 'BB', district: 'NW', country: 'DE');
        $collectingPlace_2 = $this->createCollectingPlace(sapId: '21530', name: 'REMONDIS Olpe GmbH', street: 'Unterm Breloh', housnmb: '46', plc: '59759', city: 'Arnsberg', state: 'NW', district: 'NW', country: 'DE');
        $collectingPlace_3 = $this->createCollectingPlace(sapId: '4700', name: 'Tester GmbH', street: 'Probenstraße', housnmb: '3', plc: '63795', city: 'Testerhausen', state: 'NW', district: 'NW', country: 'DE');
        $collectingPlace_4 = $this->createCollectingPlace(sapId: '5879', name: 'Versuchs GmbH', street: 'Musterweg', housnmb: '21', plc: '59759', city: 'Beispielstadt', state: 'NW', district: 'NW', country: 'DE');

        $this->addReference(name: self::COLLECTINGPLACE_REFERENCE, object: $collectingPlace);

        $this->addReference(name: self::COLLECTINGPLACE_REFERENCE_2, object: $collectingPlace_2);

        $this->addReference(name: self::COLLECTINGPLACE_REFERENCE_3, object: $collectingPlace_3);

        $this->addReference(name: self::COLLECTINGPLACE_REFERENCE_4, object: $collectingPlace_4);
    }

    public function createCollectingPlace(string $sapId, string $name, string $street, string $housnmb, string $plc, string $city, string $state, ?string $district = null, ?string $country = null): CollectingPlace
    {
        $collectingPlace = new CollectingPlace();

        $collectingPlace->setCollectingPlaceId(collectingPlaceId: $sapId);
        $collectingPlace->setName1(name1: $name);
        $collectingPlace->setName2(name2: null);
        $collectingPlace->setStreet(street: $street);
        $collectingPlace->setHouseNumber(houseNumber: $housnmb);
        $collectingPlace->setPostalCode(postalCode: $plc);
        $collectingPlace->setCity(city: $city);
        $collectingPlace->setDistrict(district: $district);
        $collectingPlace->setCountry(country: $country);
        $collectingPlace->setDsdId(dsdId: 'abc');

        switch ($state) {
            case 'BB':
                $collectingPlace->setState(state: $this->getReference(name: StateFixtures::STATE_REFERENCE_BB, class: State::class));
                break;
            case 'NW':
                $collectingPlace->setState(state: $this->getReference(name: StateFixtures::STATE_REFERENCE_NW, class: State::class));
                break;
            case 'SA':
                $collectingPlace->setState(state: $this->getReference(name: StateFixtures::STATE_REFERENCE_ST, class: State::class));
                break;
        }

        $this->manager->persist($collectingPlace);
        $this->manager->flush();

        return $collectingPlace;
    }

    public function getDependencies(): array
    {
        return [
            StateFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['orderSupply', 'collectingPlace', 'collectRegistration'];
    }
}
