<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\Contract;
use App\Entity\Main\SystemProvider;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\Console\Output\ConsoleOutput;

class SystemProviderFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    private ?ObjectManager $manager = null;

    private readonly ConsoleOutput $out;

    public const SYSTEMPROVIDER_REFERENCE = 'SystemProvider';

    /**
     * CollectRegistrationFixture constructor.
     */
    public function __construct()
    {
        $this->out = new ConsoleOutput();
    }

    public function load(ObjectManager $manager): void
    {
        /** @var Contract $contract */
        $contract = $this->getReference(name: ContractFixtures::CONTRACT_REFERENCE, class: Contract::class);

        $this->manager = $manager;

        $this->out->writeln(messages: 'Start creating collect registrations');

        $provider = new SystemProvider();
        $provider->setName(name: 'DSD Fix')
                 ->setSystemProviderId(systemProviderId: 'DSD FIX')
                 ->setMaterialId(materialId: '12345678')
                 ->setDsdFractionId(dsdFractionId: '1234')
                 ->setDsdFractionName(dsdFractionName: 'DSD FIX')
                 ->setTransport(transport: true)
                 ->setMonoFraction(monoFraction: false);
        $this->manager->persist($provider);
        $contract->addSystemProvider(systemProvider: $provider);

        $provider = new SystemProvider();
        $provider->setName(name: 'DSD Vario')
            ->setSystemProviderId(systemProviderId: 'DSD Vario')
            ->setMaterialId(materialId: '23456789')
            ->setDsdFractionId(dsdFractionId: '2345')
             ->setDsdFractionName(dsdFractionName: 'DSD Vario')
             ->setTransport(transport: true)
             ->setMonoFraction(monoFraction: false);
        $this->manager->persist($provider);
        $contract->addSystemProvider(systemProvider: $provider);

        $provider = new SystemProvider();
        $provider->setName(name: 'Interseroh')
            ->setSystemProviderId(systemProviderId: 'DSI')
            ->setMaterialId(materialId: '34567890')
            ->setDsdFractionId(dsdFractionId: '3456')
             ->setDsdFractionName(dsdFractionName: 'DSI')
             ->setTransport(transport: true)
             ->setMonoFraction(monoFraction: false);
        $this->manager->persist($provider);
        $contract->addSystemProvider(systemProvider: $provider);

        $this->addReference(name: self::SYSTEMPROVIDER_REFERENCE, object: $provider);

        $this->manager->persist($contract);

        $provider = new SystemProvider();
        $provider->setName(name: 'Veolia')
            ->setSystemProviderId(systemProviderId: 'Veolia')
            ->setMaterialId(materialId: '45678901')
            ->setDsdFractionId(dsdFractionId: '4567')
             ->setDsdFractionName(dsdFractionName: 'Veolia')
             ->setTransport(transport: false)
             ->setMonoFraction(monoFraction: true);
        $this->manager->persist($provider);

        $provider = new SystemProvider();
        $provider->setName(name: 'Zentek')
            ->setSystemProviderId(systemProviderId: 'ZENTEK')
            ->setMaterialId(materialId: '56789012')
            ->setDsdFractionId(dsdFractionId: '5678')
             ->setDsdFractionName(dsdFractionName: 'ZENTEK')
             ->setTransport(transport: false)
             ->setMonoFraction(monoFraction: false);
        $this->manager->persist($provider);

        $this->manager->flush();

        $this->out->writeln(messages: 'Creating SystemProvider Done.');
    }

    public function getDependencies(): array
    {
        return [
            ContractFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['collectRegistration'];
    }
}
