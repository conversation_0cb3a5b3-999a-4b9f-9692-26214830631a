<?php

declare(strict_types=1);

namespace App\DataFixtures;

use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class ContractAreaValidityFixtures extends Fixture implements FixtureGroupInterface, DependentFixtureInterface
{
    public const CONTRACT_AREA_VALIDITY_REFERENCE = 'contractAreaValidity';

    public function load(ObjectManager $manager): void
    {
        $validity = new ContractAreaValidity();
        $validity->setValidityId(validityId: '00E0180136721EDB8C8837484C2CCDA1');
        $validity->setContractArea(
            contractArea: $this->getReference(name: ContractAreaFixtures::CONTRACT_AREA_REFERENCE, class: ContractArea::class)
        );
        $validity->setValidFrom(validFrom: new \DateTime(datetime: '12.10.2020'));
        $validity->setValidTo(validTo: new \DateTime(datetime: '01.03.2022'));
        $validity->setAmountDay(amountDay: 2);
        $validity->setAmountWeek(amountWeek: 12);
        $manager->persist($validity);
        $manager->flush();

        // other fixtures can get this object using the ContractAreaValidityFixtures::CONTRACT_AREA_VALIDITY_REFERENCE constant
        $this->addReference(name: self::CONTRACT_AREA_VALIDITY_REFERENCE, object: $validity);
    }

    public function getDependencies(): array
    {
        return [
            StateFixtures::class,
            ContractAreaFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['contract', 'area'];
    }
}
