<?php

declare(strict_types=1);

namespace App\Form\Type;

use App\Entity\Main\DocumentType;
use App\Repository\Main\DocumentTypeRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\File;

class DocumentUploadType extends AbstractType
{
    public function __construct(private readonly DocumentTypeRepository $documentTypeRepo)
    {
    }

    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('file', FileType::class, [
                'label' => 'Document',

                'attr' => ['class' => 'block rounded border-2 border-pzgrey w-full px-4 py-[14px]'],

                // unmapped means that this field is not associated to any entity property
                'mapped' => false,

                // make it optional so you don't have to re-upload the file every time you edit the Product details
                'required' => false,

                // unmapped fields can't define their validation using annotations in the associated entity, so use the PHP constraint classes
                'constraints' => [
                    new File(maxSize: '10000k', mimeTypes: [
                        'application/pdf',
                    ], mimeTypesMessage: 'Please upload pdf file format!'),
                ],
            ])

            ->add('documentType', EntityType::class, [
                'class' => DocumentType::class,
                'label' => 'Art',
                'choice_label' => 'name',
                'choices' => $this->documentTypeRepo->findAll(),
                'attr' => ['class' => 'block rounded border-2 border-pzgrey w-full px-4 py-[14px]'],
            ])

            ->add('submit', SubmitType::class,
                [
                    'label' => 'Hochladen',
                    'attr' => ['class' => 'inline-block border-2 border-pzgreen rounded-md px-4 py-[14px] text-pzgreen font-normal text-xl hover:shadow-lg'],
                ]);
    }
}
