<?php

declare(strict_types=1);

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;

class PasswordSendType extends AbstractType
{
    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            // ->add('password_1', PasswordType::class)
            // ->add('password_2', PasswordType::class)
            ->add('submit', SubmitType::class, ['label' => 'Okay'])
        ;
    }
}
