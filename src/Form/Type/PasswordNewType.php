<?php

declare(strict_types=1);

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Regex;

class PasswordNewType extends AbstractType
{
    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('password_1', PasswordType::class,
                ['constraints' => new Regex(pattern: '/(?=^.{8,}$)((?=.*\d)|(?=.*[@$.+_!#%&]))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/')]
            )
            ->add('password_2', PasswordType::class,
                ['constraints' => new Regex(pattern: '/(?=^.{8,}$)((?=.*\d)|(?=.*[@$.+_!#%&]))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/')]
            )
            ->add('submit', SubmitType::class,
                ['label' => 'Okay']
            );
    }
}
