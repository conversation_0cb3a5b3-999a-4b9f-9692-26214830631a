<?php

declare(strict_types=1);

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ExportType extends AbstractType
{
    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('collectingPlace', HiddenType::class, [
                'data' => $options['collectingPlace'],
            ])
            ->add('unloadingPoint', HiddenType::class, [
                'data' => $options['unloadingPoint'],
            ])
            ->add('dateFrom', DateType::class, [
                'widget' => 'single_text',
                // this is actually the default format for single_text
                'format' => 'yyyy-MM-dd',
                'data' => $options['dateFrom'],
                'required' => false,
                'attr' => ['style' => 'display:none;'],
            ])
            ->add('dateTo', DateType::class, [
                'widget' => 'single_text',
                // this is actually the default format for single_text
                'format' => 'yyyy-MM-dd',
                'data' => $options['dateTo'],
                'required' => false,
                'attr' => ['style' => 'display:none;'],
            ])
            ->add('orderByAndOrder', HiddenType::class, [
                'data' => $options['orderByAndOrder'],
                'required' => false,
            ])
            // ->add('submit', SubmitType::class, ['label' => 'Export'] )
        ;
    }

    // Only option keys in array setDefaults are allowed
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            defaults: [
                'dateFrom' => null,
                'dateTo' => null,
                'collectingPlace' => null,
                'unloadingPoint' => null,
                'orderByAndOrder' => null,
            ]);
    }
}
