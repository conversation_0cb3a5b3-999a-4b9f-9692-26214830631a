<?php

declare(strict_types=1);

namespace App\Form\Type;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Class PasswordResetType.
 *
 * @psalm-suppress MissingTemplateParam
 */
class LocaleEditType extends AbstractType
{
    private readonly UserInterface $user;

    public function __construct(Security $security)
    {
        $this->user = $security->getUser();
    }

    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $locale = method_exists(object_or_class: $this->user, method: 'getLocale') ? $this->user->getLocale() : 'de';

        $builder
            ->add('locale', ChoiceType::class, [
                'choices' => ['locale.de' => 'de', 'locale.en' => 'en'],
                'data' => $locale,
            ])
            ->add('submit', SubmitType::class, ['label' => 'Save'])
        ;
    }
}
