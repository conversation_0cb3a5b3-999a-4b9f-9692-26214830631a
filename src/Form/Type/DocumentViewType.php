<?php

declare(strict_types=1);

namespace App\Form\Type;

use App\Entity\Main\Contract;
use App\Services\DateTimeHelper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DocumentViewType extends AbstractType
{
    /**
     * CollectingEntrenceType constructor.
     */
    public function __construct(protected DateTimeHelper $dateTimeHelper)
    {
    }

    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('contract', EntityType::class, [
                'class' => Contract::class,
                'required' => false,
                'choice_label' => fn ($contract): string => $contract->getContractId().' - '.$contract->getContractNumber().' ('.$contract->getDocuments().')',
                'choices' => $options['contract'],
            ])
            ->add('submit', SubmitType::class, ['label' => 'Display'])
        ;
    }

    // Only option keys in array setDefaults are allowed
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(defaults: [
            'contract' => null,
        ]);
    }
}
