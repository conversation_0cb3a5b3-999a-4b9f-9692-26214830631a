<?php

declare(strict_types=1);

namespace App\Form\Type;

use App\Entity\Main\UnloadingPoint;
use App\Services\DateTimeHelper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class OrderReportType extends AbstractType
{
    /**
     * CollectingEntrenceType constructor.
     */
    public function __construct(protected DateTimeHelper $dateTimeHelper)
    {
    }

    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('unloadingPoint', EntityType::class, [
                'class' => UnloadingPoint::class,
                'required' => false,
                'choice_label' => fn ($unloadingPoint): string => $unloadingPoint->getDsdId().' - '.$unloadingPoint->getName1().' ('.$unloadingPoint->getCity().')',
                'choices' => $options['unloadingPoint'],
            ])
            ->add('dateFrom', DateType::class, [
                'widget' => 'single_text',
                // this is actually the default format for single_text
                'format' => 'yyyy-MM-dd',
                'data' => $options['dateFrom'],
                'required' => true,
                'constraints' => [new NotBlank()],
            ])
            ->add('dateTo', DateType::class, [
                'widget' => 'single_text',
                // this is actually the default format for single_text
                'format' => 'yyyy-MM-dd',
                'data' => $options['dateTo'],
                'required' => true,
                'constraints' => [new NotBlank()],
            ])
            ->add('submit', SubmitType::class, ['label' => 'Display'])
        ;
    }

    // Only option keys in array setDefaults are allowed
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(defaults: [
            'unloadingPoint' => null,
            'dateFrom' => null,
            'dateTo' => null,
        ]);
    }
}
