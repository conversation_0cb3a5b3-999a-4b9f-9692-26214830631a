<?php

declare(strict_types=1);

namespace App\Form\Type;

use App\Entity\Main\SystemProvider;
use App\Services\DateTimeHelper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OrderCollectingType extends AbstractType
{
    /**
     * CollectingEntrenceType constructor.
     */
    public function __construct(protected DateTimeHelper $dateTimeHelper)
    {
    }

    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('systemProvider', EntityType::class, [
                'class' => SystemProvider::class,
                'choice_label' => 'name',
                'choice_value' => 'uuid',
                'choices' => $options['systemProvider'],
                'required' => true,
            ])
            ->add('disposalNumber', TextType::class, [
                'data' => $options['disposalNumber'],
            ])
            ->add('driverMessage', TextareaType::class, [
                'required' => false,
                'attr' => ['maxLength' => 50],
            ])
            // ->add('dispoMessage', TextareaType::class, [
            //    'required' => false,
            //    'attr' => ['maxLength' => 50]
            // ])
            ->add('dateTime', HiddenType::class, [
                'data' => '2020-09-28',
            ])
            ->add('collectingPlace', HiddenType::class, [
                'data' => $options['collectingPlace'],
            ])
            ->add('collectRegistration', HiddenType::class, [
                'required' => false,
            ])

            // ->add('submit', SubmitType::class, ['label' => 'Übernehmen'] ) // added in twig - easier to create
        ;
    }

    // Only option keys in array setDefaults are allowed
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            defaults: [
                'systemProvider' => null,
                'disposalNumber' => null,
                'collectingPlace' => null,
            ]);
    }
}
