<?php

declare(strict_types=1);

namespace App\Form\Type;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Services\DateTimeHelper;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CollectingEntrenceType extends AbstractType
{
    /**
     * CollectingEntrenceType constructor.
     */
    public function __construct(protected DateTimeHelper $dateTimeHelper)
    {
    }

    /**
     * @throws \Exception
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('collectingPlace', EntityType::class, [
                'class' => CollectingPlace::class,
                'required' => true,
                'choice_label' => fn ($collectingPlace): string => $collectingPlace->getDsdId().' - '.$collectingPlace->getName1().' ('.$collectingPlace->getCity().')',
                'choice_value' => 'uuid',
                'choices' => $options['collectingPlace'],
            ])
            ->add('contract_contractArea', EntityType::class, [
                'class' => ContractArea::class,
                'required' => true,
                'choice_label' => fn ($area) => $area->getName(),
                'choice_value' => 'uuid',
                'choices' => $options['areaList'],
            ])
            ->add('calendarWeeks', ChoiceType::class, [
                'choices' => $this->dateTimeHelper->getInfoStringsWeek(count: 6),
                'data' => (date(format: 'N') >= 4 ? 1 : 0),
            ])
            ->add('submit', SubmitType::class, ['label' => 'Continue'])
        ;
    }

    // Only option keys in array setDefaults are allowed
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(defaults: [
            'collectingPlace' => null,
            'areaList' => null,
        ]);
    }
}
