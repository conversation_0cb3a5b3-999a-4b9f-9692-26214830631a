<?php

declare(strict_types=1);

namespace App\Form\Dto;

use App\Dto\DocumentDto;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DocumentDtoFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('number', TextType::class, [
                'label' => 'project.field.customer',
                'attr' => [
                    'placeholder' => 'project.placeholder.customer',
                ],
                'disabled' => 'edit' === $options['mode'] || 'view' === $options['mode'],
            ])
            ->add('amount', TextType::class, [
                'label' => 'project.field.name',
                'attr' => [
                    'placeholder' => 'project.placeholder.name',
                ],
                'required' => true,
            ])
            ->add('unit', TextType::class, [
                'label' => 'project.field.description',
                'attr' => [
                    'placeholder' => 'project.placeholder.description',
                ],
            ])
            ->add('visible', TextType::class, [
                'label' => 'project.field.startdate',
                'attr' => [
                    'placeholder' => 'project.placeholder.startdate',
                ],
            ])
            ->add('active', TextType::class, [
                'label' => 'project.field.enddate',
                'attr' => [
                    'placeholder' => 'project.placeholder.enddate',
                ],
                'required' => false,
            ])
            ->add('documentType', TextType::class, [
                'label' => 'project.field.appusage',
                'attr' => [
                    'placeholder' => 'project.placeholder.appusage',
                ],
                'required' => false,
            ])
            ->add('contract', TextType::class, [
                'label' => 'project.field.appusage',
                'attr' => [
                    'placeholder' => 'project.placeholder.appusage',
                ],
                'required' => false,
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(defaults: [
            'customerChoices' => [],
            'mode' => 'view',
            'data_class' => DocumentDto::class,
        ]);
    }
}
