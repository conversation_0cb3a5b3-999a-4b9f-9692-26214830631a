<?php

declare(strict_types=1);

namespace App\Form\Dto;

use App\Dto\UserDto;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class UserDtoFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('email', EmailType::class, [
                'required' => true,
                'disabled' => 'edit' === $options['mode'] || 'view' === $options['mode'],
                'label' => 'email',
                'attr' => [
                    'placeholder' => '<EMAIL>',
                ],
            ])
            ->add('locale', ChoiceType::class, [
                'choices' => ['locale.de' => 'de', 'locale.en' => 'en'],
                'label' => 'locale',
            ])
            ->add('roles', ChoiceType::class, [
                'label' => 'user.field.roles',
                'attr' => [
                    'placeholder' => 'user.placeholder.roles',
                    'class' => 'grid grid-cols-2 items-center',
                ],
                'disabled' => $options['roleAssignmentDisabled'],
                'multiple' => true,
                'expanded' => true,
                'choices' => $options['roleChoices'],
                'choice_attr' => fn ($choice, $key, $value): array => ['class' => 'border-2 border-pzgrey rounded mr-2 justify-self-end'],
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(defaults: [
            'mode' => 'view',
            'data_class' => UserDto::class,
            'roleChoices' => [],
            'roleAssignmentDisabled' => true,
        ]);
    }
}
