<?php

declare(strict_types=1);

namespace App\Controller\Management;

use App\Dto\CollectingPlaceDto;
use App\Dto\ContractAreaCollectingPlaceDto;
use App\Dto\ContractAreaDto;
use App\Dto\ContractAreaUserDto;
use App\Dto\ContractAreaValidityDto;
use App\Dto\ContractDto;
use App\Dto\UserDto;
use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use App\Entity\Main\User;
use App\Entity\Main\UserAccess;
use App\Form\Dto\UserDtoFormType;
use App\Repository\Main\ContractAreaRepository;
use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use App\Services\ContractAreaHelper;
use App\Services\Mailer;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends BaseCrudController<ContractArea>
 */
class ContractAreaController extends BaseCrudController
{
    protected SessionInterface $session;

    public function __construct(
        protected EntityManagerInterface $manager,
        RequestStack $requestStack,
        protected AccessHelper $accessHelper,
        protected ContractAreaHelper $contractAreaHelper,
        CollectingPlaceHelper $collectingPlaceHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        ContractAreaRepository $repository,
        private readonly Mailer $mailer,
        private readonly TranslatorInterface $translator,
    ) {
        parent::__construct($repository, $serializer, $manager, $accessHelper, $contractAreaHelper, $collectingPlaceHelper, 'ContractAreaController', 'management-neu/contractarea/list.html.twig', 'management-neu/contractarea/details.html.twig');
        $this->session = $requestStack->getSession();
    }

    protected function mapEntityToDto(object $entity, string|Request $request, ?object $contractArea = null, ?object $collectingPlace = null): object
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, $request, [
            'contractArea' => $contractArea,
            'collectingPlace' => $collectingPlace,
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity(object $dto, object $entity, Request $request): object
    {
        /** @phpstan-ignore-next-line */
        return $this->autoMapper->mapToObject($dto, $entity, []);
    }

    /**
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getFormOptions(object $dto, string $mode, array $options): array
    {
        $roleChoices = [
            $this->translator->trans('roles.ROLE_ORDER') => 'ROLE_ORDER',
            $this->translator->trans('roles.ROLE_DOCUMENTS') => 'ROLE_DOCUMENTS',
        ];

        switch ($mode) {
            case BaseCrudController::FORM_MODE_ADD:
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_ADD, 'method' => Request::METHOD_POST]);
                $options['roleChoices'] = $roleChoices;
                $options['roleAssignmentDisabled'] = !$this->isGranted(attribute: 'ROLE_MANAGER');

                break;
            case BaseCrudController::FORM_MODE_EDIT:
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_EDIT, 'method' => Request::METHOD_PUT]);

                break;
            case BaseCrudController::FORM_MODE_DELETE:
                $options = array_merge($options, ['mode' => BaseCrudController::FORM_MODE_DELETE, 'method' => Request::METHOD_DELETE]);

                break;
        }

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getContractAreaOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'name' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.contract_area')],
            'state' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.state')],
            'validFrom' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.validFrom')],
            'validTo' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.validTo')],
            'countCollectingPlace' => ['visible' => true, 'label' => $this->translator->trans('count_collecting_place')],
            'countUser' => ['visible' => true, 'label' => $this->translator->trans('count_user')],
            'detailLink' => ['visible' => false, 'label' => 'detailLink'],
        ];

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getUserOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'email' => ['visible' => true, 'label' => $this->translator->trans('email')],
            'contractArea' => ['visible' => false, 'label' => 'contractArea'],
        ];

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getContractOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'contractNumber' => ['visible' => true, 'label' => $this->translator->trans('sap_contractNumber')],
            'collectingPlace' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.collecting_place')],
            'unloadingPoint' => ['visible' => true, 'label' => $this->translator->trans('unloading_point')],
        ];

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getCollectingPlaceOptions(array $entityList, array $options, bool $details = false): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'dsdId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.dsdid')],
            'esaId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.esaid')],
            'name' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.name')],
            'street' => ['visible' => true, 'label' => $this->translator->trans('street')],
            'houseNumber' => ['visible' => true, 'label' => $this->translator->trans('house_number')],
            'postalCode' => ['visible' => true, 'label' => $this->translator->trans('postal_code')],
            'city' => ['visible' => true, 'label' => $this->translator->trans('city')],
            'district' => ['visible' => true, 'label' => $this->translator->trans('district')],
            'countUser' => ['visible' => !$details, 'label' => $this->translator->trans('count_user')],
            'detailLink' => ['visible' => false, 'label' => 'detailLink'],
        ];

        return $options;
    }

    /**
     * @param array<int, object>   $entityList
     * @param array<string, mixed> $options
     *
     * @return array<string, mixed>
     */
    protected function getContractAreaValidityOptions(array $entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'validFrom' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.validFrom')],
            'validTo' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.validTo')],
            'amountDay' => ['visible' => true, 'label' => $this->translator->trans('amount_day')],
            'amountWeek' => ['visible' => true, 'label' => $this->translator->trans('amount_week')],
        ];

        return $options;
    }

    #[Route(path: '/management/contractArea', name: 'management_contractArea')]
    public function index(): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        $self = $this;
        $items = $this->manager->getRepository(ContractArea::class)->findAll();
        $mappedList = array_map(callback: fn (ContractArea $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractAreaDto::class), array: $items);

        return $this->render(view: 'management-neu/contractarea/list.html.twig', parameters: $this->getContractAreaOptions(entityList: $items, options: [
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/contractArea/{uuid}', name: 'management_contractArea_details')]
    public function details(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->redirectToRoute(route: 'management_contractArea_details_contingents', parameters: ['uuid' => $uuid]);
    }

    #[Route(path: '/management/contractArea/{uuid}/contingents', name: 'management_contractArea_details_contingents')]
    public function detailsContingents(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->manager->getRepository(ContractAreaValidity::class)->findBy(criteria: ['contractArea' => $contractArea]);

        $mappedList = array_map(callback: fn (ContractAreaValidity $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractAreaValidityDto::class, contractArea: $contractArea), array: $items);

        return $this->render(view: 'management-neu/contractarea/details.html.twig', parameters: $this->getContractAreaValidityOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_contractArea'),
            'contractArea' => $contractArea,
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/contractArea/{uuid}/collectingplaces', name: 'management_contractArea_details_collectingplaces')]
    public function detailsCollectingplaces(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->contractAreaHelper->getCollectingPlaceList(contractArea: $contractArea, locked: true);

        $mappedList = array_map(callback: fn (CollectingPlace $entity): object => $self->mapEntityToDto(entity: $entity, request: CollectingPlaceDto::class, contractArea: $contractArea), array: $items);

        return $this->render(view: 'management-neu/contractarea/details.html.twig', parameters: $this->getCollectingPlaceOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_contractArea'),
            'contractArea' => $contractArea,
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ], details: true));
    }

    #[Route(path: '/management/contractArea/{uuid}/contracts', name: 'management_contractArea_details_contracts')]
    public function detailsContracts(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->manager->getRepository(Contract::class)->findBy(criteria: ['contractArea' => $contractArea]);

        $mappedList = array_map(callback: fn (Contract $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractDto::class, contractArea: $contractArea), array: $items);

        return $this->render(view: 'management-neu/contractarea/details.html.twig', parameters: $this->getContractOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_contractArea'),
            'contractArea' => $contractArea,
            'list' => $this->serializer->serialize($mappedList, 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/users', name: 'management_contractArea_permission_users', methods: ['GET', 'POST', 'PUT'])]
    public function permissionUsers(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->manager->getRepository(User::class)->findAll();

        $mappedList = array_map(callback: fn (User $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractAreaUserDto::class, contractArea: $contractArea), array: $items);

        $newDto = $this->mapEntityToDto(entity: new User(), request: UserDto::class);

        $editForm = $this->createForm(type: UserDtoFormType::class, data: $newDto, options: $this->getFormOptions(dto: $newDto, mode: BaseCrudController::FORM_MODE_ADD, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $routeParams['uuid'] = $uuid;
            $newEntity = $this->mapDtoToEntity(dto: $newDto, entity: new User(), request: $request);

            $entity = $this->manager->getRepository(User::class)->findOneBy(criteria: ['email' => $newEntity->getEmail()]);

            if ($entity instanceof User) {
                if (!$entity->getDeleted()) {
                    $routeParams['noty'] = 'warning';
                    $routeParams['message'] = 'Benutzer existiert bereits.';

                    return $this->redirectToRoute(route: 'management_contractArea_permission_users', parameters: $routeParams);
                }
                $newEntity = $entity;
                $newEntity->setDeleted(deleted: false);
                $newEntity->setResetPassword(resetPassword: true);
            }

            $this->manager->persist($newEntity);
            $this->manager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = 'Benutzer wurde erfolgreich angelegt.';

            if ($newEntity->getResetPassword()) {
                $this->mailer->sendPasswordReset(user: $newEntity, newUser: true);
            }

            return $this->redirectToRoute(route: 'management_contractArea_permission_users', parameters: $routeParams);
        }

        return $this->render(view: 'management-neu/contractarea/permission.html.twig', parameters: $this->getUserOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_contractArea'),
            'contractArea' => $contractArea,
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_DESC), 'json', ['groups' => ['list']]),
            'editForm' => $editForm->createView(),
        ]));
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/users/add', name: 'management_contractArea_users_add', methods: ['POST'])]
    public function permissionUsersAdd(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        /** @var ContractArea $contractArea */
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $collectingPlaces = $this->contractAreaHelper->getCollectingPlaceList(contractArea: $contractArea);
        $data = json_decode(json: $request->getContent(), associative: true);

        if (count(value: $data) > 0) {
            foreach ($data as $userUuid) {
                /** @var User $user */
                $user = $this->manager->getRepository(User::class)->findOneBy(criteria: ['uuid' => $userUuid]);

                foreach ($collectingPlaces as $collectingPlace) {
                    if (false === $this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $contractArea, collectingPlace: $collectingPlace)) {
                        /** @var UserAccess $userAccess */
                        $userAccess = new UserAccess();
                        $userAccess->setUser(user: $user);
                        $userAccess->setContractArea(contractArea: $contractArea);
                        $userAccess->setCollectingPlace(collectingPlace: $collectingPlace);
                        $this->manager->persist($userAccess);
                    }
                }
            }
            $this->manager->flush();
        }

        return new JsonResponse();
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/users/delete', name: 'management_contractArea_users_delete', methods: ['POST'])]
    public function permissionUsersDelete(string $uuid, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        /** @var ContractArea $contractArea */
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);

        $data = json_decode(json: $request->getContent(), associative: true);

        if (count(value: $data) > 0) {
            foreach ($data as $userUuid) {
                /** @var User $user */
                $user = $this->manager->getRepository(User::class)->findOneBy(criteria: ['uuid' => $userUuid]);

                if ($this->accessHelper->checkUserAccessForContractArea(user: $user, contractArea: $contractArea)) {
                    /** @var UserAccess $userAccess */
                    $userAccessList = $this->manager->getRepository(UserAccess::class)->findBy(criteria: ['user' => $user, 'contractArea' => $contractArea]);

                    foreach ($userAccessList as $userAccess) {
                        $contractArea->removeUserAccess(userAccess: $userAccess);
                        $this->manager->persist($contractArea);
                        $this->manager->remove($userAccess);
                    }
                }
            }
            $this->manager->flush();
        }

        return new JsonResponse();
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/collectingplaces', name: 'management_contractArea_permission_collectingplaces', methods: ['GET', 'PUT'])]
    public function permissionCollectingplaces(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->contractAreaHelper->getCollectingPlaceList(contractArea: $contractArea, locked: true);

        $mappedList = array_map(callback: fn (CollectingPlace $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractAreaCollectingPlaceDto::class, contractArea: $contractArea), array: $items);

        return $this->render(
            view: 'management-neu/contractarea/permission.html.twig', parameters: $this->getCollectingPlaceOptions(entityList: $items, options: [
                'backUrl' => $self->generateUrl(route: 'management_contractArea'),
                'contractArea' => $contractArea,
                'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
                'editForm' => $this->createForm(type: UserDtoFormType::class)->createView(),
            ]));
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/collectingplaces/{collectingPlace}/users', name: 'management_contractArea_permission_collectingplaces_users', methods: ['GET', 'PUT'])]
    public function permissionCollectingplaceUsers(string $uuid, string $collectingPlace): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->findOneBy(criteria: ['uuid' => $collectingPlace]);
        $items = $this->accessHelper->getUserList(contractArea: $contractArea);

        $mappedList = array_map(callback: fn (object $entity): object => $self->mapEntityToDto(entity: $entity, request: ContractAreaUserDto::class, contractArea: $contractArea, collectingPlace: $collectingPlace), array: $items);

        return $this->render(
            view: 'management-neu/contractarea/collectingplace.html.twig', parameters: $this->getUserOptions(entityList: $items, options: [
                'backUrl' => $self->generateUrl(route: 'management_contractArea_permission_collectingplaces', parameters: ['uuid' => $uuid]),
                'contractArea' => $contractArea,
                'collectingPlace' => $collectingPlace,
                'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_DESC), 'json', ['groups' => ['list']]),
                'editForm' => $this->createForm(type: UserDtoFormType::class)->createView(),
            ]));
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/collectingplaces/{collectingPlace}/users/add', name: 'management_contractArea_permission_collectingplaces_users_add', methods: ['POST'])]
    public function permissionCollectingplaceUsersAdd(string $uuid, string $collectingPlace, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        /** @var ContractArea $contractArea */
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->findOneBy(criteria: ['uuid' => $collectingPlace]);
        $data = json_decode(json: $request->getContent(), associative: true);

        if (count(value: $data) > 0) {
            foreach ($data as $userUuid) {
                /** @var User $user */
                $user = $this->manager->getRepository(User::class)->findOneBy(criteria: ['uuid' => $userUuid]);

                if (false === $this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $contractArea, collectingPlace: $collectingPlace)) {
                    /** @var UserAccess $userAccess */
                    $userAccess = new UserAccess();
                    $userAccess->setUser(user: $user);
                    $userAccess->setContractArea(contractArea: $contractArea);
                    $userAccess->setCollectingPlace(collectingPlace: $collectingPlace);
                    $this->manager->persist($userAccess);
                }
            }
            $this->manager->flush();
        }

        return new JsonResponse();
    }

    #[Route(path: '/management/contractArea/{uuid}/permission/collectingplaces/{collectingPlace}/users/delete', name: 'management_contractArea_permission_collectingplaces_users_delete', methods: ['POST'])]
    public function permissionCollectingplaceUsersDelete(string $uuid, string $collectingPlace, Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        /** @var ContractArea $contractArea */
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->findOneBy(criteria: ['uuid' => $collectingPlace]);
        $data = json_decode(json: $request->getContent(), associative: true);

        if (count(value: $data) > 0) {
            foreach ($data as $userUuid) {
                /** @var User $user */
                $user = $this->manager->getRepository(User::class)->findOneBy(criteria: ['uuid' => $userUuid]);

                if ($this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $contractArea, collectingPlace: $collectingPlace)) {
                    /** @var UserAccess $userAccess */
                    $userAccessList = $this->manager->getRepository(UserAccess::class)->findBy(criteria: ['user' => $user, 'contractArea' => $contractArea, 'collectingPlace' => $collectingPlace]);

                    foreach ($userAccessList as $userAccess) {
                        $contractArea->removeUserAccess(userAccess: $userAccess);
                        $this->manager->persist($contractArea);
                        $this->manager->remove($userAccess);
                    }
                }
            }
            $this->manager->flush();
        }

        return new JsonResponse();
    }
}
