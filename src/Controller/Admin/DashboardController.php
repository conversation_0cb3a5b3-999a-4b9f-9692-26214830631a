<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use App\Entity\Main\Order;
use App\Entity\Main\State;
use App\Entity\Main\SystemProvider;
use App\Entity\Main\UnloadingPoint;
use App\Entity\Main\User;
use EasyCorp\Bundle\EasyAdminBundle\Attribute\AdminDashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\Dashboard;
use EasyCorp\Bundle\EasyAdminBundle\Config\MenuItem;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractDashboardController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\Translation\TranslatorInterface;

#[AdminDashboard(routePath: '/settings/dashboard', routeName: 'settings_dashboard')]
class DashboardController extends AbstractDashboardController
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public function index(): Response
    {
        return $this->render(view: 'easyadmin/dashboard.html.twig');
    }

    public function configureDashboard(): Dashboard
    {
        $title = $this->translator->trans('management');

        return Dashboard::new()
            ->setTitle(title: $title)
            ->setTitle(title: '<img src="../build/img/logo-prezero.svg" height="45">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'.$title.'</img>')
            ->setFaviconPath(path: 'favicon.ico');
    }

    public function configureMenuItems(): iterable
    {
        yield MenuItem::linkToRoute(label: $this->translator->trans('to_management'), icon: 'fa fa-arrow-circle-left', routeName: 'management');
        yield MenuItem::section(label: '');
        yield MenuItem::linkToRoute(label: $this->translator->trans('to_reporting'), icon: 'fa fa-arrow-circle-left', routeName: 'management_order_report');
        yield MenuItem::linktoDashboard(label: $this->translator->trans('overview'), icon: 'fa fa-home');
        yield MenuItem::section(label: $this->translator->trans('data'));
        yield MenuItem::linkToCrud(label: $this->translator->trans('management.collecting_places'), icon: 'fa fa-map-marker', entityFqcn: CollectingPlace::class);
        yield MenuItem::linkToCrud(label: $this->translator->trans('user'), icon: 'fa fa-user', entityFqcn: User::class);
        yield MenuItem::section(label: $this->translator->trans('orders'));
        yield MenuItem::linkToCrud(label: $this->translator->trans('orders'), icon: 'fa fa-folder', entityFqcn: Order::class);
        yield MenuItem::section(label: $this->translator->trans('interface'));
        yield MenuItem::linkToCrud(label: $this->translator->trans('management.contract_areas'), icon: 'fa fa-layer-group', entityFqcn: ContractArea::class);
        yield MenuItem::linkToCrud(label: $this->translator->trans('contract_area_validity'), icon: 'fa fa-layer-group', entityFqcn: ContractAreaValidity::class);
        yield MenuItem::linkToCrud(label: $this->translator->trans('contracts'), icon: 'fa fa-file-contract', entityFqcn: Contract::class);
        yield MenuItem::linkToCrud(label: $this->translator->trans('unloading_point'), icon: 'fa fa-map-marker', entityFqcn: UnloadingPoint::class);
        yield MenuItem::linkToCrud(label: $this->translator->trans('system_provider'), icon: 'fa fa-user-tag', entityFqcn: SystemProvider::class);
        yield MenuItem::section(label: $this->translator->trans('system_data'));
        yield MenuItem::linkToCrud(label: $this->translator->trans('states'), icon: 'fa fa-landmark', entityFqcn: State::class);
        yield MenuItem::section(label: $this->translator->trans('customer_view'));
        yield MenuItem::linkToRoute(label: $this->translator->trans('to_customer_view'), icon: 'fa fa-arrow-circle-left', routeName: 'app_login');
    }
}
