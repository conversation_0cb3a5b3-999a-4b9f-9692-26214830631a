<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\User;
use App\Services\Mailer;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\BooleanField;
use EasyCorp\Bundle\EasyAdminBundle\Field\ChoiceField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends AbstractCrudController<User>
 */
class UserCrudController extends AbstractCrudController
{
    public const INITIAL_PASSWORD = '##########';

    public function __construct(
        private readonly Mailer $mailer,
        private readonly TranslatorInterface $translator,
        private readonly Security $security,
    ) {
    }

    public static function getEntityFqcn(): string
    {
        return User::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular(label: $this->translator->trans('user'))
            ->setEntityLabelInPlural(label: $this->translator->trans('users'));
    }

    public function configureFields(string $pageName): iterable
    {
        // ... (your fields configuration remains the same)
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'email', label: $this->translator->trans('email')),
            BooleanField::new(propertyName: 'resetPassword', label: $this->translator->trans('reset_password')),
            ChoiceField::new(propertyName: 'roles', label: $this->translator->trans('roles_title'))->setChoices(
                choiceGenerator: [
                    $this->translator->trans('customer') => 'ROLE_USER',
                    $this->translator->trans('manager') => 'ROLE_MANAGER',
                    $this->translator->trans('reporting') => 'ROLE_REPORT',
                ]
            )->allowMultipleChoices(),
            ChoiceField::new(propertyName: 'locale', label: $this->translator->trans('locale'))->setChoices(
                choiceGenerator: [
                    $this->translator->trans('locale.de') => 'de',
                    $this->translator->trans('locale.en') => 'en',
                ]
            ),
        ];
    }

    public function persistEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        /* @var User $entityInstance */
        $this->processEntity(user: $entityInstance);

        $currentUser = $this->security->getUser();
        if ($currentUser instanceof User) {
            $entityInstance->setCreatedBy(createdBy: $currentUser->getUuid());
        }
        $entityInstance->setCreatedAt(createdAt: new \DateTime());
        parent::persistEntity($entityManager, $entityInstance);
        $this->sendPasswordResetMailIfNeeded(user: $entityInstance);
    }

    public function updateEntity(EntityManagerInterface $entityManager, $entityInstance): void
    {
        /* @var User $entityInstance */
        $this->processEntity(user: $entityInstance);

        $currentUser = $this->security->getUser();
        if ($currentUser instanceof User) {
            $entityInstance->setModifiedBy(modifiedBy: $currentUser->getUuid());
        }
        $entityInstance->setModifiedAt(modifiedAt: new \DateTime());

        parent::updateEntity($entityManager, $entityInstance);

        $this->sendPasswordResetMailIfNeeded(user: $entityInstance);
    }

    private function processEntity(User $user): void
    {
        if (count(value: array_intersect(['ROLE_ADMIN', 'ROLE_MANAGER', 'ROLE_REPORT', 'ROLE_DOCUMENTS'], $user->getRoles())) > 0 && 0 === preg_match(pattern: '(@mail.schwarz|@prezero.com)', subject: $user->getEmail())) {
            throw new BadRequestHttpException(message: 'Invalid email domain for the selected role.');
        }

        if (in_array(needle: $user->getPassword(), haystack: ['', '0'], strict: true) || $user->getResetPassword()) {
            $user->setResetPassword(resetPassword: true);
            $user->setPasswordDate(passwordDate: new \DateTime(datetime: '1900-01-01'));
            $user->setPassword(password: self::INITIAL_PASSWORD);
        }

        if ([] === $user->getRoles() || (1 === count(value: $user->getRoles()) && 'ROLE_USER' === $user->getRoles()[0])) {
            $user->setRoles(roles: ['ROLE_USER']);
        }
    }

    /**
     * Sends the password reset mail if needed.
     */
    private function sendPasswordResetMailIfNeeded(User $user): void
    {
        if ($user->getResetPassword() || self::INITIAL_PASSWORD === $user->getPassword()) {
            $this->mailer->sendPasswordReset(user: $user, newUser: self::INITIAL_PASSWORD === $user->getPassword());
        }
    }
}
