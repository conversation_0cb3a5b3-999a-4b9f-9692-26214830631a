<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\UnloadingPoint;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends AbstractCrudController<UnloadingPoint>
 */
class UnloadingPointCrudController extends AbstractCrudController
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public static function getEntityFqcn(): string
    {
        return UnloadingPoint::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            // ...
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::NEW)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::EDIT)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::DELETE)
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular(label: $this->translator->trans('unloading_point'))
            ->setEntityLabelInPlural(label: $this->translator->trans('unloading_points'));
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'unloadingPointId', label: $this->translator->trans('collectingplace.field.esaid'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'dsdId', label: $this->translator->trans('collectingplace.field.dsdid'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'name1', label: $this->translator->trans('name'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'street', label: $this->translator->trans('street'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'houseNumber', label: $this->translator->trans('house_number'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'postalCode', label: $this->translator->trans('postal_code'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'city', label: $this->translator->trans('city'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            TextField::new(propertyName: 'state', label: $this->translator->trans('state'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
        ];
    }
}
