<?php

declare(strict_types=1);

namespace App\Controller\Admin;

use App\Entity\Main\Contract;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\ArrayField;
use EasyCorp\Bundle\EasyAdminBundle\Field\AssociationField;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @extends AbstractCrudController<Contract>
 */
class ContractCrudController extends AbstractCrudController
{
    public function __construct(private readonly TranslatorInterface $translator)
    {
    }

    public static function getEntityFqcn(): string
    {
        return Contract::class;
    }

    public function configureActions(Actions $actions): Actions
    {
        return $actions
            // ...
            ->add(pageName: Crud::PAGE_INDEX, actionNameOrObject: Action::DETAIL)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::NEW)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::EDIT)
            ->remove(pageName: Crud::PAGE_INDEX, actionName: Action::DELETE)
            ->remove(pageName: Crud::PAGE_DETAIL, actionName: Action::DELETE)
        ;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setSearchFields(fieldNames: ['contractNumber', 'contractArea.name',
                'collectingPlace.collectingPlaceId', 'collectingPlace.name1', 'collectingPlace.dsdId',
                'unloadingPoint.unloadingPointId', 'unloadingPoint.name1', 'unloadingPoint.dsdId',
            ])
            ->setEntityLabelInSingular(label: $this->translator->trans('contract'))
            ->setEntityLabelInPlural(label: $this->translator->trans('contracts'));
    }

    public function configureFields(string $pageName): iterable
    {
        return [
            IdField::new(propertyName: 'id')->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            IdField::new(propertyName: 'contractNumber', label: $this->translator->trans('contract'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            AssociationField::new(propertyName: 'contractArea', label: $this->translator->trans('contact.contract_area'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            // AssociationField::new('collectingPlace', 'Umschlag')->setFormTypeOption('disabled','disabled'),
            AssociationField::new(propertyName: 'collectingPlace', label: $this->translator->trans('collectingplace')),
            AssociationField::new(propertyName: 'unloadingPoint', label: $this->translator->trans('unloading_point'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled'),
            ArrayField::new(propertyName: 'systemProviders', label: $this->translator->trans('system_provider'))->setFormTypeOption(optionName: 'disabled', optionValue: 'disabled')->onlyOnDetail(),
        ];
    }
}
