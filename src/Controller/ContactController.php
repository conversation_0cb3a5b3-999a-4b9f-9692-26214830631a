<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\Order;
use App\Entity\Main\User;
use App\Form\Type\ContactType;
use App\Repository\Main\FeatureRepository;
use App\Services\AccessHelper;
use App\Services\CaptchaVerifier;
use App\Services\Mailer;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\RateLimiter\RateLimiterFactoryInterface;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ContactController.
 */
class ContactController extends AbstractController
{
    /**
     * ContactController constructor.
     */
    public function __construct(
        private readonly EntityManagerInterface $manager,
        private readonly AccessHelper $accessHelper,
        private readonly FeatureRepository $featureRepository,
        private readonly CaptchaVerifier $captchaVerifier,
        private readonly RateLimiterFactoryInterface $contactFormLimiter,
    ) {
    }

    /**
     * Kontakt.
     */
    #[Template('contact/contact.html.twig')]
    #[Route(path: '/contact')]
    public function contact(Request $request, Mailer $mailer): Response
    {
        $feature = $this->featureRepository->findOneBy(criteria: ['name' => 'contactForm']);
        if (!$feature->isActive()) {
            return $this->render(view: 'contact/contact_inactive.html.twig');
        }

        $limiter = $this->contactFormLimiter->create($request->getClientIp());
        if (false === $limiter->consume(1)->isAccepted()) {
            $this->addFlash(type: 'error', message: 'Too many requests have been made. Please try again later.');

            return $this->redirectToRoute(route: 'app_contact_contact');
        }

        /** @var User|null $user */
        $user = $this->getUser();

        $form = $this->createForm(type: ContactType::class, data: []);
        $form->get('time')->setData(base64_encode(string: (string) time()));

        if ($user) {
            $form->remove('sender_email');
        } else {
            // No user
            $form->remove('disponumber');
            $form->remove('contract_area');
        }

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $hCaptchaResponse = $request->request->get(key: 'h-captcha-response', default: '');
            if (!$this->captchaVerifier->verify(hCaptchaResponse: $hCaptchaResponse)) {
                $this->addFlash(type: 'error', message: 'The CAPTCHA is invalid. Please try again.');

                return $this->redirectToRoute(route: 'app_contact_contact');
            }

            $formRequestData = $request->request->all(key: $form->getName());
            $isBot = false;

            // should be submitted >=5sec & <15min
            if (isset($formRequestData['time'])) {
                $time = base64_decode((string) $formRequestData['time']);
                if (is_numeric(value: $time)) {
                    $diff = time() - (int) $time;
                    if ($diff < 5 || $diff > 900) {
                        $isBot = true;
                    }
                } else {
                    $isBot = true;
                }
            } else {
                $isBot = true;
            }

            // bot protection-these should always be empty
            if (isset($formRequestData['email2']) && '' !== $formRequestData['email2']) {
                $isBot = true;
            }
            if (isset($formRequestData['telephone2']) && '' !== $formRequestData['telephone2']) {
                $isBot = true;
            }

            if ($isBot) {
                return $this->render(view: 'contact/contact_message_send.html.twig');
            }

            $data = $form->getData();
            $senderEmail = $user ? $user->getEmail() : $data['sender_email'];
            $mailer->sendContactMessage(data: $data, userMailAddress: $senderEmail);

            return $this->render(view: 'contact/contact_message_send.html.twig');
        }

        $this->prefillFormData(request: $request, form: $form);

        return $this->render(view: 'contact/contact.html.twig', parameters: [
            'form' => $form->createView(),
        ]);
    }

    /**
     * Helper method to pre-fill form data based on request query parameters.
     */
    private function prefillFormData(Request $request, FormInterface $form): void
    {
        $dispoNumber = $request->get(key: 'disponumber');
        $contractAreaId = $request->get(key: 'contractarea');
        $contractArea = null;
        if ($contractAreaId) {
            $contractArea = $this->manager->getRepository(ContractArea::class)->find(id: $contractAreaId);
        }

        $user = $this->getUser();
        if ($dispoNumber) {
            $order = $this->manager->getRepository(Order::class)->findOneBy(
                criteria: ['id' => $dispoNumber - Order::DISPOSAL_OFFSET]
            );

            if ($order && $user instanceof User && $this->accessHelper->checkUserAccessForCollectingPlace(
                user: $user,
                collectingPlace: $order->getCollectingPlace()
            )) {
                $form->get('collecting_place')->setData(
                    sprintf(
                        '%s - %s (%s)',
                        $order->getCollectingPlace()->getDsdId(),
                        $order->getCollectingPlace()->getName1(),
                        $order->getCollectingPlace()->getCity()
                    )
                );
                $form->get('contract_area')->setData($order->getContractArea()->getName());
                $form->get('disponumber')->setData($dispoNumber);
            }
        } else {
            $collectingPlaceId = $request->get(key: 'collectingplace');
            if ($collectingPlaceId) {
                $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->find(id: $collectingPlaceId);

                if ($collectingPlace && $user instanceof User && $this->accessHelper->checkUserAccessForCollectingPlace(
                    user: $user,
                    collectingPlace: $collectingPlace
                )) {
                    $form->get('collecting_place')->setData(
                        sprintf(
                            '%s - %s (%s)',
                            $collectingPlace->getDsdId(),
                            $collectingPlace->getName1(),
                            $collectingPlace->getCity()
                        )
                    );
                    if ($contractArea instanceof ContractArea) {
                        $form->get('contract_area')->setData($contractArea->getName());
                    }
                }
            }
        }
    }
}
