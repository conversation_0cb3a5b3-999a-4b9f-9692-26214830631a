<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Order;
use App\Entity\Main\UnloadingPoint;
use App\Form\Type\ExportType;
use App\Repository\Main\OrderRepository;
use App\Repository\Main\UnloadingPointRepository;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DownloadController.
 */
class DownloadController extends AbstractController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * Download orders.
     *
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    #[Template('')]
    #[Route(path: '/download', methods: ['POST'])]
    public function download(Request $request): RedirectResponse
    {
        if (!$this->isGranted(attribute: 'ROLE_ORDER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $form = $this->createForm(
            type: ExportType::class,
            data: [],
            options: ['collectingPlace' => null, 'dateFrom' => null, 'dateTo' => null]
        );

        /** @var OrderRepository $orderRepo */
        $orderRepo = $this->manager->getRepository(Order::class);
        $placeRepo = $this->manager->getRepository(CollectingPlace::class);

        $form->handleRequest($request);

        $this->getUser();

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $dateFrom = $data['dateFrom'];
            $dateTo = $data['dateTo'];
            $orderByAndOrder = $data['orderByAndOrder'];

            /** @var CollectingPlace $place */
            $place = $placeRepo->findOneBy(criteria: ['id' => $data['collectingPlace']]);
        } else {
            $dateFrom = new \DateTime();
            $dateFrom->modify(modifier: 'last monday');

            $dateTo = new \DateTime();
            $dateTo->modify(modifier: 'next friday');

            $place = null;

            $orderByAndOrder = null;
        }

        $queryBuilder = $orderRepo->findInBetweenQuery(
            startDate: $dateFrom, endDate: $dateTo, collectingPlace: $place
        );

        if ($orderByAndOrder) {
            $aryOrder = explode(separator: ':', string: $orderByAndOrder);
            $queryBuilder->orderBy(sort: $aryOrder[0], order: $aryOrder[1]);
        }

        $spreadsheet = new Spreadsheet();

        // Set document properties
        $spreadsheet->getProperties()->setCreator(creator: 'PreZero Mengenmeldung')
            ->setLastModifiedBy(modifiedBy: 'PreZero Mengenmeldung')
            ->setTitle(title: 'Office 2007 XLSX Mengenmeldung')
            ->setSubject(subject: 'Office 2007 XLSX Mengenmeldung')
            ->setDescription(description: 'Mengenmeldung System')
            ->setKeywords(keywords: 'office 2007 openxml mengenmeldung')
            ->setCategory(category: 'PreZero Mengenmeldung');

        // Add header
        $activeSheet = $spreadsheet->setActiveSheetIndex(worksheetIndex: 0);
        $activeSheet
            ->setCellValue(coordinate: 'A1', value: 'Auftragsdatum')
            ->setCellValue(coordinate: 'B1', value: 'Status')
            ->setCellValue(coordinate: 'C1', value: 'Vertragsgebiet')
            ->setCellValue(coordinate: 'D1', value: 'Umschlag')
            ->setCellValue(coordinate: 'E1', value: 'Disponummer')
            ->setCellValue(coordinate: 'F1', value: 'Systemgeber')
            ->setCellValue(coordinate: 'G1', value: 'Fahrerinfo');

        // Get results and add them to the sheet
        $results = $queryBuilder->getQuery()->getResult();

        $count = 2;

        foreach ($results as $row) {
            $status = $row['canceled'] ? 'Storniert' : ($row['transfered'] ? 'Beauftragt' : 'Erfasst');
            $activeSheet
                ->setCellValue(coordinate: 'A'.$count, value: $row['date'])
                ->setCellValue(coordinate: 'B'.$count, value: $status)
                ->setCellValue(coordinate: 'C'.$count, value: $row['contractArea'])
                ->setCellValue(coordinate: 'D'.$count, value: $row['dsdid'].' - '.
                    $row['name1'].' '.$row['name2'].', '.
                    $row['street'].' '.$row['houseNumber'].', '.
                    $row['postalCode'].' '.$row['city'])
                ->setCellValue(coordinate: 'E'.$count, value: $row['disposalNumber'])
                ->setCellValue(coordinate: 'F'.$count, value: $row['providerName'])
                ->setCellValue(coordinate: 'G'.$count, value: $row['driverMessage']);
            ++$count;
        }

        // Autosize
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'A')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'B')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'C')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'D')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'E')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'F')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'G')->setAutoSize(autosizeEnabled: true);

        // Rename worksheet
        $sheetTitle = 'Vom_'.$dateFrom->format('d.m.Y').'_bis_'.$dateTo->format('d.m.Y');
        $spreadsheet->getActiveSheet()->setTitle(title: $sheetTitle);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $spreadsheet->setActiveSheetIndex(worksheetIndex: 0);

        // Redirect output to a client’s web browser (Xlsx)
        header(header: 'Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header(header: 'Content-Disposition: attachment;filename="prezero-mengenmeldung-'.gmdate(format: 'd-m-Y_His').'.xlsx"');
        header(header: 'Cache-Control: max-age=0');

        // If you're serving to IE 9, then the following may be needed
        header(header: 'Cache-Control: max-age=1');

        // If you're serving to IE over SSL, then the following may be needed
        header(header: 'Expires: Mon, 3 Nov 2020 05:00:00 GMT'); // Date in the past
        header(header: 'Last-Modified: '.gmdate(format: 'D, d M Y H:i:s').' GMT'); // always modified
        header(header: 'Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header(header: 'Pragma: public'); // HTTP/1.0

        $writer = IOFactory::createWriter(spreadsheet: $spreadsheet, writerType: 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * Download Report orders.
     *
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    #[Route(path: '/management/download', methods: ['POST'], name: 'management_report_download')]
    public function downloadReport(Request $request): RedirectResponse
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $form = $this->createForm(
            type: ExportType::class,
            data: [],
            options: ['collectingPlace' => null, 'dateFrom' => null, 'dateTo' => null]
        );

        /** @var OrderRepository $orderRepo */
        $orderRepo = $this->manager->getRepository(Order::class);

        /** @var UnloadingPointRepository $unloadingPointRepo */
        $unloadingPointRepo = $this->manager->getRepository(UnloadingPoint::class);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $dateFrom = $data['dateFrom'];
            $dateTo = $data['dateTo'];
            $orderByAndOrder = $data['orderByAndOrder'];

            $unloadingPoint = $unloadingPointRepo->findOneBy(criteria: ['id' => $data['unloadingPoint']]);
        } else {
            $dateFrom = new \DateTime();
            $dateFrom->modify(modifier: 'last monday');

            $dateTo = new \DateTime();
            $dateTo->modify(modifier: 'next friday');

            $unloadingPoint = null;

            $orderByAndOrder = null;
        }

        $queryBuilder = $orderRepo->reportUnloadingPointQuery(
            startDate: $dateFrom, endDate: $dateTo, unloadingPoint: $unloadingPoint
        );

        if ($orderByAndOrder) {
            $aryOrder = explode(separator: ':', string: $orderByAndOrder);
            $queryBuilder->orderBy(sort: $aryOrder[0], order: $aryOrder[1]);
        }

        $spreadsheet = new Spreadsheet();

        // Set document properties
        $spreadsheet->getProperties()->setCreator(creator: 'PreZero Mengenmeldung')
            ->setLastModifiedBy(modifiedBy: 'PreZero Mengenmeldung')
            ->setTitle(title: 'Office 2007 XLSX Mengenmeldung')
            ->setSubject(subject: 'Office 2007 XLSX Mengenmeldung')
            ->setDescription(description: 'Mengenmeldung System')
            ->setKeywords(keywords: 'office 2007 openxml mengenmeldung')
            ->setCategory(category: 'PreZero Mengenmeldung');

        // Add header
        $activeSheet = $spreadsheet->setActiveSheetIndex(worksheetIndex: 0);
        $activeSheet
            ->setCellValue(coordinate: 'A1', value: 'Auftragsdatum')
            ->setCellValue(coordinate: 'B1', value: 'Status')
            ->setCellValue(coordinate: 'C1', value: 'Vertragsgebiet')
            ->setCellValue(coordinate: 'D1', value: 'Umschlag')
            ->setCellValue(coordinate: 'E1', value: 'Entladestelle')
            ->setCellValue(coordinate: 'F1', value: 'Systemgeber')
            ->setCellValue(coordinate: 'G1', value: 'Disponummer')
            ->setCellValue(coordinate: 'H1', value: 'Fahrerinfo');

        // Get results and add them to the sheet
        $results = $queryBuilder->getQuery()->getResult();

        $count = 2;

        foreach ($results as $row) {
            $status = $row['canceled'] ? 'Storniert' : ((null != $row['transferStatus'] && 'S' != $row['transferStatus']) ? 'Fehler bei Übertragung' : ($row['transfered'] ? 'Übertragen' : 'Erfasst'));

            $activeSheet->setCellValue(coordinate: 'A'.$count, value: $row['date'])
                ->setCellValue(coordinate: 'B'.$count, value: $status)
                ->setCellValue(coordinate: 'C'.$count, value: $row['contractArea'])
                ->setCellValue(coordinate: 'D'.$count, value: $row['cp_dsdid'].' - '.
                    $row['cp_name1'].' '.$row['cp_name2'].', '.
                    $row['cp_street'].' '.$row['cp_houseNumber'].', '.
                    $row['cp_postalCode'].' '.$row['cp_city'])
                ->setCellValue(coordinate: 'E'.$count, value: $row['ul_dsdid'].' - '.
                    $row['ul_name1'].' '.$row['ul_name2'].', '.
                    $row['ul_street'].' '.$row['ul_houseNumber'].', '.
                    $row['ul_postalCode'].' '.$row['ul_city'])
                ->setCellValue(coordinate: 'F'.$count, value: $row['providerName'])
                ->setCellValue(coordinate: 'G'.$count, value: $row['disposalNumber'])
                ->setCellValue(coordinate: 'H'.$count, value: $row['driverMessage']);
            ++$count;
        }

        // Autosize
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'A')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'B')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'C')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'D')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'E')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'F')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'G')->setAutoSize(autosizeEnabled: true);
        $spreadsheet->getActiveSheet()->getColumnDimension(column: 'H')->setAutoSize(autosizeEnabled: true);

        // Rename worksheet
        $sheetTitle = 'Vom_'.$dateFrom->format('d.m.Y').'_bis_'.$dateTo->format('d.m.Y');
        $spreadsheet->getActiveSheet()->setTitle(title: $sheetTitle);

        // Set active sheet index to the first sheet, so Excel opens this as the first sheet
        $spreadsheet->setActiveSheetIndex(worksheetIndex: 0);

        // Redirect output to a client’s web browser (Xlsx)
        header(header: 'Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header(header: 'Content-Disposition: attachment;filename="prezero-mengenmeldung-'.gmdate(format: 'd-m-Y_His').'.xlsx"');
        header(header: 'Cache-Control: max-age=0');

        // If you're serving to IE 9, then the following may be needed
        header(header: 'Cache-Control: max-age=1');

        // If you're serving to IE over SSL, then the following may be needed
        header(header: 'Expires: Mon, 3 Nov 2020 05:00:00 GMT'); // Date in the past
        header(header: 'Last-Modified: '.gmdate(format: 'D, d M Y H:i:s').' GMT'); // always modified
        header(header: 'Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header(header: 'Pragma: public'); // HTTP/1.0

        $writer = IOFactory::createWriter(spreadsheet: $spreadsheet, writerType: 'Xlsx');
        $writer->save('php://output');
        exit;
    }
}
