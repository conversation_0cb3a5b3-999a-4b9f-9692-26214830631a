<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use App\Services\ContractAreaValidityHelper;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ContractAreaValidityController.
 */
class ContractAreaValidityController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, private readonly ContractAreaValidityHelper $validityHelper)
    {
    }

    /**
     * Create ContractAreaValidity
     * Request to create a validity, if the validity already exists an error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/contractAreas/{contractAreaId}/validity', requirements: ['contractAreaId' => '[\w-]+'], methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractAreaId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['name', 'contractAreaId', 'state', 'validFrom', 'validTo'],
                properties: [
                    new OA\Property(property: 'validityId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31'),
                    new OA\Property(property: 'amountDay', type: 'integer', example: '3'),
                    new OA\Property(property: 'amountWeek', type: 'integer', example: '3'),
                    new OA\Property(property: 'validFrom', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2020-10-01'),
                    new OA\Property(property: 'validTo', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2022-10-01'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Response(response: 409, description: 'Already exist')]
    #[OA\Tag(name: 'ContractArea')]
    public function addContractAreasValidity(Request $request, string $contractAreaId): Response
    {
        // check if contractArea exists
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);

        $contractArea = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $contractAreaId]
        );
        if (is_null(value: $contractArea)) {
            return $this->viewNoContractArea(contractId: $contractArea);
        }

        // check if validity ALREADY exists
        $contractAreaRepo = $this->manager->getRepository(ContractAreaValidity::class);

        $existingValidity = $contractAreaRepo->findOneBy(
            criteria: ['validityId' => $request->get(key: 'validityId')]
        );
        if ($existingValidity instanceof ContractAreaValidity) {
            return $this->updateContractAreasValidity(request: $request, contractAreaId: $contractAreaId);
            // return $this->viewValidityAlreadyExists($request->get('validityId'));
        }

        $contractAreaValidity = $this->validityHelper->createValidity(
            values: [
                'validityId' => $request->get(key: 'validityId'),
                'amountDay' => $request->get(key: 'amountDay'),
                'amountWeek' => $request->get(key: 'amountWeek'),
                'validFrom' => $request->get(key: 'validFrom'),
                'validTo' => $request->get(key: 'validTo'),
            ], contractArea: $contractArea
        );

        $view = $this->view(
            data: [
                'id' => $contractAreaValidity->getId(),
                'contractAreaValidityId' => $contractAreaValidity->getValidityId(),
                'amountDay' => $contractAreaValidity->getAmountDay(),
                'amountWeek' => $contractAreaValidity->getAmountWeek(),
                'validFrom' => $contractAreaValidity->getValidFrom()->format('Y-m-d'),
                'validTo' => $contractAreaValidity->getValidTo()->format('Y-m-d'),
            ], statusCode: 201
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Update ContractAreaValidity
     * Request to update a validity, if the validity NOT exists an error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/contractAreas/{contractAreaId}/validity/{validityId}', requirements: ['contractAreaId' => '[\w-]+', 'validityId' => '[\w-]+'], methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractAreaId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\Parameter(name: 'validityId', in: 'path', example: 'abcd7bc08-fcab-42fb-a419-0692a769abcd')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['name', 'contractAreaId', 'state', 'validFrom', 'validTo'],
                properties: [
                    new OA\Property(property: 'amountDay', type: 'integer', example: '3'),
                    new OA\Property(property: 'amountWeek', type: 'integer', example: '3'),
                    new OA\Property(property: 'validFrom', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2020-10-01'),
                    new OA\Property(property: 'validTo', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2022-10-01'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 200, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'ContractArea')]
    public function updateContractAreasValidity(Request $request, string $contractAreaId): Response
    {
        // check if contractArea exists
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);

        $contractArea = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $contractAreaId]
        );
        if (is_null(value: $contractArea)) {
            return $this->viewNoContractArea(contractId: $contractArea);
        }

        // check if validity ALREADY exists
        $contractAreaRepo = $this->manager->getRepository(ContractAreaValidity::class);

        $existingValidity = $contractAreaRepo->findOneBy(
            criteria: ['validityId' => $request->get(key: 'validityId')]
        );
        if (is_null(value: $existingValidity)) {
            return $this->viewValidityNotExists(validityUuid: $request->get(key: 'validityId'));
        }

        $contractAreaValidity = $this->validityHelper->saveValidity(
            values: [
                'validityId' => $request->get(key: 'validityId'),
                'amountDay' => $request->get(key: 'amountDay'),
                'amountWeek' => $request->get(key: 'amountWeek'),
                'validFrom' => $request->get(key: 'validFrom'),
                'validTo' => $request->get(key: 'validTo'),
            ],
            contractAreaValidity: $existingValidity,
            contractArea: $contractArea
        );

        $view = $this->view(
            data: [
                'id' => $contractAreaValidity->getId(),
                'contractAreaValidityId' => $contractAreaValidity->getValidityId(),
                'amountDay' => $contractAreaValidity->getAmountDay(),
                'amountWeek' => $contractAreaValidity->getAmountWeek(),
                'validFrom' => $contractAreaValidity->getValidFrom()->format('Y-m-d'),
                'validTo' => $contractAreaValidity->getValidTo()->format('Y-m-d'),
            ], statusCode: 200
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewValidityNotExists(string $validityUuid): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Validity Not Found',
                'contractAreaId' => $validityUuid,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewNoContractArea(?string $contractId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: No ContractArea for UUID',
                'contractAreaId' => $contractId,
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
