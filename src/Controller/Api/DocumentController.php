<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\Contract;
use App\Entity\Main\Document;
use App\Entity\Main\DocumentData;
use App\Entity\Main\DocumentType;
use App\Services\FileStore;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DocumentController.
 */
class DocumentController extends AbstractFOSRestController
{
    public function __construct(private readonly FileStore $fileStore, private readonly EntityManagerInterface $entityManager)
    {
    }

    #[Route(path: '/rest/{version}/file/{uuid}', name: 'file', methods: ['GET'])]
    public function index(string $uuid): Response
    {
        $document = $this->entityManager->getRepository(Document::class)->findOneBy(criteria: ['uuid' => Uuid::fromString(uuid: $uuid)]);

        if (!$document || !$document->getDocumentData() || !$document->getDocumentData()->getS3FilePath()) {
            throw $this->createNotFoundException(message: 'File not found.');
        }

        $documentData = $document->getDocumentData();
        $s3Path = $documentData->getS3FilePath();

        $response = new StreamedResponse(callbackOrChunks: function () use ($s3Path): void {
            $outputStream = fopen(filename: 'php://output', mode: 'wb');
            $fileStream = $this->fileStore->getStream(s3Path: $s3Path);
            if ($fileStream) {
                stream_copy_to_stream(from: $fileStream, to: $outputStream);
                fclose(stream: $fileStream);
            }
        });

        $response->headers->set(key: 'Content-Type', values: $documentData->getMimeType());
        if ('application/pdf' == $documentData->getMimeType()) {
            $response->headers->set(key: 'Content-Disposition', values: 'attachment; filename="mengenmeldung-downloaded.pdf"');
        }

        return $response;
    }

    #[Route(path: '/rest/{version}/documents/{uuid}', name: 'file-delete', methods: ['DELETE'])]
    public function delete(string $uuid): Response
    {
        $document = $this->entityManager->getRepository(Document::class)->findOneBy(criteria: ['uuid' => Uuid::fromString(uuid: $uuid)]);

        if ($document instanceof Document) {
            $documentData = $document->getDocumentData();
            if ($documentData && $documentData->getS3FilePath()) {
                $this->fileStore->delete(s3Path: $documentData->getS3FilePath());
            }

            $this->entityManager->remove($document);
            if ($documentData instanceof DocumentData) {
                $this->entityManager->remove($documentData);
            }
            $this->entityManager->flush();

            return new Response(content: json_encode(value: ['uuid' => $uuid]), status: Response::HTTP_OK);
        }

        return new Response(content: json_encode(value: ['uuid' => $uuid]), status: Response::HTTP_BAD_REQUEST);
    }

    #[Route(path: '/rest/{version}/documents', name: 'add-document', methods: ['POST'])]
    public function addDocument(Request $request): Response
    {
        $file = $request->files->get(key: 'file');
        if (!$file) {
            return $this->viewInputFileNotFound();
        }

        $data = json_decode(json: $request->get(key: 'data'), associative: true);
        if (!$data) {
            return $this->viewInputDataNotFound();
        }

        $contract = $this->entityManager->getRepository(Contract::class)->findOneBy(criteria: ['contractNumber' => $data['contractNumber']]);
        if (!$contract instanceof Contract) {
            return $this->viewContractNotFound(contractNumber: $data['contractNumber']);
        }

        $documentType = $this->entityManager->getRepository(DocumentType::class)->findOneBy(criteria: ['name' => $data['type']]);
        if (!$documentType instanceof DocumentType) {
            return $this->viewDocumentTypeNotFound(documentType: $data['type']);
        }

        $existingDocument = $this->entityManager->getRepository(Document::class)->findOneBy(criteria: [
            'documentType' => $documentType,
            'contract' => $contract,
            'number' => $data['number'],
        ]);

        if ($existingDocument instanceof Document) {
            return $this->viewDocumentExists(documentId: (string) $existingDocument->getUuid());
        }

        $documentUuid = Uuid::uuid4();
        $s3Path = $this->fileStore->getS3Key(uuid: $documentUuid->toString());

        $fileStream = fopen(filename: $file->getRealPath(), mode: 'r');
        $this->fileStore->upload(s3Path: $s3Path, contentStream: $fileStream);

        $documentData = new DocumentData();
        $documentData->setUuid(uuid: $documentUuid);
        $documentData->setMimeType(mimeType: $file->getMimeType());
        $documentData->setS3FilePath(s3FilePath: $s3Path);
        $documentData->setCreatedAt(createdAt: new \DateTime());

        $document = new Document();
        $document->setUuid(uuid: $documentUuid);
        $document->setNumber(number: $data['number']);
        $document->setDate(date: \DateTime::createFromFormat(format: 'Ymd', datetime: $data['date']));
        $document->setAmount(amount: $data['amount']);
        $document->setUnit(unit: $data['unit']);
        $document->setContract(contract: $contract);
        $document->setDocumentType(documentType: $documentType);
        $document->setActive(active: true);
        $document->setVisible(visible: true);
        $document->setAdditionals(additionals: $data['additional'] ?? []);
        $document->setDocumentData(documentData: $documentData);
        $document->setCreatedAt(createdAt: new \DateTime());

        $this->entityManager->persist($documentData);
        $this->entityManager->persist($document);
        $this->entityManager->flush();

        return new Response(content: json_encode(value: ['uuid' => $document->getUuid()]), status: Response::HTTP_OK);
    }

    #[Route(path: '/rest/{version}/documents/{uuid}', name: 'update-document', methods: ['POST'])]
    public function updateDocument(Request $request, string $uuid): Response
    {
        $document = $this->entityManager->getRepository(Document::class)->findOneBy(criteria: ['uuid' => Uuid::fromString(uuid: $uuid)]);
        if (!$document instanceof Document) {
            return $this->viewDocumentNotFound(document: $uuid);
        }

        $file = $request->files->get(key: 'file');
        if (!$file) {
            return $this->viewInputFileNotFound();
        }

        $data = json_decode(json: $request->get(key: 'data'), associative: true);
        if (!$data) {
            return $this->viewInputDataNotFound();
        }

        $contract = $this->entityManager->getRepository(Contract::class)->findOneBy(criteria: ['contractNumber' => $data['contractNumber']]);
        if (!$contract instanceof Contract) {
            return $this->viewContractNotFound(contractNumber: $data['contractNumber']);
        }

        $documentType = $this->entityManager->getRepository(DocumentType::class)->findOneBy(criteria: ['name' => $data['type']]);
        if (!$documentType instanceof DocumentType) {
            return $this->viewDocumentTypeNotFound(documentType: $data['type']);
        }

        $s3Path = $this->fileStore->getS3Key(uuid: $uuid);
        $fileStream = fopen(filename: $file->getRealPath(), mode: 'r');
        $this->fileStore->upload(s3Path: $s3Path, contentStream: $fileStream);

        $documentData = $document->getDocumentData() ?? new DocumentData();
        $documentData->setUuid(uuid: Uuid::fromString(uuid: $uuid));
        $documentData->setMimeType(mimeType: $file->getMimeType());
        $documentData->setS3FilePath(s3FilePath: $s3Path);
        $documentData->setModifiedAt(modifiedAt: new \DateTime());

        $document->setNumber(number: $data['number']);
        $document->setDate(date: \DateTime::createFromFormat(format: 'Ymd', datetime: $data['date']));
        $document->setAmount(amount: $data['amount']);
        $document->setUnit(unit: $data['unit']);
        $document->setContract(contract: $contract);
        $document->setDocumentType(documentType: $documentType);
        $document->setAdditionals(additionals: $data['additional'] ?? []);
        $document->setDocumentData(documentData: $documentData);
        $document->setModifiedAt(modifiedAt: new \DateTime());

        $this->entityManager->persist($documentData);
        $this->entityManager->persist($document);
        $this->entityManager->flush();

        return new Response(content: json_encode(value: ['uuid' => $document->getUuid()]), status: Response::HTTP_OK);
    }

    private function viewInputFileNotFound(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: No input file!',
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewInputDataNotFound(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: No input data!',
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDocumentNotFound(string $document): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Document NOT found!',
                'document' => $document,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDocumentExists(string $documentId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Document already exists!',
                'document' => $documentId,
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDocumentTypeNotFound(string $documentType): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Document type NOT found!',
                'documentType' => $documentType,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewContractNotFound(string $contractNumber): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Contract NOT found!',
                'contractNumber' => $contractNumber,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
