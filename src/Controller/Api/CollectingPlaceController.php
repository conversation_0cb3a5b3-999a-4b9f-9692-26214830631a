<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\CollectingPlace;
use App\Services\CollectingPlaceHelper;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class CollectingPlaceController.
 */
class CollectingPlaceController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, private readonly CollectingPlaceHelper $collectingPlaceHelper)
    {
    }

    /**
     * Create CollectingPlace
     * Request to create a contract area, if the contractAreaId exist parameter error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/collectingPlaces', methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['collectingPlaceId', 'dsdId', 'name1', 'name2', 'street', 'houseNumber', 'postalCode', 'city', 'district', 'state', 'country'],
                properties: [
                    new OA\Property(property: 'collectingPlaceId', type: 'integer', example: '321'),
                    new OA\Property(property: 'dsdId', type: 'string', example: '321'),
                    new OA\Property(property: 'name1', type: 'string', example: 'B & E Antriebselemente GmbH'),
                    new OA\Property(property: 'name2', type: 'string', example: 'Antriebe aller Art'),
                    new OA\Property(property: 'street', type: 'string', example: 'Lange Str.'),
                    new OA\Property(property: 'houseNumber', type: 'string', example: '8'),
                    new OA\Property(property: 'postalCode', type: 'string', example: '33014'),
                    new OA\Property(property: 'city', type: 'string', example: 'Bad Driburg'),
                    new OA\Property(property: 'district', type: 'string', example: 'Test'),
                    new OA\Property(property: 'state', type: 'string', example: 'BB'),
                    new OA\Property(property: 'country', type: 'string', example: 'DE'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Response(response: 409, description: 'Already exist')]
    #[OA\Tag(name: 'CollectingPlace')]
    public function addCollectingPlaces(Request $request): Response
    {
        $collectingPlaceRepo = $this->manager->getRepository(CollectingPlace::class);

        $collectingPlaceId = $request->get(key: 'collectingPlaceId');

        // check if collectingPlace already exists
        $collectingPlace = $collectingPlaceRepo->findOneBy(
            criteria: ['collectingPlaceId' => $collectingPlaceId]
        );

        if ($collectingPlace instanceof CollectingPlace) {
            return $this->updateCollectingPlaces(request: $request);
            // return $this->viewCollectingPlaceExist($collectingPlaceId);
        }

        if (is_null(value: $request->get(key: 'dsdId'))) {
            return $this->viewDsdIdNotSet();
        }

        if (is_null(value: $request->get(key: 'state'))) {
            return $this->viewStateNotSet();
        }

        if (is_null(value: $request->get(key: 'country'))) {
            return $this->viewCountryNotSet();
        }

        // create contract area
        $collectingPlace = $this->collectingPlaceHelper->createPlace(values: [
            'collectingPlaceId' => $request->get(key: 'collectingPlaceId'),
            'dsdId' => $request->get(key: 'dsdId'),
            'name1' => $request->get(key: 'name1'),
            'name2' => $request->get(key: 'name2'),
            'street' => $request->get(key: 'street'),
            'houseNumber' => $request->get(key: 'houseNumber'),
            'postalCode' => $request->get(key: 'postalCode'),
            'city' => $request->get(key: 'city'),
            'district' => $request->get(key: 'district'),
            'state' => $request->get(key: 'state'),
            'country' => $request->get(key: 'country'),
        ]);

        $view = $this->view(
            data: [
                'id' => $collectingPlace->getId(),
                'collectingPlaceId' => $collectingPlace->getCollectingPlaceId(),
                'dsdId' => $collectingPlace->getDsdId(),
                'name1' => $collectingPlace->getName1(),
                'name2' => $collectingPlace->getName2(),
                'street' => $collectingPlace->getStreet(),
                'houseNumber' => $collectingPlace->getHouseNumber(),
                'postalCode' => $collectingPlace->getPostalCode(),
                'city' => $collectingPlace->getCity(),
                'district' => $collectingPlace->getDistrict(),
                'state' => $collectingPlace->getState()->getShortName(),
                'country' => $collectingPlace->getCountry(),
            ], statusCode: 201
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Update CollectingPlace
     * Put Request to update a collecting place, if the collecting place id is NOT existing, error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/collectingPlaces/{collectingPlaceId}', requirements: ['collectingPlaceId' => '^[\d]+$'], methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'collectingPlaceId', in: 'path', example: '321')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['collectingPlaceId'],
                properties: [
                    new OA\Property(property: 'collectingPlaceId', type: 'integer', example: '321'),
                    new OA\Property(property: 'dsdId', type: 'string', example: '321'),
                    new OA\Property(property: 'name1', type: 'string', example: 'B & E Antriebselemente GmbH'),
                    new OA\Property(property: 'name2', type: 'string', example: 'Antriebe aller Art'),
                    new OA\Property(property: 'street', type: 'string', example: 'Lange Str.'),
                    new OA\Property(property: 'houseNumber', type: 'string', example: '8'),
                    new OA\Property(property: 'postalCode', type: 'string', example: '33014'),
                    new OA\Property(property: 'city', type: 'string', example: 'Bad Driburg'),
                    new OA\Property(property: 'district', type: 'string', example: 'Test'),
                    new OA\Property(property: 'state', type: 'string', example: 'BB'),
                    new OA\Property(property: 'country', type: 'string', example: 'DE'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 200, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'CollectingPlace')]
    public function updateCollectingPlaces(Request $request): Response
    {
        $collectingPlaceRepo = $this->manager->getRepository(CollectingPlace::class);

        $collectingPlaceId = $request->get(key: 'collectingPlaceId');

        /** @var CollectingPlace|null $collectingPlace */
        $collectingPlace = $collectingPlaceRepo->findOneBy(
            criteria: ['collectingPlaceId' => $collectingPlaceId]
        );

        if (is_null(value: $collectingPlace)) {
            return $this->viewCollectingPlaceNotExist(collectingPlaceId: $collectingPlaceId);
        }

        if (is_null(value: $request->get(key: 'dsdId'))) {
            return $this->viewDsdIdNotSet();
        }

        if (is_null(value: $request->get(key: 'state'))) {
            return $this->viewStateNotSet();
        }

        if (is_null(value: $request->get(key: 'country'))) {
            return $this->viewCountryNotSet();
        }

        // update collecting place
        $collectingPlace = $this->collectingPlaceHelper->saveCollectingPlace(values: [
            'collectingPlaceId' => $request->get(key: 'collectingPlaceId'),
            'dsdId' => $request->get(key: 'dsdId'),
            'name1' => $request->get(key: 'name1'),
            'name2' => $request->get(key: 'name2'),
            'street' => $request->get(key: 'street'),
            'houseNumber' => $request->get(key: 'houseNumber'),
            'postalCode' => $request->get(key: 'postalCode'),
            'city' => $request->get(key: 'city'),
            'district' => $request->get(key: 'district'),
            'state' => $request->get(key: 'state'),
            'country' => $request->get(key: 'state'),
        ], collectingPlace: $collectingPlace);

        $view = $this->view(
            data: [
                'id' => $collectingPlace->getId(),
                'collectingPlaceId' => $collectingPlace->getCollectingPlaceId(),
                'dsdId' => $collectingPlace->getDsdId(),
                'name1' => $collectingPlace->getName1(),
                'name2' => $collectingPlace->getName2(),
                'street' => $collectingPlace->getStreet(),
                'houseNumber' => $collectingPlace->getHouseNumber(),
                'postalCode' => $collectingPlace->getPostalCode(),
                'city' => $collectingPlace->getCity(),
                'district' => $collectingPlace->getDistrict(),
                'state' => $collectingPlace->getState()->getShortName(),
                'country' => $collectingPlace->getCountry(),
            ], statusCode: 200
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewCollectingPlaceNotExist(string $collectingPlaceId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: CollectingPlace NOT found!',
                'contractAreaId' => $collectingPlaceId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDsdIdNotSet(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: DSD Id not set!',
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewStateNotSet(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: State not set!',
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewCountryNotSet(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Country not set!',
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
