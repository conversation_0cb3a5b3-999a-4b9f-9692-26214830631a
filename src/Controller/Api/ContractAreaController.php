<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use App\Entity\Main\State;
use App\Services\ContractAreaHelper;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ContractAreaController.
 */
class ContractAreaController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, private readonly ContractAreaHelper $contractAreaHelper)
    {
    }

    /**
     * Remove ContractAreaValidity
     * Delete request to remove a contract area validity from a contract area, if the contractAreaId or relation to validity NOT exists, error message is returned.
     */
    #[Route(path: '/rest/{version}/contractAreas/{contractAreaId}/{contractAreaValidityId}', requirements: ['contractAreaId' => '[\w-]+', 'contractAreaValidityId' => '[\w-]+'], methods: ['DELETE'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractAreaId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\Parameter(name: 'contractAreaValidityId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\Response(response: 204, description: 'Removed')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'ContractArea')]
    public function removeContractAreaValidity(string $contractAreaId, string $contractAreaValidityId): Response
    {
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);

        // check if contractArea exists
        /** @var ContractArea|null $contractArea */
        $contractArea = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $contractAreaId]
        );

        if (is_null(value: $contractArea)) {
            return $this->viewContractAreaNotExist(contractId: $contractAreaId);
        }

        // check if validity exists
        $validityRepo = $this->manager->getRepository(ContractAreaValidity::class);

        /** @var ContractAreaValidity|null $validity */
        $validity = $validityRepo->findOneBy(
            criteria: ['validityId' => $contractAreaValidityId]
        );

        if (is_null(value: $validity)) {
            return $this->viewAreaValidityNotExist(validityId: $contractAreaId);
        }

        $contractArea->removeContractAreaValidity(contractAreaValidity: $validity);
        $this->manager->persist($contractArea);
        $this->manager->flush();

        $view = $this->view(data: [], statusCode: 204);

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Create ContractArea
     * Request to create a contract area, if the contractAreaId exist parameter error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/contractAreas', methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['name', 'contractAreaId', 'state', 'validFrom', 'validTo'],
                properties: [
                    new OA\Property(property: 'name', type: 'string', example: 'NS105-2007G1-109'),
                    new OA\Property(property: 'contractAreaId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31'),
                    new OA\Property(property: 'state', type: 'string', example: 'NI'),
                    new OA\Property(property: 'validFrom', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2020-10-01'),
                    new OA\Property(property: 'validTo', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2022-10-01'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Response(response: 409, description: 'Already exist')]
    #[OA\Tag(name: 'ContractArea')]
    public function addContractAreas(Request $request): Response
    {
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);
        $contractId = $request->get(key: 'contractAreaId');

        // check if contractArea already exists
        $contractArea = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $contractId]
        );

        if ($contractArea instanceof ContractArea) {
            return $this->updateContractAreas(request: $request);
            // return $this->viewContractAreaExist($contractId);
        }

        // check if state is available
        $stateRepo = $this->manager->getRepository(State::class);
        $state = $stateRepo->findOneBy(criteria: ['shortName' => $request->get(key: 'state')]);

        if (is_null(value: $state)) {
            return $this->viewStateNotFound(state: $state);
        }

        // create contract area
        $contractArea = $this->contractAreaHelper->createContract(values: [
            'contractAreaId' => $request->get(key: 'contractAreaId'),
            'name' => $request->get(key: 'name'),
            'validFrom' => $request->get(key: 'validFrom'),
            'validTo' => $request->get(key: 'validTo'),
        ], state: $state
        );

        $view = $this->view(
            data: [
                'id' => $contractArea->getId(),
                'contractAreaId' => $contractArea->getContractAreaId(),
                'name' => $contractArea->getName(),
                'state' => $contractArea->getState()->getShortName(),
                'validFrom' => $contractArea->getValidFrom()->format(format: 'Y-m-d'), // "2017-07-21",
                'validTo' => $contractArea->getValidTo()->format(format: 'Y-m-d'), // "2017-07-21"
            ],
            statusCode: 201
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Update ContractArea
     * Put Request to update a contract area, if the contractAreaId is NOT existing, error message is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/contractAreas/{contractAreaId}', requirements: ['contractAreaId' => '[\w-]+'], methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractAreaId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['name', 'contractAreaId', 'state', 'validFrom', 'validTo'],
                properties: [
                    new OA\Property(property: 'name', type: 'string', example: 'NS105-2007G1-109'),
                    new OA\Property(property: 'state', type: 'string', example: 'NI'),
                    new OA\Property(property: 'validFrom', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2020-10-01'),
                    new OA\Property(property: 'validTo', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2022-10-01'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 200, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'ContractArea')]
    public function updateContractAreas(Request $request): Response
    {
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);

        $contractId = $request->get(key: 'contractAreaId');

        // check if contractArea already exists
        $contractArea = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $contractId]
        );
        if (is_null(value: $contractArea)) {
            return $this->viewContractAreaNotExist(contractId: $contractId);
        }

        // check if state is available
        $stateRepo = $this->manager->getRepository(State::class);
        $state = $request->get(key: 'state');
        $state = $stateRepo->findOneBy(criteria: ['shortName' => $state]);
        if (is_null(value: $state)) {
            return $this->viewStateNotFound(state: $state);
        }

        // update contract area
        $this->contractAreaHelper->saveContract(values: [
            'contractAreaId' => $request->get(key: 'contractAreaId'),
            'name' => $request->get(key: 'name'),
            'validFrom' => $request->get(key: 'validFrom'),
            'validTo' => $request->get(key: 'validTo'),
        ], contractArea: $contractArea, state: $state);

        $view = $this->view(
            data: [
                'id' => $contractArea->getId(),
                'contractAreaId' => $contractArea->getContractAreaId(),
                'name' => $contractArea->getName(),
                'state' => $contractArea->getState()->getShortName(),
                'validFrom' => $contractArea->getValidFrom()->format(format: 'Y-m-d'), // "2017-07-21",
                'validTo' => $contractArea->getValidTo()->format(format: 'Y-m-d'), // "2017-07-21"
            ], statusCode: 200
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewContractAreaNotExist(string $contractId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Contract area does NOT exist!',
                'contractAreaId' => $contractId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewAreaValidityNotExist(string $validityId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Validity (ContractAreaValidity) does NOT exist!',
                'contractAreaId' => $validityId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewStateNotFound(?string $state): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: State not found!',
                'state' => $state,
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
