<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\Order;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class LogisticsController.
 */
class LogisticsController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * Update an order
     * Request to update an order, if the order does not exist an error is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/logistics/{orderId}', requirements: ['orderId' => '[\w-]+'], methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'orderId', in: 'path', example: '4a5c9e63-de3a-4fe0-9f22-304eda1f7922')]
    #[OA\Response(response: 204, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'Logistics')]
    public function updateLogistics(string $orderId): Response
    {
        $orderRepo = $this->manager->getRepository(Order::class);

        /** @var Order|null $order */
        $order = $orderRepo->findOneBy(
            criteria: ['orderId' => $orderId]
        );

        if (is_null(value: $order)) {
            return $this->viewOrderNotExist(orderId: $orderId);
        }

        $order->setCanceled(canceled: new \DateTime());

        $this->manager->persist($order);

        $this->manager->flush();

        $view = $this->view(data: [], statusCode: 204);

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Cancel an order.
     */
    #[Route(path: '/rest/{version}/logistics/{orderId}', requirements: ['orderId' => '[\w-]+'], methods: ['DELETE'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'orderId', in: 'path', example: '4a5c9e63-de3a-4fe0-9f22-304eda1f7922')]
    #[OA\Response(response: 204, description: 'Removed')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'Logistics')]
    public function deleteLogistics(string $orderId): Response
    {
        $orderRepo = $this->manager->getRepository(Order::class);

        /** @var Order|null $order */
        $order = $orderRepo->findOneBy(
            criteria: ['orderId' => $orderId]
        );

        if (is_null(value: $order)) {
            return $this->viewOrderNotExist(orderId: $orderId);
        }

        $order->setCanceled(canceled: new \DateTime());

        $this->manager->persist($order);

        $this->manager->flush();

        $view = $this->view(data: [], statusCode: 204);

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewOrderNotExist(string $orderId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Order NOT found!',
                'orderId' => $orderId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
