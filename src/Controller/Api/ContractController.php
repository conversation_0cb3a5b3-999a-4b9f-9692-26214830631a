<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\SystemProvider;
use App\Entity\Main\UnloadingPoint;
use App\Services\ContractHelper;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class ContractController.
 */
class ContractController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, private readonly ContractHelper $contractHelper)
    {
    }

    /**
     * Create Contract
     * Request to create a contract, if a contract with the same uuid exists an error is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/contracts', methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['contractId', 'contractAreaId', 'contractNumber', 'validFrom', 'validTo', 'collectingPlaceId', 'unloadingPointId'],
                properties: [
                    new OA\Property(property: 'contractId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fdf2'),
                    new OA\Property(property: 'contractAreaId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31'),
                    new OA\Property(property: 'contractNumber', type: 'string', example: '456516'),
                    new OA\Property(property: 'validFrom', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2020-10-01'),
                    new OA\Property(property: 'validTo', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2022-10-01'),
                    new OA\Property(property: 'collectingPlaceId', type: 'string', example: '456-abc'),
                    new OA\Property(property: 'unloadingPointId', type: 'string', example: '123-def'),
                ],
            )
        ),
    )]
    #[OA\Response(response: 201, description: 'Created')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Response(response: 409, description: 'Already exist')]
    #[OA\Tag(name: 'Contract')]
    public function addContract(Request $request): Response
    {
        $collectingPlaceRepro = $this->manager->getRepository(Contract::class);

        $contractId = $request->get(key: 'contractId');

        // check if contract already exists
        $contract = $collectingPlaceRepro->findOneBy(
            criteria: ['contractId' => $contractId]
        );

        if ($contract instanceof Contract) {
            return $this->updateContract(request: $request);
            // return $this->viewContractExist($contractId);
        }

        $contractNumber = $request->get(key: 'contractNumber');
        if (is_null(value: $contractNumber)) {
            return $this->viewContractNumberMissing(contractNumber: $contractNumber);
        }

        // check if contractArea is available
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);

        /** @var ContractArea|null $area */
        $area = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $request->get(key: 'contractAreaId')]
        );

        if (is_null(value: $area)) {
            return $this->viewContractAreaNotFound(contractAreaId: $request->get(key: 'contractAreaId'));
        }

        // check if collectingPlace is available
        $collectingPlaceRepro = $this->manager->getRepository(CollectingPlace::class);

        $collectingPlace = $collectingPlaceRepro->findOneBy(
            criteria: ['collectingPlaceId' => $request->get(key: 'collectingPlaceId')]
        );

        if (is_null(value: $collectingPlace)) {
            return $this->viewCollectingPlaceNotFound(collectingPlaceId: $request->get(key: 'collectingPlaceId'));
        }

        // check if unloadingPoint is available
        $unloadingPointRepro = $this->manager->getRepository(UnloadingPoint::class);

        /** @var UnloadingPoint|null $unloadingPoint */
        $unloadingPoint = $unloadingPointRepro->findOneBy(
            criteria: ['unloadingPointId' => $request->get(key: 'unloadingPointId')]
        );

        if (is_null(value: $unloadingPoint)) {
            return $this->viewUnloadingPointNotFound(unloadingPointId: $request->get(key: 'unloadingPointId'));
        }

        // create contract
        $contract = $this->contractHelper->createContract(values: [
            'contractId' => $request->get(key: 'contractId'),
            'contractNumber' => $request->get(key: 'contractNumber'),
            'validFrom' => $request->get(key: 'validFrom'),
            'validTo' => $request->get(key: 'validTo'),
        ], area: $area, collectingPlace: $collectingPlace, unloadingPoint: $unloadingPoint);

        $view = $this->view(
            data: [
                'id' => $contract->getId(),
                'contractId' => $contract->getContractId(),
                'contractAreaId' => $contract->getContractArea()->getContractAreaId(),
                'collectingPlaceId' => $contract->getCollectingPlace()->getCollectingPlaceId(),
                'unloadingPointId' => $contract->getUnloadingPoint()->getUnloadingPointId(),
                'validFrom' => $contract->getValidFrom()->format(format: 'Y-m-d'), // "2017-07-21",
                'validTo' => $contract->getValidTo()->format(format: 'Y-m-d'), // "2017-07-21"
            ]
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Delete a provider
     * Remove a SystemProvider from a Contract.
     */
    #[Route(path: '/rest/{version}/contracts/{contractId}/removeSystemProvider', requirements: ['contractId' => '[\w-]+'], methods: ['DELETE'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractId', in: 'path', example: '456516')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(property: 'systemProviderId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31'),
                ]
            )
        ),
    )]
    #[OA\Response(response: 204, description: 'Removed')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'Contract')]
    public function deleteSystemProvider(Request $request, string $contractId): Response
    {
        $contractRepo = $this->manager->getRepository(Contract::class);

        /** @var Contract|null $contract */
        $contract = $contractRepo->findOneBy(
            criteria: ['contractId' => $contractId]
        );

        if (is_null(value: $contract)) {
            return $this->viewContractNotExist(contractId: $contractId);
        }

        $providerRepo = $this->manager->getRepository(SystemProvider::class);

        /** @var SystemProvider|null $provider */
        $provider = $providerRepo->findOneBy(
            // ['materialId'=>$request->get('materialId')]
            criteria: ['systemProviderId' => $request->get(key: 'systemProviderId')]
        );

        if (is_null(value: $provider)) {
            // return $this->viewProviderNotExist($request->get('materialId'));
            return $this->viewProviderNotExist(providerId: $request->get(key: 'systemProviderId'));
        }

        $contract->removeSystemProvider(systemProvider: $provider);

        $this->manager->persist($contract);

        $this->manager->flush();

        $view = $this->view(data: [], statusCode: 204);

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Delete all providers
     * Remove a SystemProvider from a Contract.
     */
    #[Route(path: '/rest/{version}/contracts/{contractId}/removeAllSystemProviders', requirements: ['contractId' => '[\w-]+'], methods: ['DELETE'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractId', in: 'path', example: '456516')]
    #[OA\Response(response: 204, description: 'Removed')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'Contract')]
    public function deleteAllSystemProviders(Request $request, string $contractId): Response
    {
        $contractRepo = $this->manager->getRepository(Contract::class);

        /** @var Contract|null $contract */
        $contract = $contractRepo->findOneBy(
            criteria: ['contractId' => $contractId]
        );

        if (is_null(value: $contract)) {
            return $this->viewContractNotExist(contractId: $contractId);
        }

        if ($contract->getSystemProviders()->isEmpty()) {
            return $this->viewProviderNotExist(providerId: $request->get(key: 'contractId'));
        }

        foreach ($contract->getSystemProviders() as $provider) {
            $contract->removeSystemProvider(systemProvider: $provider);
        }

        $this->manager->persist($contract);
        $this->manager->flush();

        $view = $this->view(data: [], statusCode: 204);

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Add a provider
     * Add a SystemProvider to a Contract.
     */
    #[Route(path: '/rest/{version}/contracts/{contractId}/addSystemProvider', requirements: ['contractId' => '[\w-]+'], methods: ['POST'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractId', in: 'path', example: '456516')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                properties: [
                    new OA\Property(property: 'systemProviderId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31'),
                ]
            )
        ),
    )]
    #[OA\Response(response: 204, description: 'Added')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'Contract')]
    public function addSystemProvider(Request $request, string $contractId): Response
    {
        $contractRepo = $this->manager->getRepository(Contract::class);

        /** @var Contract|null $contract */
        $contract = $contractRepo->findOneBy(
            criteria: ['contractId' => $contractId]
        );

        if (is_null(value: $contract)) {
            return $this->viewContractNotExist(contractId: $contractId);
        }

        $providerRepo = $this->manager->getRepository(SystemProvider::class);

        /** @var SystemProvider|null $provider */
        $provider = $providerRepo->findOneBy(
            // ['materialId'=>$request->get('materialId')]
            criteria: ['systemProviderId' => $request->get(key: 'systemProviderId')]
        );

        if (is_null(value: $provider)) {
            // return $this->viewProviderNotExist($request->get('materialId'));
            return $this->viewProviderNotExist(providerId: $request->get(key: 'systemProviderId'));
        }

        $contract->addSystemProvider(systemProvider: $provider);

        $this->manager->persist($contract);

        $this->manager->flush();

        $view = $this->view(data: [], statusCode: 204);

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    /**
     * Update Contract
     * Request to update a contract, if the contract does not exist an error is returned.
     *
     * @throws \Exception
     */
    #[Route(path: '/rest/{version}/contracts/{contractId}', requirements: ['contractId' => '[\w-]+'], methods: ['PUT'])]
    #[OA\Parameter(name: 'version', in: 'path', example: 'v1')]
    #[OA\Parameter(name: 'contractId', in: 'path', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31')]
    #[OA\RequestBody(
        content: new OA\MediaType(
            mediaType: 'application/json',
            schema: new OA\Schema(
                required: ['contractId', 'contractAreaId', 'contractNumber', 'validFrom', 'validTo', 'collectingPlaceId', 'unloadingPointId'],
                properties: [
                    new OA\Property(property: 'contractAreaId', type: 'string', example: 'bea7bc08-fcab-42fb-a419-0692a769fe31'),
                    new OA\Property(property: 'contractNumber', type: 'string', example: '456516'),
                    new OA\Property(property: 'validFrom', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2020-10-01'),
                    new OA\Property(property: 'validTo', type: 'string', pattern: '^([0-9]{4}[-])([0-9]{2}[-])([0-9]{2})$', example: '2022-10-01'),
                    new OA\Property(property: 'collectingPlaceId', type: 'string', example: '456-abc'),
                    new OA\Property(property: 'unloadingPointId', type: 'string', example: '123-def'),
                ]
            )
        ),
    )]
    #[OA\Response(response: 200, description: 'Update OK')]
    #[OA\Response(response: 400, description: 'Validation Error')]
    #[OA\Response(response: 404, description: 'Not found')]
    #[OA\Tag(name: 'Contract')]
    public function updateContract(Request $request): Response
    {
        $contractRepo = $this->manager->getRepository(Contract::class);

        $contractId = $request->get(key: 'contractId');

        /** @var Contract|null $contract */
        $contract = $contractRepo->findOneBy(
            criteria: ['contractId' => $contractId]
        );

        if (is_null(value: $contract)) {
            return $this->viewContractNotExist(contractId: $contractId);
        }

        $contractNumber = $request->get(key: 'contractNumber');
        if (is_null(value: $contractNumber)) {
            return $this->viewContractNumberMissing(contractNumber: $contractNumber);
        }

        // check if contractArea is available
        $contractAreaRepo = $this->manager->getRepository(ContractArea::class);

        $area = $contractAreaRepo->findOneBy(
            criteria: ['contractAreaId' => $request->get(key: 'contractAreaId')]
        );

        if (is_null(value: $area)) {
            return $this->viewContractAreaNotFound(contractAreaId: $request->get(key: 'contractAreaId'));
        }

        // check if collectingPlace is available
        $collecingPlaceRepo = $this->manager->getRepository(CollectingPlace::class);

        $collecingPlace = $collecingPlaceRepo->findOneBy(
            criteria: ['collectingPlaceId' => $request->get(key: 'collectingPlaceId')]
        );

        if (is_null(value: $collecingPlace)) {
            return $this->viewCollectingPlaceNotFound(collectingPlaceId: $request->get(key: 'collectingPlaceId'));
        }

        // check if unloadingPoint is available
        $unloadingPointRepo = $this->manager->getRepository(UnloadingPoint::class);

        $unloadingPoint = $unloadingPointRepo->findOneBy(
            criteria: ['unloadingPointId' => $request->get(key: 'unloadingPointId')]
        );

        if (is_null(value: $unloadingPoint)) {
            return $this->viewUnloadingPointNotFound(unloadingPointId: $request->get(key: 'unloadingPointId'));
        }

        // update contract
        $this->contractHelper->saveContract(values: [
            'contractId' => $request->get(key: 'contractId'),
            'contractNumber' => $request->get(key: 'contractNumber'),
            'validFrom' => $request->get(key: 'validFrom'),
            'validTo' => $request->get(key: 'validTo'),
        ], contract: $contract, contractArea: $area, collectingPlace: $collecingPlace, unloadingPoint: $unloadingPoint);

        $view = $this->view(
            data: [
                'id' => $contract->getId(),
                'contractId' => $contract->getContractId(),
                'contractAreaId' => $contract->getContractArea()->getContractAreaId(),
                'collectingPlaceId' => $contract->getCollectingPlace()->getCollectingPlaceId(),
                'unloadingPointId' => $contract->getUnloadingPoint()->getUnloadingPointId(),
                'validFrom' => $contract->getValidFrom()->format(format: 'Y-m-d'), // "2017-07-21",
                'validTo' => $contract->getValidTo()->format(format: 'Y-m-d'), // "2017-07-21"
            ], statusCode: 200
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewContractAreaNotFound(string $contractAreaId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Contract Area for Contract NOT found!',
                'contractAreaId' => $contractAreaId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewCollectingPlaceNotFound(string $collectingPlaceId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Collecting place for Contract NOT found!',
                'contractAreaId' => $collectingPlaceId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewUnloadingPointNotFound(string $unloadingPointId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: UnloadingPoint place for Contract NOT found!',
                'contractAreaId' => $unloadingPointId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewContractNotExist(string $contractId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Contract NOT found!',
                'contractId' => $contractId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewContractNumberMissing(?string $contractNumber): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Contract Number missing!',
                'contractId' => $contractNumber,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewProviderNotExist(string $providerId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Provider NOT found!',
                'contractId' => $providerId,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
