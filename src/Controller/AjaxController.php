<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\Order;
use App\Entity\Main\User;
use App\Repository\Main\ContractAreaRepository;
use App\Repository\Main\OrderRepository;
use App\Services\AccessHelper;
use App\Services\ContractAreaValidityHelper;
use App\Services\Mailer;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class AjaxController.
 */
class AjaxController extends AbstractController
{
    private readonly SessionInterface $session;

    /**
     * DefaultController constructor.
     */
    public function __construct(
        private readonly Mailer $mailer,
        private readonly EntityManagerInterface $manager,
        RequestStack $requestStack,
        private readonly AccessHelper $accessHelper,
        private readonly ContractAreaValidityHelper $cavHelper,
    ) {
        $this->session = $requestStack->getSession();
    }

    /**
     * @throws \Exception
     */
    #[Route(path: '/ajax/transfer-orders', methods: ['POST'])]
    public function transferOrders(Request $request): JsonResponse|RedirectResponse
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return $this->redirectToRoute(route: 'app_default_ordernewclearance');
        }

        $collectingPlaceRepo = $this->manager->getRepository(CollectingPlace::class);

        /** @var ContractAreaRepository $areaRepo */
        $areaRepo = $this->manager->getRepository(ContractArea::class);

        $data = $request->request->all();
        $stdObjPost = json_decode(json: $data['json']);

        /** @var CollectingPlace|null $collectingPlace */
        $collectingPlace = $collectingPlaceRepo->findOneBy(criteria: ['uuid' => $stdObjPost->placeId]);
        /** @var ContractArea|null $area */
        $area = $areaRepo->findOneBy(criteria: ['uuid' => $this->session->get('contractAreaId')]);

        if (false === $this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $area, collectingPlace: $collectingPlace)) {
            return $this->redirectToRoute(route: 'app_default_ordernewclearance');
        }

        $today = new \DateTime();
        $today->modify(modifier: '00:00:00');

        $dtFrom = new \DateTime(datetime: $stdObjPost->from.' 00:00:00'); // YYYY-mm-dd
        $dtTo = new \DateTime(datetime: $stdObjPost->to.'23:59:59'); // YYYY-mm-dd
        /** @var OrderRepository $orderRepo */
        $orderRepo = $this->manager->getRepository(Order::class);

        $transferErrors = [];

        if (is_null(value: $collectingPlace)) {
            return $this->json(data: [
                'areaId' => $area->getId(),
                'collectingPlaceId' => $stdObjPost->placeId,
                'from' => $dtFrom->format(format: 'Y.m.d H:i:s'),
                'to' => $dtTo->format(format: 'Y.m.d H:i:s'),
                'errors' => $transferErrors,
                'message' => 'No Collecting Place found with id '.$stdObjPost->placeId,
                'orderCount' => 0,
            ], status: 404);
        }

        $orderList = $orderRepo->findInBetween(
            startDate: $dtFrom,
            endDate: $dtTo,
            collectingPlace: $collectingPlace,
            contractArea: $area
        );

        $transferErrors = [];

        if (0 < count(value: $orderList)) {
            $weekStart = clone $dtFrom;
            $weekStart = $weekStart->modify(
                modifier: ('Sunday' === $weekStart->format(format: 'l')) ? 'Monday last week' : 'Monday this week'
            );
            $mailerOrderList = [];

            /** @var Order $order */
            foreach ($orderList as $order) {
                if (is_null(value: $order->getTransfered()) && Order::STATUS_TRANSMISSION_READY != $order->getTransferStatus(
                )) {
                    $order->setTransferStatus(transferStatus: Order::STATUS_TRANSMISSION_READY);
                    $this->manager->persist($order);
                    $mailerOrderList[] = $order;

                    // next line comment out - transfer done by command and cron // all orders just marked as transfer ready
                    // $transferErrors = array_merge($transferErrors, $this->orderHelper->send($order));
                }
            }
            $this->manager->flush();

            if ([] !== $mailerOrderList) {
                $this->mailer->sendOrderList(user: $user, orderList: $mailerOrderList, dateFrom: $dtFrom, dateTo: $dtTo, weekStart: $weekStart);
            }
        } else {
            return $this->json(data: [
                'areaId' => $area->getId(),
                'collectingPlaceId' => $stdObjPost->placeId,
                'from' => $dtFrom->format(format: 'Y.m.d H:i:s'),
                'to' => $dtTo->format(format: 'Y.m.d H:i:s'),
                'errors' => $transferErrors,
                'message' => 'No Orders found in time range for this place. '.$stdObjPost->placeId,
                'orderCount' => 0,
            ], status: 404);
        }

        $message = 'All order requests have been transferred.';

        // Handled by SendOpenCollectOrdersCommand
        /*if (0 < count($transferErrors)) {
            $this->mailer->sendErrorMessages($transferErrors);
            $message = 'Not all order requests have been transferred, please check error messages and order states!';
        }*/

        $transferErrors = [];

        return $this->json(data: [
            'areaId' => $area->getId(),
            'collectingPlaceId' => $stdObjPost->placeId,
            'from' => $dtFrom->format(format: 'Y.m.d H:i:s'),
            'to' => $dtTo->format(format: 'Y.m.d H:i:s'),
            'errors' => $transferErrors,
            'message' => $message,
            'orderCount' => count(value: $orderList),
        ]);
    }

    #[Route(path: '/ajax/get-collecting-place/{uuid}', methods: ['GET'])]
    public function getCollectingPlace(
        #[MapEntity(mapping: ['uuid' => 'uuid'])]
        ?CollectingPlace $collectingPlace = null,
    ): JsonResponse|RedirectResponse {
        if (!$collectingPlace instanceof CollectingPlace) {
            return $this->json(data: []);
        }
        $contractList[] = '';

        $user = $this->getUser();
        if (!$user instanceof User || false === $this->accessHelper->checkUserAccessForCollectingPlace(user: $user, collectingPlace: $collectingPlace)) {
            return $this->redirectToRoute(route: 'app_default_ordernewclearance');
        }

        foreach ($collectingPlace->getContracts() as $contract) {
            $area = $contract->getContractArea();
            if ($this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $area, collectingPlace: $collectingPlace)) {
                $validBool = $this->cavHelper->hasValidityForDate(contractArea: $area, date: new \DateTime(datetime: 'NOW'));

                if ($validBool) {
                    $contractList[strval(value: $area->getUuid())] = $area->getName();
                }
            }
        }

        return $this->json(
            data: [
                'dsdId' => $collectingPlace->getDsdId(),
                'name' => $collectingPlace->getName1(),
                'street' => $collectingPlace->getStreet(),
                'houseNumber' => $collectingPlace->getHouseNumber(),
                'postalCode' => $collectingPlace->getPostalCode(),
                'city' => $collectingPlace->getCity(),
                'contractList' => $contractList,
            ]
        );
    }

    #[Route(path: '/ajax/delete-collect-order/{uuid}', methods: ['DELETE'])]
    public function deleteCollectOrder(
        #[MapEntity(mapping: ['uuid' => 'uuid'])]
        Order $order,
    ): JsonResponse|RedirectResponse {
        $user = $this->getUser();
        if (!$user instanceof User || false === $this->accessHelper->checkUserAccessForCollectingPlace(
            user: $user,
            collectingPlace: $order->getcollectingPlace()
        )) {
            return $this->redirectToRoute(route: 'app_default_ordernewclearance');
        }

        if (is_null(value: $order->getTransfered())) {
            $this->manager->remove($order);
            $this->manager->flush();

            return $this->json(
                data: ['success' => true]
            );
        }

        return $this->json(
            data: ['success' => false, 'message' => 'Could not be deleted, order already transferred!']
        );
    }

    #[Route(path: '/ajax/cancel-order/{uuid}', methods: ['DELETE'])]
    public function cancelOrder(
        #[MapEntity(mapping: ['uuid' => 'uuid'])]
        Order $order,
    ): JsonResponse {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return $this->json(data: ['success' => false, 'message' => 'User not authenticated']);
        }

        if (!is_null(value: $order->getTransferStatus()) && is_null(value: $order->getCanceled())) {
            $cancelingTime = new \DateTime();
            $order->setCanceled(canceled: $cancelingTime);
            $orderingTime = $order->getDate();

            $maxTimeLimit = new \DateInterval(duration: 'P2D');
            $timeLimitInHours = $this->getTotalHours(int: $maxTimeLimit);
            // calculating the timediff in hours for the ordering and canceling time
            $diffOrderAndCanceling = date_diff(baseObject: $cancelingTime, targetObject: $orderingTime);
            $diffOACInHours = $this->getTotalHours(int: $diffOrderAndCanceling);
            // bool if the timediff is less than the timelimit for a free cancellation
            $timeLimit = $diffOACInHours <= $timeLimitInHours;

            $this->manager->persist($order);
            $this->manager->flush();

            $this->mailer->sendCancelOrder(user: $user, orderToCancel: $order, timeLimitToCancel: $timeLimit);

            return $this->json(
                data: ['success' => true]
            );
        }

        return $this->json(
            data: ['success' => false, 'message' => 'Could not be canceled, order already canceled!']
        );
    }

    private function getTotalHours(\DateInterval $int): int|float
    {
        return ($int->d * 24) + $int->h + $int->i / 60;
    }
}
