<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class ContractDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $contractNumber;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $collectingPlace;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $unloadingPoint;
}
