<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class DocumentDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $uuid;

    /**
     * @var string
     */
    #[Serializer\Groups(['get', 'list', 'details'])]
    public $number;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $date;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $amount;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $unit;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $visible;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $active;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $documentType;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $contract;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $contractAreaDisplayText;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $detailLink;
}
