<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class ContractAreaUserDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $status;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $email;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $contractArea;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $collectingPlace;
}
