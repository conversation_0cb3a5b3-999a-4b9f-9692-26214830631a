<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class ContractAreaValidityDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $status;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $amountDay;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $amountWeek;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $validFrom;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $validTo;
}
