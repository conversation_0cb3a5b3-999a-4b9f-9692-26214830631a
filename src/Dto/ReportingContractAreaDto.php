<?php

declare(strict_types=1);

namespace App\Dto;

use <PERSON>ymfony\Component\Serializer\Attribute as Serializer;

class ReportingContractAreaDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $status;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $name;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $state;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $validFrom;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $validTo;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $countCollectingPlace;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $countUser;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $detailLink;
}
