<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class ContractAreaCollectingPlaceDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $status;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $dsdId;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $esaId;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $name;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $street;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $houseNumber;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $postalCode;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $city;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $district;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $countUser;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $detailLink;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $permissionLink;
}
