<?php

declare(strict_types=1);

namespace App\Dto;

use Symfony\Component\Serializer\Attribute as Serializer;

class UserDto
{
    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $id;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $status;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $email;

    /**
     * @var string
     */
    #[Serializer\Groups('list')]
    public $locale;

    /**
     * @var array<string>
     */
    #[Serializer\Groups('list')]
    public $roles;

    /**
     * @var array<string>
     */
    #[Serializer\Groups('list')]
    public $assignedRoles;

    /**
     * @var array<string>
     */
    #[Serializer\Groups('list')]
    public $reachableRoles;

    /**
     * @var array<mixed>
     */
    #[Serializer\Groups('list')]
    public $active;

    /**
     * @var array<mixed>
     */
    #[Serializer\Groups('list')]
    public $locked;

    /**
     * @var string
     */
    #[Serializer\Groups(['list'])]
    public $detailLink;
}
