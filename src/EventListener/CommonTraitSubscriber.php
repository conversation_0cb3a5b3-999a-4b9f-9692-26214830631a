<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Entity\Main\User;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Doctrine\Persistence\Event\LifecycleEventArgs;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class CommonTraitSubscriber
{
    private User $user;

    public function __construct(private readonly TokenStorageInterface $tokenStorage)
    {
        $this->user = new User(uuid: Uuid::fromString(uuid: '00000000-0000-0000-0000-000000000000'));
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $this->setCreatedFields(args: $args);
        $this->setUuid(args: $args);
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $this->setUpdatedFields(args: $args);
    }

    /**
     * @param LifecycleEventArgs<EntityManagerInterface> $args
     */
    public function setCreatedFields(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof HasCreateModifyStamps) {
            return;
        }

        if (!$entity->getCreatedAt() instanceof \DateTime) {
            $entity->setCreatedAt(new \DateTime());
        }

        if (!$entity->getCreatedBy() instanceof UuidInterface) {
            $entity->setCreatedBy($this->getUser()->getUuid());
        }
    }

    /**
     * @param LifecycleEventArgs<EntityManagerInterface> $args
     */
    public function setUpdatedFields(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof HasCreateModifyStamps) {
            return;
        }

        $entity->setModifiedAt(new \DateTime());
        $entity->setModifiedBy($this->getUser()->getUuid());
    }

    /**
     * @param LifecycleEventArgs<EntityManagerInterface> $args
     */
    public function setUuid(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof HasUuid) {
            return;
        }

        if (!$entity->getUuid() instanceof UuidInterface) {
            $uuid = Uuid::uuid4();

            $entity->setUuid($uuid);
        }
    }

    private function getUser(): User
    {
        if (!is_null(value: $this->tokenStorage->getToken())) {
            $user = $this->tokenStorage->getToken()->getUser();
            if ($user instanceof User) {
                $this->user = $user;
            }
        }

        return $this->user;
    }
}
