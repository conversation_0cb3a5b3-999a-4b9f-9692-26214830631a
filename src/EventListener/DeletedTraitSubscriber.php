<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Entity\Common\HasDeleted;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Doctrine\Persistence\Event\LifecycleEventArgs;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class DeletedTraitSubscriber
{
    public function prePersist(PrePersistEventArgs $args): void
    {
        $this->setCreatedFields(args: $args);
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $this->setUpdatedFields(args: $args);
    }

    /**
     * @param LifecycleEventArgs<EntityManagerInterface> $args
     */
    public function setCreatedFields(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof HasDeleted) {
            return;
        }

        if (true !== $entity->getDeleted()) {
            $entity->setDeleted(false);
        }
    }

    /**
     * @param LifecycleEventArgs<EntityManagerInterface> $args
     */
    public function setUpdatedFields(LifecycleEventArgs $args): void
    {
        $this->setCreatedFields(args: $args);
    }
}
