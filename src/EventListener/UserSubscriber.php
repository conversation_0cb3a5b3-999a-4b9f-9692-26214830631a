<?php

declare(strict_types=1);

namespace App\EventListener;

use App\Entity\Main\User;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Doctrine\Persistence\Event\LifecycleEventArgs;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class UserSubscriber
{
    public function __construct(private readonly UserPasswordHasherInterface $passwordHasher)
    {
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        if (!$args->getObject() instanceof User) {
            return;
        }
        $this->checkUser(args: $args);
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        if (!$args->getObject() instanceof User) {
            return;
        }
        $this->checkUser(args: $args);
    }

    /**
     * @param LifecycleEventArgs<EntityManagerInterface> $args
     */
    public function checkUser(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if (count(value: array_intersect(['ROLE_ADMIN', 'ROLE_MANAGER'], $entity->getRoles())) > 0 && 0 === preg_match(pattern: '(@mail.schwarz|@prezero.com)', subject: $entity->getEmail())) {
            throw new BadRequestHttpException();
        }

        if (empty($entity->getLocked())) {
            $entity->setLocked(false);
        }

        if (empty($entity->getPassword()) || $entity->getResetPassword()) {
            $entity->setResetPassword(true);
            $entity->setPasswordDate(new \DateTime(datetime: '1900-01-01'));
            $entity->setPassword($this->passwordHasher->hashPassword($entity, Uuid::uuid4()->toString()));
        }

        if (empty($entity->getRoles()) || 1 === count(value: $entity->getRoles())) {
            $entity->setRoles(['ROLE_USER']);
        }
    }
}
