<?php

declare(strict_types=1);

namespace App\EventSubscriber;

use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Translation\LocaleSwitcher;

class LocaleSubscriber implements EventSubscriberInterface
{
    public function __construct(private readonly Security $security, private readonly LocaleSwitcher $localeSwitcher, private readonly bool $languageSwitchEnabled)
    {
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        $user = $this->security->getUser();
        if ($this->languageSwitchEnabled && $user && method_exists(object_or_class: $user, method: 'getLocale') && !empty($user->getLocale())) {
            $this->localeSwitcher->setLocale(locale: $user->getLocale());
        } else {
            $this->localeSwitcher->setLocale(locale: 'de');
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [['onKernelRequest', 0]],
        ];
    }
}
