<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ContractAreaUserDto;
use App\Entity\Main\User;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class ContractAreaUserMapperConfig implements AutoMapperConfiguratorInterface
{
    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(User::class, ContractAreaUserDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('status', function ($entity, $mapper, array $context): string {
                if (isset($context['contractArea'])) {
                    $user = $entity;
                    $contractArea = $context['contractArea'];
                    $collectingPlace = $context['collectingPlace'];

                    $userAccessList = $user->getUserAccessList()->filter(function ($userAccess) use ($contractArea, $collectingPlace): bool {
                        if (!is_null(value: $collectingPlace)) {
                            return $userAccess->getContractArea() === $contractArea && $userAccess->getCollectingPlace() === $collectingPlace;
                        }

                        return $userAccess->getContractArea() === $contractArea;
                    });

                    if (count(value: $userAccessList) > 0) {
                        return 'Zugewiesen';
                    }
                }

                return 'Nicht Zugewiesen';
            })
            ->forMember('email', fn ($entity) => $entity->getEmail())
            ->forMember('contractArea', function ($entity, $mapper, array $context) {
                if (isset($context['contractArea'])) {
                    return $context['contractArea']->getUuid();
                }

                return null;
            })
            ->forMember('collectingPlace', function ($entity, $mapper, array $context) {
                if (isset($context['collectingPlace'])) {
                    return $context['collectingPlace']->getUuid();
                }

                return null;
            })
        ;

        $config->registerMapping(ContractAreaUserDto::class, User::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', Operation::fromProperty(propertyName: 'id'))
            ->forMember('email', Operation::fromProperty(propertyName: 'email'))
            ->forMember('roles', Operation::fromProperty(propertyName: 'roles'))
        ;
    }
}
