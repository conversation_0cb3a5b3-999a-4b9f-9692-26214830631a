<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\DocumentTypeDto;
use App\Entity\Main\DocumentType;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class DocumentTypeMapperConfig implements AutoMapperConfiguratorInterface
{
    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(DocumentType::class, DocumentTypeDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getId())
            ->forMember('name', fn ($entity) => $entity->getName());

        $config->registerMapping(DocumentTypeDto::class, DocumentType::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', Operation::fromProperty(propertyName: 'id'))
            ->forMember('name', Operation::fromProperty(propertyName: 'name'));
    }
}
