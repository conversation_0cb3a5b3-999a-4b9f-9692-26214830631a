<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ContractDto;
use App\Dto\DocumentDto;
use App\Entity\Main\Document;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class DocumentMapperConfig implements AutoMapperConfiguratorInterface
{
    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(Document::class, DocumentDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('number', fn ($entity) => $entity->getNumber())
            ->forMember('date', fn ($entity) => $entity->getDate() ? $entity->getDate()->format('d.m.Y') : null)
            ->forMember('amount', fn ($entity) => $entity->getAmount())
            ->forMember('unit', fn ($entity) => $entity->getUnit())
            ->forMember('visible', fn ($entity) => $entity->getVisible())
            ->forMember('active', fn ($entity) => $entity->getActive())
            ->forMember('contract', Operation::mapTo(destinationClass: ContractDto::class))
            ->forMember('contractAreaDisplayText', fn ($entity) => $entity->getContract() ? ($entity->getContract()->getContractArea() ? $entity->getContract()->getContractArea()->getName() : null) : null)
            ->forMember('detailLink', fn ($entity, $mapper, $context) => $entity->getUuid() ? $context['generateUrl']('documents_file', ['uuid' => $entity->getUuid()]) : null);

        $config->registerMapping(DocumentDto::class, Document::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('customer', fn ($dto, $mapper, $context) => $context['customerRepository']->findOneBy(['uuid' => $dto->customerId]))
            ->forMember('id', Operation::fromProperty(propertyName: 'id'))
            ->forMember('number', Operation::fromProperty(propertyName: 'number'))
            ->forMember('date', Operation::fromProperty(propertyName: 'date'))
            ->forMember('amount', Operation::fromProperty(propertyName: 'amount'))
            ->forMember('unit', Operation::fromProperty(propertyName: 'unit'))
            ->forMember('visible', Operation::fromProperty(propertyName: 'visible'))
            ->forMember('active', Operation::fromProperty(propertyName: 'active'))
            ->forMember('contract', fn ($dto, $mapper, $context) => $context['contractRepository']->findOneBy(['uuid' => $dto->contractId]));
    }
}
