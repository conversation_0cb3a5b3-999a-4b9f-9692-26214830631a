<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ReportingUserDto;
use App\Entity\Main\User;
use App\Services\AccessHelper;
use App\Services\SecurityHelper;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class ReportingUserMapperConfig implements AutoMapperConfiguratorInterface
{
    public function __construct(protected SecurityHelper $securityHelper, protected AccessHelper $accessHelper)
    {
    }

    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(User::class, ReportingUserDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('email', fn ($entity) => $entity->getEmail())
            ->forMember('roles', fn (User $entity) => $this->securityHelper->getRoleHierarchy(user: $entity)['reachableRoles'])
            ->forMember('assignedRoles', fn (User $entity) => $this->securityHelper->getTranslatedRoleHierarchy(user: $entity)['highestRoles'])
            ->forMember('reachableRoles', fn (User $entity) => $this->securityHelper->getTranslatedRoleHierarchy(user: $entity)['reachableRoles'])
            ->forMember('status', fn ($entity): string => $entity->getLocked() ? 'Gesperrt' : 'Aktiv')
            ->forMember('locked', fn ($entity) => $entity->getLocked())
            ->forMember('detailLink', fn ($entity, $mapper, $context) => $entity->getUuid() ? $context['generateUrl']('management_reporting_user_details', ['uuid' => $entity->getUuid()]) : null)
            ->forMember('countCollectingPlace', fn (User $entity): int => count(value: $this->accessHelper->getCollectingPlaceList(user: $entity)))
            ->forMember('countContractArea', fn (User $entity): int => count(value: $this->accessHelper->getContractAreaList(user: $entity)))
        ;

        $config->registerMapping(ReportingUserDto::class, User::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('uuid', Operation::fromProperty(propertyName: 'id'))
            ->forMember('email', Operation::fromProperty(propertyName: 'email'))
            ->forMember('roles', Operation::fromProperty(propertyName: 'roles'))
        ;
    }
}
