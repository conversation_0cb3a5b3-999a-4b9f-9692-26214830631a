<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ContractAreaDto;
use App\Entity\Main\ContractArea;
use App\Services\AccessHelper;
use App\Services\ContractAreaHelper;
use App\Services\ContractAreaValidityHelper;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class ContractAreaMapperConfig implements AutoMapperConfiguratorInterface
{
    public function __construct(protected AccessHelper $accessHelper, protected ContractAreaHelper $contractAreaHelper, protected ContractAreaValidityHelper $contractAreaValidityHelper)
    {
    }

    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(ContractArea::class, ContractAreaDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('status', function ($entity): string {
                if ($this->contractAreaValidityHelper->hasValidityForDate(contractArea: $entity, date: new \DateTime(datetime: 'NOW'))) {
                    return 'Aktiv';
                }

                return 'Inaktiv';
            })
            ->forMember('name', fn ($entity) => $entity->getName())
            ->forMember('state', fn ($entity) => $entity->getState()->getName())
            ->forMember('validFrom', fn ($entity) => $entity->getValidFrom()->format('d.m.Y'))
            ->forMember('validTo', fn ($entity) => $entity->getValidTo()->format('d.m.Y'))
            ->forMember('countCollectingPlace', fn (ContractArea $entity): int => count(value: $this->contractAreaHelper->getCollectingPlaceList(contractArea: $entity)))
            ->forMember('countUser', fn (?ContractArea $entity): int => count(value: $this->accessHelper->getUserList(contractArea: $entity)))
            ->forMember('detailLink', fn ($entity, $mapper, $context) => $entity->getId() ? $context['generateUrl']('management_contractArea_details', ['uuid' => $entity->getUuid()]) : null)
            ->forMember('permissionLink', fn ($entity, $mapper, $context) => $entity->getId() ? $context['generateUrl']('management_contractArea_permission_users', ['uuid' => $entity->getUuid()]) : null)
        ;

        $config->registerMapping(ContractAreaDto::class, ContractArea::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('uuid', Operation::fromProperty(propertyName: 'id'))
            ->forMember('name', Operation::fromProperty(propertyName: 'name'))
            ->forMember('state', Operation::fromProperty(propertyName: 'state'))
            ->forMember('validFrom', Operation::fromProperty(propertyName: 'validFrom'))
            ->forMember('validTo', Operation::fromProperty(propertyName: 'validTo'))
            ->forMember('countCollectingPlace', Operation::fromProperty(propertyName: 'countCollectingPlace'))
            ->forMember('countUser', Operation::fromProperty(propertyName: 'countUser'))
            ->forMember('detailLink', Operation::fromProperty(propertyName: 'detailLink'))
        ;
    }
}
