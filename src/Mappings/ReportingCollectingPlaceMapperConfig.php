<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ReportingCollectingPlaceDto;
use App\Entity\Main\CollectingPlace;
use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class ReportingCollectingPlaceMapperConfig implements AutoMapperConfiguratorInterface
{
    public function __construct(protected AccessHelper $accessHelper, protected CollectingPlaceHelper $collectingPlaceHelper)
    {
    }

    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(CollectingPlace::class, ReportingCollectingPlaceDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('status', function ($entity): string {
                if (!$entity->getLocked()) {
                    return 'Aktiv';
                }

                return 'Inaktiv';
            })
            ->forMember('dsdId', fn ($entity) => $entity->getDsdId())
            ->forMember('esaId', fn ($entity) => $entity->getCollectingPlaceId())
            ->forMember('name', fn ($entity): string => $entity->getName1().' '.$entity->getName2())
            ->forMember('street', fn ($entity) => $entity->getStreet())
            ->forMember('houseNumber', fn ($entity) => $entity->getHouseNumber())
            ->forMember('postalCode', fn ($entity) => $entity->getPostalCode())
            ->forMember('city', fn ($entity) => $entity->getCity())
            ->forMember('district', fn ($entity) => $entity->getDistrict())
            ->forMember('countContractArea', fn (CollectingPlace $entity): int => count(value: $this->collectingPlaceHelper->getContractAreaList(collectingPlace: $entity, locked: true)))
            ->forMember('countUser', fn (?CollectingPlace $entity): int => count(value: $this->accessHelper->getUserList(collectingPlace: $entity)))
            ->forMember('detailLink', fn ($entity, $mapper, $context) => $entity->getUuid() ? $context['generateUrl']('management_reporting_collectingPlace_details', ['uuid' => $entity->getUuid()]) : null)
            ->forMember('locked', fn ($entity) => $entity->getLocked())
        ;

        $config->registerMapping(ReportingCollectingPlaceDto::class, CollectingPlace::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('uuid', Operation::fromProperty(propertyName: 'id'))
            ->forMember('dsdId', Operation::fromProperty(propertyName: 'dsdId'))
            ->forMember('name', Operation::fromProperty(propertyName: 'name1'))
            ->forMember('street', Operation::fromProperty(propertyName: 'street'))
            ->forMember('houseNumber', Operation::fromProperty(propertyName: 'houseNumber'))
            ->forMember('postalCode', Operation::fromProperty(propertyName: 'postalCode'))
            ->forMember('city', Operation::fromProperty(propertyName: 'city'))
            ->forMember('district', Operation::fromProperty(propertyName: 'district'))
            ->forMember('locked', Operation::fromProperty(propertyName: 'locked'));
    }
}
