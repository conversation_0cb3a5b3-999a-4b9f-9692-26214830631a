<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\UserDto;
use App\Entity\Main\User;
use App\Services\SecurityHelper;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class UserMapperConfig implements AutoMapperConfiguratorInterface
{
    public function __construct(protected SecurityHelper $securityHelper)
    {
    }

    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(User::class, UserDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('email', fn ($entity) => $entity->getEmail())
            ->forMember('locale', fn ($entity) => $entity->getLocale())
            ->forMember('roles', fn (User $entity) => $this->securityHelper->getRoleHierarchy(user: $entity)['reachableRoles'])
            ->forMember('assignedRoles', fn (User $entity) => $this->securityHelper->getTranslatedRoleHierarchy(user: $entity)['highestRoles'])
            ->forMember('reachableRoles', fn (User $entity) => $this->securityHelper->getTranslatedRoleHierarchy(user: $entity)['reachableRoles'])
            ->forMember('status', fn ($entity): string => $entity->getLocked() ? 'Gesperrt' : 'Aktiv')
            ->forMember('locked', fn ($entity) => $entity->getLocked())
            ->forMember('detailLink', fn ($entity, $mapper, $context) => $entity->getUuid() ? $context['generateUrl']('management_user_details', ['uuid' => $entity->getUuid()]) : null);

        $config->registerMapping(UserDto::class, User::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('uuid', Operation::fromProperty(propertyName: 'id'))
            ->forMember('email', Operation::fromProperty(propertyName: 'email'))
            ->forMember('locale', Operation::fromProperty(propertyName: 'locale'))
            ->forMember('roles', Operation::fromProperty(propertyName: 'roles'))
        ;
    }
}
