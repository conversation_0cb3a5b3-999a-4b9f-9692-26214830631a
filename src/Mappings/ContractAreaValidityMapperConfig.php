<?php

declare(strict_types=1);

namespace App\Mappings;

use App\Dto\ContractAreaValidityDto;
use App\Entity\Main\ContractAreaValidity;
use App\Services\AccessHelper;
use App\Services\ContractAreaHelper;
use App\Services\ContractAreaValidityHelper;
use AutoMapperPlus\AutoMapperPlusBundle\AutoMapperConfiguratorInterface;
use AutoMapperPlus\Configuration\AutoMapperConfigInterface;
use AutoMapperPlus\MappingOperation\Operation;

class ContractAreaValidityMapperConfig implements AutoMapperConfiguratorInterface
{
    public function __construct(protected AccessHelper $accessHelper, protected ContractAreaHelper $contractAreaHelper, protected ContractAreaValidityHelper $contractAreaValidityHelper)
    {
    }

    public function configure(AutoMapperConfigInterface $config): void
    {
        $config->registerMapping(ContractAreaValidity::class, ContractAreaValidityDto::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('id', fn ($entity) => $entity->getUuid())
            ->forMember('status', function ($entity): string {
                if ($this->contractAreaValidityHelper->isValidForDate(contractAreaValidity: $entity, date: new \DateTime(datetime: 'NOW'))) {
                    return 'Aktiv';
                }

                return 'Inaktiv';
            })
            ->forMember('amountDay', fn ($entity) => $entity->getAmountDay())
            ->forMember('amountWeek', fn ($entity) => $entity->getAmountWeek())
            ->forMember('validFrom', fn ($entity) => $entity->getValidFrom()->format('d.m.Y'))
            ->forMember('validTo', fn ($entity) => $entity->getValidTo()->format('d.m.Y'))
        ;

        $config->registerMapping(ContractAreaValidityDto::class, ContractAreaValidity::class)
            ->withDefaultOperation(Operation::ignore())
            ->forMember('uuid', Operation::fromProperty(propertyName: 'id'))
            ->forMember('amountDay', Operation::fromProperty(propertyName: 'amountDay'))
            ->forMember('amountWeek', Operation::fromProperty(propertyName: 'amountWeek'))
            ->forMember('validFrom', Operation::fromProperty(propertyName: 'validFrom'))
            ->forMember('validTo', Operation::fromProperty(propertyName: 'validTo'))
        ;
    }
}
