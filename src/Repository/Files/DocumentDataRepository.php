<?php

declare(strict_types=1);

namespace App\Repository\Files;

use App\Entity\Files\DocumentData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method DocumentData|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentData|null findOneBy(array<string, mixed> $criteria, array<string, string>|null $orderBy = null)
 * @method DocumentData[]    findAll()
 * @method DocumentData[]    findBy(array<string, mixed> $criteria, array<string, string>|null $orderBy = null, $limit = null, $offset = null)
 *
 * @extends ServiceEntityRepository<DocumentData>
 */
class DocumentDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentData::class);
    }
}
