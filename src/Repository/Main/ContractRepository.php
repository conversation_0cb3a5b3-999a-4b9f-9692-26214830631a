<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class ContractRepository.
 *
 * @extends ServiceEntityRepository<Contract>
 */
class ContractRepository extends ServiceEntityRepository
{
    /**
     * ContractRepository constructor.
     */
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Contract::class);
    }

    public function getContractByData(ContractArea $contractArea, CollectingPlace $collectingPlace, mixed $systemProvider): mixed
    {
        $qb = $this->createQueryBuilder(alias: 'c')
            ->innerJoin(join: 'c.systemProviders', alias: 's')
            ->andWhere('c.contractArea = :contractArea')
            ->andWhere('c.collectingPlace = :collectingPlace')
            ->andWhere('s.id = :systemProvider');

        $qb->setParameter(key: 'contractArea', value: $contractArea)
            ->setParameter(key: 'collectingPlace', value: $collectingPlace)
            ->setParameter(key: 'systemProvider', value: $systemProvider);
        $qb->setMaxResults(maxResults: 1);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
