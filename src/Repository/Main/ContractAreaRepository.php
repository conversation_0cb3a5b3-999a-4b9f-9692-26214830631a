<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\ContractArea;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class ContractAreaRepository.
 *
 * @extends ServiceEntityRepository<ContractArea>
 */
class ContractAreaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ContractArea::class);
    }
}
