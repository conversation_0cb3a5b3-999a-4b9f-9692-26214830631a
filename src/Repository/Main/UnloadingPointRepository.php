<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\UnloadingPoint;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method UnloadingPoint|null find($id, $lockMode = null, $lockVersion = null)
 * @method UnloadingPoint|null findOneBy(array<string, mixed> $criteria, array<string, string>|null $orderBy = null)
 * @method UnloadingPoint[]    findAll()
 * @method UnloadingPoint[]    findBy(array<string, mixed> $criteria, array<string, string>|null $orderBy = null, $limit = null, $offset = null)
 *
 * @extends ServiceEntityRepository<UnloadingPoint>
 */
class UnloadingPointRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UnloadingPoint::class);
    }

    // /**
    //  * @return UnloadingPoint[] Returns an array of UnloadingPoint objects
    //  */
    /*
    public function findByExampleField($value)
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.exampleField = :val')
            ->setParameter('val', $value)
            ->orderBy('u.id', 'ASC')
            ->setMaxResults(10)
            ->getQuery()
            ->getResult()
        ;
    }
    */

    /*
    public function findOneBySomeField($value): ?UnloadingPoint
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.exampleField = :val')
            ->setParameter('val', $value)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }
    */
}
