<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\DocumentData;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DocumentData>
 *
 * @method DocumentData|null find($id, $lockMode = null, $lockVersion = null)
 * @method DocumentData|null findOneBy(array<string, mixed> $criteria, array<string, string> $orderBy = null)
 * @method DocumentData[]    findAll()
 * @method DocumentData[]    findBy(array<string, mixed> $criteria, array<string, string> $orderBy = null, $limit = null, $offset = null)
 */
class DocumentDataRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DocumentData::class);
    }

    /**
     * Finds all UUIDs that already exist from a given list.
     *
     * @param string[] $uuids
     *
     * @return string[] a flat array of UUID strings that were found
     */
    public function findUuidsInList(array $uuids): array
    {
        if ([] === $uuids) {
            return [];
        }

        $qb = $this->createQueryBuilder(alias: 'd');
        $qb->select('d.uuid')
            ->where($qb->expr()->in(x: 'd.uuid', y: ':uuids'))
            ->setParameter(key: 'uuids', value: $uuids);

        // flat
        return array_column(array: $qb->getQuery()->getArrayResult(), column_key: 'uuid');
    }
}
