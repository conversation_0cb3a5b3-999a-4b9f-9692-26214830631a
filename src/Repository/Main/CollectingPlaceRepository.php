<?php

declare(strict_types=1);

namespace App\Repository\Main;

use App\Entity\Main\CollectingPlace;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class CollectingPlaceRepository.
 *
 * @extends ServiceEntityRepository<CollectingPlace>
 */
class CollectingPlaceRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CollectingPlace::class);
    }
}
