<?php

declare(strict_types=1);

namespace App\Security;

use App\Entity\Main\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Http\Authenticator\AbstractLoginFormAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\CsrfTokenBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Symfony\Component\Security\Http\Util\TargetPathTrait;

class LoginFormAuthenticator extends AbstractLoginFormAuthenticator
{
    use TargetPathTrait;

    public const LOGIN_ROUTE = 'app_login';

    /**
     * LoginFormAuthenticator constructor.
     */
    public function __construct(private EntityManagerInterface $entityManager, private UrlGeneratorInterface $urlGenerator)
    {
    }

    public function authenticate(Request $request): Passport
    {
        $email = $request->request->get(key: 'email');
        $password = $request->request->get(key: 'password');

        return new Passport(
            userBadge: new UserBadge(userIdentifier: $email, userLoader: function ($userIdentifier): User {
                // optionally pass a callback to load the User manually
                $user = $this->entityManager
                    ->getRepository(User::class)
                    ->findOneBy(criteria: ['email' => $userIdentifier]);
                if (!$user instanceof User) {
                    throw new UserNotFoundException();
                }

                return $user;
            }),
            credentials: new PasswordCredentials(password: $password),
            badges: [
                new CsrfTokenBadge(
                    csrfTokenId: 'authenticate',
                    csrfToken: $request->request->get(key: '_csrf_token')
                ),
                new RememberMeBadge()->disable(),
            ]
        );
    }

    public function supports(Request $request): bool
    {
        return self::LOGIN_ROUTE === $request->attributes->get(key: '_route')
            && $request->isMethod(method: 'POST');
    }

    /**
     * @throws \Exception
     */
    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): RedirectResponse|Response|null
    {
        if (in_array(needle: 'ROLE_ADMIN', haystack: $token->getUser()->getRoles())) {
            return new RedirectResponse(url: $this->urlGenerator->generate('management'));
        }

        if (in_array(needle: 'ROLE_MANAGER', haystack: $token->getUser()->getRoles())) {
            return new RedirectResponse(url: $this->urlGenerator->generate('management_order_report'));
        }

        if (in_array(needle: 'ROLE_REPORT', haystack: $token->getUser()->getRoles())) {
            return new RedirectResponse(url: $this->urlGenerator->generate('management_order_report'));
        }

        if (in_array(needle: 'ROLE_ORDER', haystack: $token->getUser()->getRoles())) {
            return new RedirectResponse(url: $this->urlGenerator->generate('app_default_orderoverview'));
        }

        if (in_array(needle: 'ROLE_DOCUMENTS', haystack: $token->getUser()->getRoles())) {
            return new RedirectResponse(url: $this->urlGenerator->generate('app_default_documentsview'));
        }

        if ($targetPath = $this->getTargetPath(session: $request->getSession(), firewallName: $firewallName)) {
            return new RedirectResponse(url: $targetPath);
        }

        return new RedirectResponse(url: $this->urlGenerator->generate('app_default_orderoverview'));
    }

    protected function getLoginUrl(Request $request): string
    {
        return $this->urlGenerator->generate(self::LOGIN_ROUTE);
    }
}
