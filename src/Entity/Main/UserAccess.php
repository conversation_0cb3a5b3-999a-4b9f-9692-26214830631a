<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\UserAccessRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: UserAccessRepository::class)]
class UserAccess implements HasUuid, HasCreateModifyStamps
{
    use CommonTrait;

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'userAccessList')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id', nullable: false)]
    private ?User $user = null;

    #[ORM\ManyToOne(targetEntity: ContractArea::class, inversedBy: 'userAccessList')]
    #[ORM\JoinColumn(name: 'contract_area_id', referencedColumnName: 'id', nullable: false)]
    private ?ContractArea $contractArea = null;

    #[ORM\ManyToOne(targetEntity: CollectingPlace::class, inversedBy: 'userAccessList')]
    #[ORM\JoinColumn(name: 'collecting_place_id', referencedColumnName: 'id', nullable: false)]
    private ?CollectingPlace $collectingPlace = null;

    public function __construct()
    {
        $this->init();
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getContractArea(): ?ContractArea
    {
        return $this->contractArea;
    }

    public function setContractArea(?ContractArea $contractArea): self
    {
        $this->contractArea = $contractArea;

        return $this;
    }

    public function getCollectingPlace(): ?CollectingPlace
    {
        return $this->collectingPlace;
    }

    public function setCollectingPlace(?CollectingPlace $collectingPlace): self
    {
        $this->collectingPlace = $collectingPlace;

        return $this;
    }
}
