<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\DocumentDataRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: DocumentDataRepository::class)]
class DocumentData implements HasUuid, HasCreateModifyStamps
{
    use CommonTrait;

    #[ORM\Column(type: Types::STRING, length: 63)]
    private ?string $mimeType = null;

    #[ORM\Column(type: Types::STRING, length: 1024, nullable: true)]
    private ?string $s3FilePath = null;

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(string $mimeType): self
    {
        $this->mimeType = $mimeType;

        return $this;
    }

    public function getS3FilePath(): ?string
    {
        return $this->s3FilePath;
    }

    public function setS3FilePath(?string $s3FilePath): self
    {
        $this->s3FilePath = $s3FilePath;

        return $this;
    }
}
