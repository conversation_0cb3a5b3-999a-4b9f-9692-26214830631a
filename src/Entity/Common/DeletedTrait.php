<?php

declare(strict_types=1);

namespace App\Entity\Common;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait DeletedTrait
{
    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    private ?bool $deleted = null;

    public function getDeleted(): ?bool
    {
        return $this->deleted;
    }

    /**
     * @return $this
     */
    public function setDeleted(bool $deleted): static
    {
        $this->deleted = $deleted;

        return $this;
    }
}
