<?php

declare(strict_types=1);

namespace App\Entity\Common;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait LockedTrait
{
    #[ORM\Column(type: Types::BOOLEAN, options: ['default' => false])]
    private ?bool $locked = null;

    public function getLocked(): ?bool
    {
        return $this->locked;
    }

    /**
     * @return $this
     */
    public function setLocked(bool $locked): static
    {
        $this->locked = $locked;

        return $this;
    }
}
