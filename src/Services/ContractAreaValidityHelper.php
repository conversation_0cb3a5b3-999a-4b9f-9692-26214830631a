<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use Doctrine\ORM\EntityManagerInterface;

class ContractAreaValidityHelper
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * @param array<string, mixed> $values
     *
     * @throws \Exception
     */
    public function createValidity(array $values, ContractArea $contractArea): ContractAreaValidity
    {
        $contractAreaValidity = new ContractAreaValidity();

        return $this->saveValidity(values: $values, contractAreaValidity: $contractAreaValidity, contractArea: $contractArea);
    }

    /**
     * @param array<string, mixed> $values
     *
     * @throws \Exception
     */
    public function saveValidity(array $values, ContractAreaValidity $contractAreaValidity, ContractArea $contractArea): ContractAreaValidity
    {
        $contractAreaValidity->setValidityId(validityId: $values['validityId']);

        $contractAreaValidity->setAmountDay(amountDay: $values['amountDay']);
        $contractAreaValidity->setAmountWeek(amountWeek: $values['amountWeek']);

        $dateTimeFrom = new \DateTime(datetime: $values['validFrom']);
        $contractAreaValidity->setValidFrom(validFrom: $dateTimeFrom);

        $dateTimeTo = new \DateTime(datetime: $values['validTo']);
        $contractAreaValidity->setValidTo(validTo: $dateTimeTo);

        // add validity to contract area
        $contractArea->addContractAreaValidity(contractAreaValidity: $contractAreaValidity);

        $this->manager->persist($contractArea);
        $this->manager->persist($contractAreaValidity);

        $this->manager->flush();

        return $contractAreaValidity;
    }

    public function isValidForDate(ContractAreaValidity $contractAreaValidity, \DateTime $date): bool
    {
        return $contractAreaValidity->getValidFrom() <= $date && $contractAreaValidity->getValidTo() >= $date;
    }

    public function hasValidityForDate(ContractArea $contractArea, \DateTime $date): bool
    {
        if ($contractArea->getValidFrom() > $date) {
            // return false;
        }
        if ($contractArea->getValidTo() < $date) {
            return false;
        }

        foreach ($contractArea->getContractAreaValidities() as $validity) {
            if ($validity->getValidTo() >= $date) {
                return true;
            }
        }

        return false;
    }
}
