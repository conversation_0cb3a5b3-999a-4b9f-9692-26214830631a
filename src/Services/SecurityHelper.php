<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\User;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class SecurityHelper
{
    public function __construct(protected RoleHierarchyInterface $roleHierarchy, protected TranslatorInterface $translator)
    {
    }

    /**
     * @return array<string, array<string>>
     */
    public function getTranslatedRoleHierarchy(User $user): array
    {
        $roles = $this->getRoleHierarchy(user: $user);

        foreach ($roles['highestRoles'] as $key => $highestRole) {
            $roles['highestRoles'][$key] = $this->translator->trans('roles.'.$highestRole);
        }

        foreach ($roles['reachableRoles'] as $key => $reachableRoles) {
            $roles['reachableRoles'][$key] = $this->translator->trans('roles.'.$reachableRoles);
        }

        return $roles;
    }

    /**
     * @return array<string, array<string>>
     */
    public function getRoleHierarchy(User $user): array
    {
        $roles = [
            'highestRoles' => [],
            'reachableRoles' => [],
        ];

        foreach ($user->getRoles() as $role) {
            if (in_array(needle: $role, haystack: $roles['reachableRoles'])) {
                continue;
            }
            $roles['highestRoles'][] = $role;

            foreach ($this->roleHierarchy->getReachableRoleNames([$role]) as $reachableRole) {
                if (!in_array(needle: $reachableRole, haystack: $roles['reachableRoles'])) {
                    $roles['reachableRoles'][] = $reachableRole;
                }
            }
        }

        return $roles;
    }
}
