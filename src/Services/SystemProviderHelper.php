<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\SystemProvider;
use Doctrine\ORM\EntityManagerInterface;

/**
 * Class SystemProviderHelper.
 */
class SystemProviderHelper
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * @param array<string, mixed> $values
     */
    public function createProvider(array $values): SystemProvider
    {
        $systemProvider = new SystemProvider();

        return $this->saveProvider(values: $values, systemProvider: $systemProvider);
    }

    /**
     * @param array<string, mixed> $values
     */
    public function saveProvider(array $values, SystemProvider $systemProvider): SystemProvider
    {
        $systemProvider->setMaterialId(materialId: $values['materialId']);

        $systemProvider->setSystemProviderId(systemProviderId: $values['systemProviderId']);

        $systemProvider->setName(name: $values['name']);

        $systemProvider->setLongtext(longtext: $values['longtext']);

        $systemProvider->setDsdFractionId(dsdFractionId: $values['dsdFractionId']);

        $systemProvider->setDsdFractionName(dsdFractionName: $values['dsdFractionName']);

        $systemProvider->setMonoFraction(monoFraction: $values['monoFraction']);

        $systemProvider->setTransport(transport: $values['transport']);

        $this->manager->persist($systemProvider);

        $this->manager->flush();

        return $systemProvider;
    }
}
