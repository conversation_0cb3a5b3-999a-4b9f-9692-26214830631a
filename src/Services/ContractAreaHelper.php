<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\State;
use Doctrine\ORM\EntityManagerInterface;

class ContractAreaHelper
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * @param array<string, mixed> $values
     *
     * @throws \Exception
     */
    public function createContract(array $values, State $state): ContractArea
    {
        $contractArea = new ContractArea();

        return $this->saveContract(values: $values, contractArea: $contractArea, state: $state);
    }

    /**
     * @param array<string, mixed> $values
     *
     * @throws \Exception
     */
    public function saveContract(array $values, ContractArea $contractArea, State $state): ContractArea
    {
        $contractArea->setContractAreaId(contractAreaId: $values['contractAreaId']);

        $contractArea->setName(name: $values['name']);

        $dateTimeFrom = new \DateTime(datetime: $values['validFrom']);
        $contractArea->setValidFrom(validFrom: $dateTimeFrom);

        $dateTimeTo = new \DateTime(datetime: $values['validTo']);
        $contractArea->setValidTo(validTo: $dateTimeTo);

        $contractArea->setState(state: $state);

        $this->manager->persist($contractArea);

        $this->manager->flush();

        return $contractArea;
    }

    /**
     * @return array<CollectingPlace>
     */
    public function getCollectingPlaceList(ContractArea $contractArea, bool $locked = false): array
    {
        $contracts = $this->manager->getRepository(Contract::class)->findBy(criteria: ['contractArea' => $contractArea]);

        $collectingPlaces = [];
        /** @var Contract $contract */
        foreach ($contracts as $contract) {
            if ($locked || !$contract->getCollectingPlace()->getLocked()) {
                $collectingPlaces[] = $contract->getCollectingPlace();
            }
        }

        return array_unique(array: $collectingPlaces);
    }
}
