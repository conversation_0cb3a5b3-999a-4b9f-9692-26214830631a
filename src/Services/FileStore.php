<?php

declare(strict_types=1);

namespace App\Services;

use Aws\S3\Exception\S3Exception;
use Aws\S3\S3Client;
use Aws\S3\S3ClientInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for interacting with an S3-compatible file storage.
 */
class FileStore
{
    public function __construct(
        private readonly string $bucketName,
        /** @var S3Client&S3ClientInterface $s3Client */
        private readonly S3ClientInterface $s3Client,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * Uploads a file stream to a specified path in the S3 bucket.
     *
     * @param string   $s3Path        the destination path (key) in the S3 bucket
     * @param resource $contentStream the file stream to upload
     *
     * @throws \RuntimeException if the upload fails
     */
    public function upload(string $s3Path, $contentStream): void
    {
        $this->ensureBucketExists();
        try {
            $this->s3Client->putObject([
                'Bucket' => $this->bucketName,
                'Key' => $s3Path,
                'Body' => $contentStream,
            ]);
        } catch (S3Exception $e) {
            $this->logger->error('Failed to upload to S3 at path {path}: {message}', [
                'path' => $s3Path,
                'message' => $e->getMessage(),
                'exception' => $e,
            ]);
            throw new \RuntimeException(message: 'S3 upload failed.', code: $e->getCode(), previous: $e);
        }
    }

    /**
     * Retrieves a file from S3 as a stream resource.
     *
     * @param string $s3Path the path (key) of the file in the S3 bucket
     *
     * @return resource|null the file stream resource, or null if the file is not found or an error occurs
     */
    public function getStream(string $s3Path)
    {
        $this->ensureBucketExists();
        try {
            $result = $this->s3Client->getObject([
                'Bucket' => $this->bucketName,
                'Key' => $s3Path,
            ]);

            return $result['Body']->detach();
        } catch (S3Exception $e) {
            if ('NoSuchKey' === $e->getAwsErrorCode()) {
                $this->logger->warning('File not found in S3 at path: {path}', ['path' => $s3Path]);
            } else {
                $this->logger->error('Error getting stream for S3 path {path}: {message}', [
                    'path' => $s3Path,
                    'message' => $e->getMessage(),
                    'exception' => $e,
                ]);
            }

            return null;
        }
    }

    /**
     * Deletes a file from the S3 bucket.
     *
     * @param string $s3Path the path (key) of the file to delete
     */
    public function delete(string $s3Path): void
    {
        $this->ensureBucketExists();
        try {
            $this->s3Client->deleteObject([
                'Bucket' => $this->bucketName,
                'Key' => $s3Path,
            ]);
        } catch (S3Exception $e) {
            $this->logger->error('Failed to delete S3 object at path {path}: {message}', [
                'path' => $s3Path,
                'message' => $e->getMessage(),
                'exception' => $e,
            ]);
        }
    }

    /**
     * Generates a consistent S3 key for a file based on its UUID.
     *
     * @param string $uuid the UUID of the file
     */
    public function getS3Key(string $uuid): string
    {
        return 'files/'.$uuid;
    }

    /**
     * Ensures the configured S3 bucket exists, creating it if necessary.
     */
    private function ensureBucketExists(): void
    {
        try {
            $this->s3Client->headBucket(['Bucket' => $this->bucketName]);
        } catch (S3Exception $e) {
            if ('NotFound' === $e->getAwsErrorCode() || 404 === $e->getStatusCode()) {
                try {
                    $this->s3Client->createBucket(['Bucket' => $this->bucketName]);
                    $this->s3Client->waitUntil(name: 'BucketExists', args: ['Bucket' => $this->bucketName]);
                    $this->logger->info('Created S3 bucket: {bucket}', ['bucket' => $this->bucketName]);
                } catch (S3Exception $createException) {
                    $this->logger->error('Failed to create S3 bucket {bucket}: {message}', [
                        'bucket' => $this->bucketName,
                        'message' => $createException->getMessage(),
                        'exception' => $createException,
                    ]);
                    throw new \RuntimeException(message: 'Failed to create S3 bucket.', code: $e->getCode(), previous: $createException);
                }
            } else {
                throw $e;
            }
        }
    }
}
