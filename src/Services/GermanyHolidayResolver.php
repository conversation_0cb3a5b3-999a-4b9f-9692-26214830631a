<?php

declare(strict_types=1);

namespace App\Services;

use Symfony\Contracts\Translation\TranslatorInterface;

class GermanyHolidayResolver implements HolidayResolverInterface
{
    private const string TRANSLATION_DOMAIN = 'holidays';

    public function __construct(
        private readonly TranslatorInterface $translator,
    ) {
    }

    /**
     * @param array<string> $states
     *
     * @return false|array<string, mixed>
     */
    public function resolveHoliday(string $date, array $states): false|array
    {
        [$year, $month, $day] = explode(separator: '-', string: $date);

        if (!checkdate(month: (int) $month, day: (int) $day, year: (int) $year)) {
            return false;
        }

        $easterTimestamp = easter_date(year: (int) $year);
        $easterBase = new \DateTime(datetime: "@$easterTimestamp");
        $easterBase->setTimezone(timezone: new \DateTimeZone(timezone: date_default_timezone_get()));

        $rules = [
            '0101' => $this->translate(key: 'holiday.new_year'),
            '0106' => [$this->translate(key: 'holiday.epiphany'), ['BW', 'BY', 'ST']],
            $this->formatDate(date: (clone $easterBase)->modify(modifier: '-2 days')) => $this->translate(key: 'holiday.good_friday'),
            $this->formatDate(date: clone $easterBase) => $this->translate(key: 'holiday.easter_sunday'),
            $this->formatDate(date: (clone $easterBase)->modify(modifier: '+1 day')) => $this->translate(key: 'holiday.easter_monday'),
            '0501' => $this->translate(key: 'holiday.labour_day'),
            $this->formatDate(date: (clone $easterBase)->modify(modifier: '+39 days')) => $this->translate(key: 'holiday.ascension_day'),
            $this->formatDate(date: (clone $easterBase)->modify(modifier: '+49 days')) => $this->translate(key: 'holiday.pentecost_sunday'),
            $this->formatDate(date: (clone $easterBase)->modify(modifier: '+50 days')) => $this->translate(key: 'holiday.pentecost_monday'),
            $this->formatDate(date: (clone $easterBase)->modify(modifier: '+60 days')) => [$this->translate(key: 'holiday.corpus_christi'), ['BW', 'BY', 'HE', 'NW', 'RP', 'SL', 'SN', 'TH']],
            '0815' => [$this->translate(key: 'holiday.assumption_day'), ['SL', 'BY']],
            '1003' => $this->translate(key: 'holiday.german_unity_day'),
            '1031' => [$this->translate(key: 'holiday.reformation_day'), ['BB', 'MV', 'SN', 'ST', 'TH']],
            '1101' => [$this->translate(key: 'holiday.all_saints'), ['BW', 'BY', 'NW', 'RP', 'SL']],
            '1224' => $this->translate(key: 'holiday.christmas_eve'),
            '1225' => $this->translate(key: 'holiday.christmas_day_1'),
            '1226' => $this->translate(key: 'holiday.christmas_day_2'),
            '1231' => $this->translate(key: 'holiday.new_years_eve'),
        ];

        if ($this->isDayOfRepentanceAndPrayer(year: $year, month: $month, day: $day) && in_array(needle: 'SN', haystack: $states, strict: true)) {
            return ['boolean' => true, 'name' => $this->translate(key: 'holiday.repentance_day')];
        }

        $monthDay = str_pad(string: $month, length: 2, pad_string: '0', pad_type: STR_PAD_LEFT).str_pad(string: $day, length: 2, pad_string: '0', pad_type: STR_PAD_LEFT);

        if (isset($rules[$monthDay])) {
            $value = $rules[$monthDay];

            if (is_array(value: $value)) {
                [$name, $validStates] = $value;
                if (count(value: array_intersect($states, $validStates)) > 0) {
                    return ['boolean' => true, 'name' => $name];
                }

                return ['boolean' => false];
            }

            return ['boolean' => true, 'name' => $value];
        }

        return ['boolean' => false];
    }

    private function formatDate(\DateTime $date): string
    {
        return $date->format(format: 'md');
    }

    private function isDayOfRepentanceAndPrayer(string $year, string $month, string $day): bool
    {
        if ('11' !== $month) {
            return false;
        }

        $dayInt = (int) $day;
        if ($dayInt < 16 || $dayInt > 22) {
            return false;
        }

        $date = new \DateTime(datetime: "$year-$month-$day");

        // 1 (for Monday) through 7 (for Sunday)
        return '3' === $date->format(format: 'N');
    }

    private function translate(string $key): string
    {
        return $this->translator->trans($key, domain: self::TRANSLATION_DOMAIN);
    }

    public function supports(string $countryCode): bool
    {
        return 'DE' === $countryCode;
    }
}
