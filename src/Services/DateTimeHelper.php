<?php

declare(strict_types=1);

namespace App\Services;

/**
 * Class DateTimeHelper.
 */
class DateTimeHelper
{
    /**
     * @return array<string, int>
     *
     * @throws \Exception
     */
    public function getInfoStringsWeek(int $count): array
    {
        $weekStrResults = [];
        for ($i = 0; $i < $count; ++$i) {
            $weekInfo = 'KW ';
            $dateTime = $this->addWeeks(addWeekCount: $i);
            // if NOT monday (1) go back to last monday
            if (1 !== intval(value: $dateTime->format(format: 'N'))) {
                $dateTime->modify(modifier: 'last monday');
            }
            $weekInfo .= $dateTime->format(format: 'W').': '.$dateTime->format(format: 'd.m');
            $dateTime->modify(modifier: 'next saturday');
            $weekInfo .= ' - '.$dateTime->format(format: 'd.m.Y');
            $weekStrResults[$weekInfo] = $i;
        }

        return $weekStrResults;
    }

    /**
     * @throws \Exception
     */
    public function addWeeks(int $addWeekCount): \DateTime
    {
        $dateTime = new \DateTime();
        if (1 === $addWeekCount) {
            $dateTime->modify(modifier: '+'.$addWeekCount.' week');
        } elseif (1 < $addWeekCount && 6 >= $addWeekCount) {
            $dateTime->modify(modifier: '+'.$addWeekCount.' week');
        } elseif (7 <= $addWeekCount) {
            throw new \Exception(message: 'Sorry, only 6 weeks into future could be handled!');
        }

        return $dateTime;
    }

    /**
     * @return array<CalendarDay>
     */
    public function getWeekFromMonToSat(\DateTime $dateTime): array
    {
        if (1 !== intval(value: $dateTime->format(format: 'N'))) {
            $dateTime->modify(modifier: 'last monday');
        } else {
            $dateTime->setTime(hour: 0, minute: 0);
        }
        $end = clone $dateTime;

        return $this->getDatesFromRange(start: $dateTime, end: $end->modify(modifier: '+5 days'));
    }

    /**
     * @return array<CalendarDay>
     */
    public function getDatesFromRange(\DateTime $start, \DateTime $end, string $format = 'd-m-Y'): array
    {
        $aryResult = [];

        // P1D = period 1 day
        $interval = new \DateInterval(duration: 'P1D');

        $end->add(interval: $interval);

        $period = new \DatePeriod($start, $interval, $end);

        // Store every date from period
        foreach ($period as $date) {
            $calendarDay = new CalendarDay(dtObject: $date);
            $aryResult[] = $calendarDay;
        }

        return $aryResult;
    }
}
