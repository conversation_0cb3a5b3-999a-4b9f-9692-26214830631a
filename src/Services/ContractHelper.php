<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\SystemProvider;
use App\Entity\Main\UnloadingPoint;
use Doctrine\ORM\EntityManagerInterface;

class ContractHelper
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * @param array<string, mixed> $values
     */
    public function createContract(array $values, ContractArea $area, CollectingPlace $collectingPlace, UnloadingPoint $unloadingPoint): Contract
    {
        $contract = new Contract();

        return $this->saveContract(values: $values, contract: $contract, contractArea: $area, collectingPlace: $collectingPlace, unloadingPoint: $unloadingPoint);
    }

    /**
     * @param array<string, mixed> $values
     */
    public function saveContract(array $values, Contract $contract, ContractArea $contractArea, CollectingPlace $collectingPlace, UnloadingPoint $unloadingPoint, ?SystemProvider $systemProvider = null): Contract
    {
        $contract->setContractId(contractId: $values['contractId']);

        $contract->setContractNumber(contractNumber: $values['contractNumber']);

        $dateTimeFrom = new \DateTime(datetime: $values['validFrom']);
        $contract->setValidFrom(validFrom: $dateTimeFrom);

        $dateTimeTo = new \DateTime(datetime: $values['validTo']);
        $contract->setValidTo(validTo: $dateTimeTo);

        $contract->setContractArea(contractArea: $contractArea);

        $contract->setCollectingPlace(collectingPlace: $collectingPlace);

        $contract->setUnloadingPoint(unloadingPoint: $unloadingPoint);

        if ($systemProvider instanceof SystemProvider) {
            $contract->addSystemProvider(systemProvider: $systemProvider);
        }

        $this->manager->persist($contract);

        $this->manager->flush();

        return $contract;
    }
}
