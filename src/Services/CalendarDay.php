<?php

declare(strict_types=1);

namespace App\Services;

class CalendarDay
{
    private bool $isPublicHoliday = false;

    private string $publicHolidayName = '';

    private int $orderCount = 0;

    private int $orderCountMax = 0;

    /**
     * @var array<string>
     */
    private array $validityMessages = [];

    private bool $isTransferable = true;

    private bool $isOrderAble = true;

    /**
     * CalendarDay constructor.
     */
    public function __construct(private readonly \DateTime $dtObject)
    {
    }

    /**
     * Returns name of the day: 'Mon, Tue, Wed, Thu, Fri, Sat, Sun'.
     */
    public function getName(): string
    {
        return $this->dtObject->format(format: 'D');
    }

    public function getIsPublicHoliday(): bool
    {
        return $this->isPublicHoliday;
    }

    /**
     * @return $this
     */
    public function setIsPublicHoliday(bool $isPublicHoliday): static
    {
        $this->isPublicHoliday = $isPublicHoliday;

        return $this;
    }

    public function isOrderAble(): bool
    {
        return $this->isOrderAble;
    }

    public function setIsOrderAble(bool $isOrderAble): void
    {
        $this->isOrderAble = $isOrderAble;
    }

    public function getPublicHolidayName(): string
    {
        return $this->publicHolidayName;
    }

    public function setPublicHolidayName(string $publicHolidayName): CalendarDay
    {
        $this->publicHolidayName = $publicHolidayName;

        return $this;
    }

    public function getDtObject(): \DateTime
    {
        return $this->dtObject;
    }

    public function addValidityMessage(string $message): void
    {
        $this->validityMessages[] = $message;
    }

    /**
     * @return array<string>
     */
    public function getValidityMessages(): array
    {
        return $this->validityMessages;
    }

    public function getOrderCount(): int
    {
        return $this->orderCount;
    }

    public function setOrderCount(int $orderCount): CalendarDay
    {
        $this->orderCount = $orderCount;

        return $this;
    }

    public function getOrderCountMax(): int
    {
        return $this->orderCountMax;
    }

    public function setOrderCountMax(int $orderCountMax): CalendarDay
    {
        $this->orderCountMax = $orderCountMax;

        return $this;
    }

    public function isTransferable(): bool
    {
        return $this->isTransferable;
    }

    public function setIsTransferable(bool $isTransferable): CalendarDay
    {
        $this->isTransferable = $isTransferable;

        return $this;
    }
}
