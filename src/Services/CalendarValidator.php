<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use App\Entity\Main\Order;

class CalendarValidator
{
    /**
     * @var array<string, int>
     *
     * @example ['20210419' => 1, '20210420' => 2,] key is day, value is count of canceled orders
     */
    private array $cancelledCollects = [];

    /**
     * CalendarValidator constructor.
     *
     * @param ContractAreaValidity[]|null $validities
     * @param Order[]|null                $collects
     * @param Order[]|null                $cancelledCollects
     */
    public function __construct(
        private readonly ContractArea $area,
        private readonly ?array $validities,
        private readonly ?WeekDayIterator $weekIterator,
        private readonly ?array $collects,
        ?array $cancelledCollects)
    {
        $this->initCancelledCollects(cancelledCollects: $cancelledCollects);
    }

    /**
     * @param Order[]|null $cancelledCollects
     */
    private function initCancelledCollects(?array $cancelledCollects): void
    {
        if ($cancelledCollects) {
            foreach ($cancelledCollects as $order) {
                if ($order->getCanceled()) {
                    $key = $order->getDate()->format(format: 'Ymd');
                    if (array_key_exists(key: $key, array: $this->cancelledCollects)) {
                        ++$this->cancelledCollects[$key];
                    } else {
                        $this->cancelledCollects[$key] = 1;
                    }
                }
            }
        }
    }

    public function inWeekOrderLimit(CalendarDay $day, int $canceledDates): static
    {
        $validityCount = 0;
        // counting the possible dates for ordering
        $countPossibleDates = count(value: $this->collects) - $canceledDates;

        foreach ($this->validities as $validity) {
            $validityCount += $validity->getAmountWeek();
        }

        if ($countPossibleDates >= $validityCount) {
            $day->setIsOrderAble(isOrderAble: false);
            $day->addValidityMessage(message: 'Weekly Limit is exhausted: '.count(value: $this->collects).' Bookings. Validity Limit '.$validityCount);
        }

        $day->addValidityMessage(message: 'Weekly Limit Numbers: '.count(value: $this->collects).' Bookings. Validity Limit '.$validityCount);

        return $this;
    }

    public function checkAndSetOrderLimit(CalendarDay $day): static
    {
        $validity = $this->getValidity(calendarDay: $day);

        switch ($day->getName()) {
            case 'Mon':
                $this->calculateDailyOrderCount(day: $day, maxDayCount: $this->weekIterator->lastElementMon());

                if ($day->getOrderCount() >= $validity->getAmountDay()) {
                    $day->addValidityMessage(message: 'Mon: Day Limit is exhausted: '.
                        $this->weekIterator->lastElementMon().' Validity.dayAmount: '.$validity->getAmountDay()
                    );

                    $day->setIsOrderAble(isOrderAble: false);
                }

                break;

            case 'Tue':
                $this->calculateDailyOrderCount(day: $day, maxDayCount: $this->weekIterator->lastElementTue());

                if ($day->getOrderCount() >= $validity->getAmountDay()) {
                    $day->addValidityMessage(message: 'Tue: Day Limit is exhausted:  '.
                        $this->weekIterator->lastElementTue().' Validity.dayAmount: '.$validity->getAmountDay()
                    );

                    $day->setIsOrderAble(isOrderAble: false);
                }

                break;

            case 'Wed':
                $this->calculateDailyOrderCount(day: $day, maxDayCount: $this->weekIterator->lastElementWed());

                if ($day->getOrderCount() >= $validity->getAmountDay()) {
                    $day->addValidityMessage(message: 'Wed: Day Limit is exhausted: '.
                        $this->weekIterator->lastElementWed().' Validity.dayAmount: '.$validity->getAmountDay()
                    );

                    $day->setIsOrderAble(isOrderAble: false);
                }

                break;

            case 'Thu':
                $this->calculateDailyOrderCount(day: $day, maxDayCount: $this->weekIterator->lastElementThu());

                if ($day->getOrderCount() >= $validity->getAmountDay()) {
                    $day->addValidityMessage(message: 'Thu: Day Limit is exhausted: '.
                        $this->weekIterator->lastElementThu().' Validity.dayAmount: '.$validity->getAmountDay()
                    );

                    $day->setIsOrderAble(isOrderAble: false);
                }

                break;

            case 'Fri':
                $this->calculateDailyOrderCount(day: $day, maxDayCount: $this->weekIterator->lastElementFri());

                if ($day->getOrderCount() >= $validity->getAmountDay()) {
                    $day->addValidityMessage(message: 'Fri: Day Limit is exhausted: '.
                        $this->weekIterator->lastElementFri().' Validity.dayAmount: '.$validity->getAmountDay()
                    );

                    $day->setIsOrderAble(isOrderAble: false);
                }

                break;

            case 'Sat':
                $this->calculateDailyOrderCount(day: $day, maxDayCount: $this->weekIterator->lastElementSat());

                if ($day->getOrderCount() >= $validity->getAmountDay()) {
                    $day->addValidityMessage(message: 'Sat: Day Limit is exhausted: '.
                        $this->weekIterator->lastElementSat().' Validity.dayAmount: '.$validity->getAmountDay()
                    );

                    $day->setIsOrderAble(isOrderAble: false);
                }

                break;
        }

        return $this;
    }

    public function inValidityTimeRange(CalendarDay $day): static
    {
        $validity = $this->getValidity(calendarDay: $day);

        // Validity: Check if date is bookable in Validity! 'validFrom' and 'validTo'
        if ($day->getDtObject() < $validity->getValidFrom()) {
            $day->setIsOrderAble(isOrderAble: false);
            $day->addValidityMessage(message: 'Validity: Day is before the Validity constraints.');
        }

        // Validity
        if ($day->getDtObject() > $validity->getValidTo()) {
            $day->setIsOrderAble(isOrderAble: false);
            $day->addValidityMessage(message: 'Validity: Day is after the Validity constraints.');
        }

        return $this;
    }

    public function inAreaTimeRange(CalendarDay $day): static
    {
        // Area: Check if date is bookable in area! 'validFrom' and 'validTo'
        if ($day->getDtObject() < $this->area->getValidFrom()) {
            $day->setIsOrderAble(isOrderAble: false);
            $day->addValidityMessage(message: 'Area: Day is before the Area is valid.');
        }

        // Area
        if ($day->getDtObject() > $this->area->getValidTo()) {
            $day->setIsOrderAble(isOrderAble: false);
            $day->addValidityMessage(message: 'Area: Day is after the last day the Area is valid.');
        }

        return $this;
    }

    public function dayToOld(CalendarDay $day): static
    {
        $limitingDay = new \DateTime();
        // $limitingDay->modify('next monday');
        $limitingDay->setTime(hour: 0, minute: 0);
        if ($day->getDtObject() < $limitingDay) {
            $day->setIsOrderAble(isOrderAble: false); // Also set to 'false' if limits reached! Reason for next value!
            $day->setIsTransferable(isTransferable: false); // If day is outdated no transfer is allowed
            $day->addValidityMessage(message: 'Day is out dated. '.
                $day->getDtObject()->format(format: 'd.m.Y h:i:s').'<'.$limitingDay->format(format: 'd.m.Y h:i:s')
            );
        }

        return $this;
    }

    public function isAlreadyTransfered(CalendarDay $day): void
    {
        $transferable = false;

        foreach ($this->collects as $collect) {
            if ($day->getDtObject() == $collect->getDate() && empty($collect->getTransferStatus())) {
                $transferable = true;
            }
        }

        $day->setIsTransferable(isTransferable: $transferable);
    }

    public function validityNull(): bool
    {
        return is_null(value: $this->validities);
    }

    private function getValidity(CalendarDay $calendarDay): ContractAreaValidity
    {
        foreach ($this->validities as $validity) {
            if ($calendarDay->getDtObject()->format(format: 'Y-m-d') >= $validity->getValidFrom()->format('Y-m-d')
                    && $calendarDay->getDtObject()->format(format: 'Y-m-d') <= $validity->getValidTo()->format('Y-m-d')) {
                return $validity;
            }
        }

        return new ContractAreaValidity();
    }

    protected function calculateDailyOrderCount(CalendarDay $day, int $maxDayCount): void
    {
        $cancelledKey = $day->getDtObject()->format(format: 'Ymd');

        if (array_key_exists(key: $cancelledKey, array: $this->cancelledCollects)) {
            $leftOrders = $maxDayCount - $this->cancelledCollects[$cancelledKey];

            if ($leftOrders < 0) {
                $day->setOrderCount(orderCount: 0);
            } else {
                $day->setOrderCount(orderCount: $leftOrders);
            }
        } else {
            $day->setOrderCount(orderCount: $maxDayCount);
        }
    }
}
