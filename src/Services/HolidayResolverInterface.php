<?php

declare(strict_types=1);

namespace App\Services;

use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag('app.holiday_resolver')]
interface HolidayResolverInterface
{
    /**
     * @param array<string> $states
     *
     * @return false|array<string, mixed>
     */
    public function resolveHoliday(string $date, array $states): false|array;

    public function supports(string $countryCode): bool;
}
