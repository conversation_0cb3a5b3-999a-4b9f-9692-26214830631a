<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\State;
use App\Entity\Main\UnloadingPoint;
use Doctrine\ORM\EntityManagerInterface;

class UnloadingPointHelper
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * @param array<string, mixed> $values
     */
    public function createPlace(array $values): UnloadingPoint
    {
        $unloadingPoint = new UnloadingPoint();

        return $this->saveUnloadingPoint(values: $values, unloadingPoint: $unloadingPoint);
    }

    /**
     * @param array<string, mixed> $values
     */
    public function saveUnloadingPoint(array $values, UnloadingPoint $unloadingPoint): UnloadingPoint
    {
        $stateRepo = $this->manager->getRepository(State::class);
        $unloadingPoint->setUnloadingPointId(unloadingPointId: $values['unloadingPointId']);

        if ($values['dsdId']) {
            $unloadingPoint->setDsdId(dsdId: $values['dsdId']);
        }

        if ($values['name1']) {
            $unloadingPoint->setName1(name1: $values['name1']);
        }

        if ($values['name2']) {
            $unloadingPoint->setName2(name2: $values['name2']);
        }

        if ($values['street']) {
            $unloadingPoint->setStreet(street: $values['street']);
        }

        if ($values['houseNumber']) {
            $unloadingPoint->setHouseNumber(houseNumber: $values['houseNumber']);
        }

        if ($values['postalCode']) {
            $unloadingPoint->setPostalCode(postalCode: $values['postalCode']);
        }

        if ($values['city']) {
            $unloadingPoint->setCity(city: $values['city']);
        }

        if ($values['district']) {
            $unloadingPoint->setDistrict(district: $values['district']);
        }

        if ($values['state']) {
            $unloadingPoint->setState(state: $stateRepo->findOneBy(criteria: ['shortName' => $values['state']]));
        }

        // TODO
        $unloadingPoint->setCountry(country: 'DE');
        if ($values['country']) {
            $unloadingPoint->setCountry(country: $values['country']);
        }

        $this->manager->persist($unloadingPoint);

        $this->manager->flush();

        return $unloadingPoint;
    }
}
