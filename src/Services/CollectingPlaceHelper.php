<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\Contract;
use App\Entity\Main\ContractArea;
use App\Entity\Main\State;
use Doctrine\ORM\EntityManagerInterface;

class CollectingPlaceHelper
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager)
    {
    }

    /**
     * @param array<string, mixed> $values
     */
    public function createPlace(array $values): CollectingPlace
    {
        $collectingPlace = new CollectingPlace();

        return $this->saveCollectingPlace(values: $values, collectingPlace: $collectingPlace);
    }

    /**
     * @param array<string, mixed> $values
     */
    public function saveCollectingPlace(array $values, CollectingPlace $collectingPlace): CollectingPlace
    {
        $stateRepo = $this->manager->getRepository(State::class);
        $collectingPlace->setCollectingPlaceId(collectingPlaceId: $values['collectingPlaceId']);

        if ($values['dsdId']) {
            $collectingPlace->setDsdId(dsdId: $values['dsdId']);
        }

        if ($values['name1']) {
            $collectingPlace->setName1(name1: $values['name1']);
        }

        if ($values['name2']) {
            $collectingPlace->setName2(name2: $values['name2']);
        }

        if ($values['street']) {
            $collectingPlace->setStreet(street: $values['street']);
        }

        if ($values['houseNumber']) {
            $collectingPlace->setHouseNumber(houseNumber: $values['houseNumber']);
        }

        if ($values['postalCode']) {
            $collectingPlace->setPostalCode(postalCode: $values['postalCode']);
        }

        if ($values['city']) {
            $collectingPlace->setCity(city: $values['city']);
        }

        if ($values['district']) {
            $collectingPlace->setDistrict(district: $values['district']);
        }

        if ($values['state']) {
            $collectingPlace->setState(state: $stateRepo->findOneBy(criteria: ['shortName' => $values['state']]));
        }

        // TODO
        $collectingPlace->setCountry(country: 'DE');
        if ($values['country']) {
            $collectingPlace->setCountry(country: $values['country']);
        }

        $this->manager->persist($collectingPlace);

        $this->manager->flush();

        return $collectingPlace;
    }

    /**
     * @return array<ContractArea>
     */
    public function getContractAreaList(CollectingPlace $collectingPlace, bool $locked = false): array
    {
        $contracts = $this->manager->getRepository(Contract::class)->findBy(criteria: ['collectingPlace' => $collectingPlace]);

        $contractAreas = [];
        /** @var Contract $contract */
        foreach ($contracts as $contract) {
            if ($locked || !$contract->getCollectingPlace()->getLocked()) {
                $contractAreas[] = $contract->getContractArea();
            }
        }

        return array_unique(array: $contractAreas);
    }
}
