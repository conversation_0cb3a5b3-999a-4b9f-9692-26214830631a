<?php

declare(strict_types=1);

namespace App\Services;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\Order;
use App\Repository\Main\ContractAreaValidityRepository;
use App\Repository\Main\OrderRepository;

class OrderCollectCalculator
{
    public function __construct(private readonly \DateTime $start, private readonly \DateTime $end, private readonly OrderRepository $orderRepo)
    {
    }

    /**
     * @param array<mixed> $week
     */
    public function getMaximumOrderCount(
        ContractArea $area,
        ContractAreaValidityRepository $areaValidityRepo,
        array $week,
        int $countOwnCollects,
        int $countOwnCancelleledCollects): int|float
    {
        $validities = $areaValidityRepo->getValidities(contractArea: $area, week: $week);

        if (!is_null(value: $validities)) {
            $orderCountArea = $this->orderRepo->getOrderCountByArea(
                startDate: $week[0]->getDtObject(),
                endDate: $week[5]->getDtObject(),
                contractArea: $area
            );

            $validityOrderWeekCount = 0;

            foreach ($validities as $validity) {
                $validityOrderWeekCount += $validity->getAmountWeek();
            }

            // need to substract OWN cancelledCollects because they are not included in orderCountArea but already removed from weekly amount!
            $validityOrderWeekCount = $validityOrderWeekCount + $countOwnCollects - $countOwnCancelleledCollects - $orderCountArea;

            return $validityOrderWeekCount;
        } else {
            return 0;
        }
    }

    /**
     * @return array<Order>
     */
    public function getCollectOrders(CollectingPlace $collectingPlace, ContractArea $area): array
    {
        return $this->orderRepo->findInBetween(startDate: $this->start, endDate: $this->end, collectingPlace: $collectingPlace, contractArea: $area);
    }

    /**
     * @param array<Order> $orders
     *
     * @return array<Order>
     */
    public function getCancelledCollectOrders(array $orders): array
    {
        $cancelled = [];
        foreach ($orders as $order) {
            if ($order->getCanceled()) {
                $cancelled[] = $order;
            }
        }

        return $cancelled;
    }
}
