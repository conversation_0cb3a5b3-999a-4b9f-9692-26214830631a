parameters:
	ignoreErrors:


		-
			message: '#^Missing parameter \$type \(class\-string\<object\>\) in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.missing
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Parameter \$translator of class App\\Services\\GermanyHolidayResolver constructor expects Symfony\\Contracts\\Translation\\TranslatorInterface, PHPUnit\\Framework\\MockObject\\MockObject given\.$#'
			identifier: argument.type
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Unable to resolve the template type RealInstanceType in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)$#'
			identifier: argument.templateType
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php

		-
			message: '#^Unknown parameter \$originalClassName in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.unknown
			count: 1
			path: tests/Services/GermanyHolidayResolverTest.php



		-
			message: '#^Missing parameter \$type \(class\-string\<object\>\) in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.missing
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php

		-
			message: '#^Unable to resolve the template type RealInstanceType in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)$#'
			identifier: argument.templateType
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php

		-
			message: '#^Unknown parameter \$originalClassName in call to method PHPUnit\\Framework\\TestCase\:\:createMock\(\)\.$#'
			identifier: argument.unknown
			count: 1
			path: tests/Services/PublicHolidayHelperTest.php
