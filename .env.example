# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/mailer ###
MAILER_DSN=smtp://mailcatcher:1025
###< symfony/mailer ###
STDOUT_LOG_LEVEL=debug
###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# For a PostgreSQL database, use: "postgresql://db_user:db_password@127.0.0.1:5432/db_name?serverVersion=11&charset=utf8"
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
###< doctrine/doctrine-bundle ###
###> Additional Mail Configuration ###
MAIL_BASE_LINK=http://localhost:880
EMAIL_FROM=<EMAIL>

# ADMIN
EMAIL_MONOLOG=<EMAIL> # WARNUNG: ONLY ONE E-MAIL SUPPORTED!!!
EMAIL_ADMIN=admin@localhost

# APP
EMAIL_MANAGER=manger@localhost
EMAIL_ORDER=order@localhost
EMAIL_STORNO=storno@localhost
EMAIL_QUESTION=question@localhost
###< Additional Mail Configuration ###

SAP_API=moapi.toensmeier.de:10443/test/tbd/
SAP_API_USER=user-not-set
SAP_API_PASSWORD=password-not-set

API_USER=apiuser
API_PASSWORD='981.ao345!'

###> ENV-Data ###
APP_NAME=lvp-portal
DB_ROOT_PWD=rootpwd
DB_NAME=lvp-portal
DB_USER=user
DB_USER_PWD=userpwd

DATABASE_URL_DATA=mysql://${DB_USER}:${DB_USER_PWD}@db:3306/${DB_NAME}
DATABASE_URL_FILES=mysql://${DB_USER}:${DB_USER_PWD}@dbfiles:3306/${DB_NAME}
###< ENV-Data ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

# Docker
HTTP_PORT=8600
DB_PORT=33065
PHP_ADMIN_PORT=8580
MAIL_CATCHER_PORT_UI=1480
GITHUB_TOKEN=
PREZERO_NPM_TOKEN=
FIXTURES_PASS=
XDEBUG_MODE=off

# language switch
APP_LANGUAGE_SWITCH=true

CAPTCHA_SECRET=
CAPTCHA_VERIFY_URL=
CAPTCHA_SITE_KEY=

MINIO_ROOT_USER=
MINIO_ROOT_PASSWORD=
S3_ACCESS_KEY=${MINIO_ROOT_USER}
S3_SECRET_KEY=${MINIO_ROOT_PASSWORD}
S3_ENDPOINT_URL=http://s3storage:7550
S3_BUCKET_NAME=test-bucket
###> symfony/routing ###
# Configure how to generate URLs in non-HTTP contexts, such as CLI commands.
# See https://symfony.com/doc/current/routing.html#generating-urls-in-commands
DEFAULT_URI=http://localhost
###< symfony/routing ###

