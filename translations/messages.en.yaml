Mon: Mon
Tu<PERSON>: <PERSON><PERSON>: Wed
Thu: Thu
Fri: <PERSON>i
<PERSON>t: Sat
January: January
February: February
March: March
April: April
May: May
June: June
July: July
August: August
September: September
October: October
November: November
December: December
Continue: Continue
Email could not be found.: The email address could not be found.
You are logged in as: You are logged in as
Reset Password: Reset password
The two passwords are not equal. Please try again.: The two passwords do not match. Please try again.
No User for the link has been found.: No user found for the link.
Save Password: Save password
Transfer: Transfer
System deliverer: System deliverer
Disposition number: Disposition number
Driver message: Driver info
System generated: System generated
This value should not be blank.: This value should not be blank.
Display: Display
Save: Save
This e-mail address is already in use. You need to enter a different one.: This email address is already in use. You need to enter a different email address.
Okay: Okay
The password is the same as before. Please type in a new one.: The password entered is the same as the current password, please enter a new password.
Send Message: Send message
Auto generated mail - please do not reply to this mail: 'This is an auto-generated email. Please do not reply to this email.'
'Question about an Order': 'Question about an Order'
'Request to <PERSON><PERSON>': 'Request to <PERSON><PERSON>'
'General Question': 'General Question'

popup:
  cancel: Cancel
  save: Save
  delete: Delete
  lock: Lock
  unlock: Unlock
  reset: Reset

user: User
user.list: User list
user.details: User
user.details.add: Add user
user.details.edit: Edit user
user.details.delete: Delete user
user.details.lock: Lock user
user.details.unlock: Unlock user
user.details.reset: Reset password
user.field.id: Id
user.field.fullname: Name
user.field.email: Email
user.field.roles: Assigned roles
user.placeholder.fullname: Enter name
user.placeholder.email: Enter email
user.message.delete: Are you sure you want to delete this user?
user.message.lock: Are you sure you want to lock this user?
user.message.unlock: Are you sure you want to unlock this user?
user.message.reset: Are you sure you want to reset this user's password? The user will receive an email afterward.
user.myprofile: My profile

collectingplace: Collection point
collectingplace.list: Collection point list
collectingplace.details: Collection point
collectingplace.details.lock: Lock collection point
collectingplace.details.unlock: Unlock collection point
collectingplace.message.lock: Are you sure you want to lock this collection point?
collectingplace.message.unlock: Are you sure you want to unlock this collection point?

collectingplace.field:
  dsdid: DSD number
  name: Name
  esaid: ESA number

contractArea:
  field:
    name: Name
    state: State
    validFrom: Valid from
    validTo: Valid until

roles:
  ROLE_ADMIN: 'PreZero - Administrator'
  ROLE_MANAGER: 'PreZero - Manager'
  ROLE_REPORT: 'PreZero - Reporting'
  ROLE_ORDER: 'Order Creator'
  ROLE_DOCUMENTS: 'Weighing slips'
  ROLE_USER: 'User'

exception.error.text.header: 'An error has occurred!'
exception.error.text.details: 'An unexpected error has occurred'
exception.error.text.try_later: 'Please try again later'
exception.error.text.page_not_found: 'The requested page could not be found.'
exception.error.text.check_url: 'Please check the URL for errors or return to the homepage.'
contact.title: 'PreZero - Contact'
contact.header: 'Contact'
contact_form: 'Contact form'
contact.request_type: 'Request type'
contact.collecting_place: 'Collection point'
contact.contract_area: 'Contract area'
contact.disponumber: 'Disposition number'
contact.sender_email: 'Email address'
contact.company: 'Company'
contact.message: 'Message'
contact.message.send_success: 'Your message has been sent successfully.'
document.title: 'PreZero - Documents'
order.report.title: 'PreZero - Order overview'
order.report.evaluation: 'Evaluations'
order.report.unloading_point: 'Unloading point'
order.report.date_from: 'Date from'
order.report.date_to: 'Date to'
order.report.export: 'Export'
collectingplace.contract_territory_management: 'Contract territory management'
collectingplace.assign_user: 'Assign user'
collectingplace.contract_area: 'Contract area'
collectingplace.collecting_place: 'Collection point collecting place'
collectingplace.authorization: 'Authorize users for the collection point <b>%dsdId% - %name1% (%city%)</b> in the contract area <b>%name%</b>.'
collectingplace.collecting_places: 'Collecting places'
collectingplace.contracts: 'Contracts'
collectingplace.quotas: 'Quotas'
contact.prezero: 'Contact PreZero'
order.report: 'Order Overview'
collectingplace.quotas.overview_text: 'Overview of all stored quotas in the contract area.'
collectingplace.collecting_places.overview_text: 'Overview of all stored collecting places in the contract area.'
collectingplace.contracts.overview_text: 'Overview of all contracts in the contract area.'
collectingplace.contract_area_overview: 'Contract Area Overview'
collectingplace.user_select_text: 'Select a user to assign them to the selected contract area.'
collectingplace.collecting_places_contract_area_text: 'All collecting places for the contract area <b>%name%</b> are displayed here.'
index.login_header: 'PreZero - Login'
index.login: 'Login'
index.login_text: 'Log in with your credentials:'
index.login_email: 'Email:'
index.login_password: 'Password:'
index.login_password_reset: 'Forgot password?'
index.login_submit: 'Sign in'
index.support: 'Support'
index.to_the_contact_form: 'To the contact form'
ordercalender.ordercount: '%count% of %countMax%'
ordercalender.disponumber: 'Dispatch no.:'
holiday.header: 'Holiday'
go_to_overview: 'Back to Overview'
go_to_homepage: 'Go to homepage'
go_to_login_page: 'Go to login page'
select.time_period: 'Please select a time period:'
calendar.week: 'Calendar Week'
calendar.week_abbreviation: 'CW'
to_the_portal: 'To the Portal'
account: 'Account'
logged_in_as: 'Logged in as:'
old_management: 'Old Management'
contract_area_management: 'Contract Area Management'
collecting_places_management: 'Collecting places Management'
user_management: 'User Management'
reporting: 'Reporting'
logout: 'Logout'
save: 'Save'
delete: 'Delete'
transfer: 'Apply'
contact: 'Contact'
cancel_order: 'Cancel'
cancel: 'Cancel'
ok: 'OK'
back_to_selection: 'Back to Selection'
canceled: 'Canceled'
requested: 'Requested'
created: 'Created'
please_select: 'Please select'
return: 'Back'
privacy_policy: 'Privacy Policy'
compliance: 'Compliance'
legal_notice: 'Legal Notice'
profile: 'Profile'
weighing_slip: 'Weighing Slips'
weighing_slip_number: 'Weighing Slips number'
commerce_and_industry: 'Commerce & Industry'
private_households: 'Private Households'
configure_disposal: 'Configure Disposal'
date: 'Date'
as_of: 'as of'
net_weight: 'Net weight'
unit: 'Unit'
actions: 'Actions'
count_collecting_place: 'Count collecting place'
count_user: 'Count user'
count_contract_area: 'Count contract area'
name: 'Name'
street: 'Street'
house_number: 'House number'
postal_code: 'Postal code'
city: 'Stadt'
district: 'Ortsteil'
email: 'Email address'
roles_title: 'Roles'
sap_contractNumber: 'SAP contract number'
amount_week: 'Amount week'
amount_day: 'Amount day'
order.index_header: 'PreZero - Index'
order.register_header: 'Register Order'
order.collecting_place.select_question: 'For which collecting place would you like to request a pickup?'
order.collecting_place.select_text: 'Please select your collecting place:'
order.contract_area.select_text: 'Please select your contract area:'
order.pickup.time_period_question: 'When would you like the pickup to take place?'
order.collecting_place.infos: 'Collecting place Information:'
order.collecting_place.no_infos_text: 'No collecting place information available!'
order.is_canceled: 'This request has been canceled.'
order.driver_message: 'Driver Info (max. 50 characters)'
order.edit_is_not_possible: 'Editing is no longer possible.<br> Please use the contact form.'
order.cancel_info: 'Your cancellation was successfully submitted. For further concerns or questions, please use the contact form.'
order.process_cancellation: 'Execute cancellation?'
order.request_submit: 'Submit request?'
order.thank_for_request: 'Thank you for your request!'
order.error_occurred: 'Unfortunately, an error occurred while processing your request!'
order.created: 'Order created'
order.transferred: 'Order submitted'
order.cancel: 'Order canceled'
order.request_pickup: 'Request Pickup'
order.date: 'Order Date'
order.status: 'Status'

order.cancel_popup: 'Would you like to cancel your <br>
                order <b><span id="popupCancelDispoNum"></span></b> (<span id="popupCancelSystemproviderName"></span>)<br>
                <br>
                scheduled for <b><span id="popupCancelDay"></span></b>, <b><span id="popupCancelDate"></span></b><br>
                <br>
                for the collecting place<br>
                <b>%dsdid% - %name1% (%city%)</b><br>
                <br>
                in the contract area<br>
                <b>%areaName%</b><br/><br>
                <b><span id="popupCancelChargeable"></span></b> cancel?<br>'

order.submit_popup: 'Would you like to request pickup for your quantities starting<br>
                <b>%date%</b><br>
                <br>
                in<br>
                <b>Week %calendarWeek%</b><br>
                <br>
                for the collecting place<br>
                <b>%collectingPlace%</b><br>
                <br>
                in the contract area<br>
                <b>%areaName%</b><br/><br>
                submit request?'

order.thanks_popup: 'Your request starting<br>
                <b>%date%</b><br>
                <br>
                in<br>
                <b>Week %calendarWeek%</b><br>
                <br>
                for the collecting place<br>
                <b>%collectingPlace%</b><br>
                <br>
                in the contract area<br>
                <b>%areaName%</b><br/><br>
                is being processed.'

order.success_cancel: 'Your <b><span id="popupCancelSuccessChargeable"></span></b> cancellation<br>
                <br>
                for <b><span id="popupSuccessCancelDay"></span></b>, <b><span id="popupSuccessCancelDate"></span></b><br>
                <br>
                for the collecting place<br>
                <b>%collectingPlace%</b><br>
                <br>
                in the contract area<br>
                <b>%areaName%</b><br/><br>
                has been successfully submitted.'

order.error_cancel: 'Please check your request for<br>
                <b><span id="popupErrorCancelDay"></span></b>, <b><span id="popupErrorCancelDate"></span></b><br>
                and if necessary, try to cancel again.<br>
                If the issue persists, please use the contact form.'

order.error: 'Please check your request for<br>
                <b>Week %calendarWeek%</b><br>
                and if necessary, submit the pickup request again.'


management.collecting_places_management: 'Collecting place Management'
management.collecting_places: 'Collecting places'
management.collecting_places_overview: 'Overview of collecting places'
management.reporting_by: 'Reporting by'
management.reporting: 'Reporting'
management.contract_areas: 'Contract Areas'
management.user_overview: 'User Overview'
management.user_create: 'Create User'

security.new_password_header: 'PreZero - New Password'
security.new_password_input_text: 'Enter your new password.'
security.new_password_lable: 'New password (Min. 8 characters, one number, one uppercase and one lowercase letter, and one of these special characters: "@$.+_!#%&"):'
security.new_password_second_input: 'New password, 2nd entry:'
security.password_saved_header: 'Password saved'
security.password_saved_text: 'Your new password has been saved.'
security.reset_password_header: 'PreZero Quantity Report - Reset Password'
security.enter_your_email: 'Please enter your email address<br>you used to register.'
security.link_sent: 'Link Sent!'
security.link_sent_text: 'If the provided email address is known to us, a link to <br /> reset your password has been sent to that email address!'

user.profile_header: 'PreZero - My Profile'
user.profile_title: 'User Profile'
user.change_password: 'Change Password'
user.change_password_text: 'Click "Okay" if you would like to receive an email with a link to set a new password.'
user.change_email: 'Change Email'
user.change_email_text: 'Enter your new email address and current password.'
user.new_email: 'New Email Address'
user.new_email_saved_header: 'Email Address saved'
user.new_email_saved_text: 'The new email address has been saved.'
user.current_password: 'Current Password'
user.master_data: 'Master Data'
user.company: 'Company'
user.street_and_house_number: 'Street / No.'
user.postal_code_and_city: 'Postal Code / City'
user.reset_password_link_sent: 'Password Reset Link Sent!'
user.reset_password_link_sent_text: 'The link has been sent to %email%.'

email.contact_details: 'Contact Details'
email.set_password_header: 'PreZero Quantity Report - New Access'
email.reset_password_header: 'PreZero Quantity Report - Reset Password'
email.greeting: 'Hello,'
email.set_password_link_text: 'You can set your password for your new user profile using the following link:'
email.reset_password_link_text: 'You can reset your password using the following link:'
email.warning_text: 'If you did not request a new password, please contact us:'
email.thanks_for_request: 'Thank you for your request.'
email.cancel_confirm_text: 'We hereby confirm the cancellation of the following item:'
system_provider: 'System Provider'
email.order_cancel_time_limit_text: 'Since the pickup date is scheduled within 48 hours or less, we unfortunately have to <b>charge</b> you for the cancellation.'
email.order_cancel_contact_form_text: 'If you have any questions or changes, please contact us via the'
email.order_cancel_email_text: 'or alternatively via the following email address'
email.time_period: 'Time Period'
email.order_positions_confirm_text: 'We hereby confirm receipt of the following items:'
email.order_position_confirm_text: 'We hereby confirm receipt of the following item:'
locale.en: 'English'
locale.de: 'German'
locale: 'Language'
user.change_locale: 'Change Language'
contract_area_validity: 'Contract area validity'
contract_areas_validity: 'Contract areas validity'
quantity_per_day: 'Quantity per day'
quantity_per_week: 'Quantity per week'
total_per_week: 'Total per week'
from: 'from'
contract: 'Contract'
contracts: 'Contracts'
to_management: 'To management'
to_reporting: 'To reporting'
prezero_management: 'PreZero management'
management: 'Management'
overview: 'Overview'
data: 'Data'
order: 'Order'
orders: 'Orders'
interface: 'Interface'
system_data: 'System data'
states: 'States'
state: 'State'
customer_view: 'Customer view'
to_customer_view: 'To customer view'
transfer_date: 'Transfer date'
transfer_status: 'Transfer status'
overdraft_info: 'Overdraft info'
abbreviation: 'Abbreviation'
system_provider_id: 'System Provider id'
unloading_point : 'Unloading point'
unloading_points : 'Unloading points'
users: 'users'
reset_password: 'Reset password'
customer: 'Customer'
manager: 'Manager'
dashboard.overview_text: 'This page provides a brief overview of the administration area.<br> At the top left, the link "<i class="fa fa-arrow-circle-left"></i>&nbsp;To reporting" leads to the overview of all orders related to the unloading points.'
dashboard.data_text: 'In the <u>Data</u> section, data can/must be edited.'
dashboard.customer_text: 'The customer object acts as a container in the portal, collecting place one or more sites and users.'
dashboard.collecting_place_text: 'The listed collecting places are transferred from SAP, so changes to the master data are not possible.<br> Here, sites can be assigned to customers.'
dashboard.user_text: 'Users can be created here—both administrative users (PreZero) and users for the customers.<br> Additionally, users can be assigned to customers at this point. A role must be specified when creating a user:'
dashboard.user_roles_text: 'The <u>Customer</u> role must be assigned to a customer user. The user also needs to be linked to a customer; otherwise, they will receive an error or see no data.<br> The <u>Manager</u> role must be assigned to a user from PreZero/Schwarz Group. This role grants access to the admin area and the management view. The <u>Manager</u> role can only be assigned to @prezero.com and @mail.schwarz email addresses. Any other email address will cause an error.'
dashboard.user_reset_password_text: 'When a new user is created or "Reset Password" is selected, a password reset link is automatically sent to the user’s email address upon creation/saving. It is therefore <u>not</u> possible to manually set a password in the admin area.<br> Every logged-in user can later change their password under "My Profile."'
dashboard.order_text: 'In the <u>Orders</u> section, the customers orders are displayed.'
dashboard.order_status_text: 'All orders have a status (Transferred, Error, No Status). Orders may show an "Error" status if, for example, the SAP system was unreachable. Additionally, for orders that weren’t transferred, the unloading point and order date can be modified. Orders that failed to transfer will be re-sent to SAP upon saving.'
dashboard.interface_text: 'In the <u>Interface</u> section, the data transferred from SAP into the portal is displayed. Data cannot be changed here, as the SAP system is the leading source. For some objects (e.g., contracts), you can open a detail view to see more information.'
dashboard.contract_areas_text: 'All contract areas ever transferred are displayed here.'
dashboard.contracts_text: 'All contracts transferred to the contract areas can be viewed here. This section also contains information on loading and unloading points. Additionally, it shows how many and which system providers are currently active for the contract.'
dashboard.unloading_points_text: 'All unloading points ever transferred are displayed here.'
dashboard.system_provider_text: 'All system providers ever transferred are displayed here.'
dashboard.system_data_text: 'The <u>System Data</u> section shows data that must be permanently present in the system.'
dashboard.states_text: 'All federal states with their official abbreviations are displayed here. These are used to calculate public holidays.'
dashboard.customer_view_text: 'Via "To Customer View", you can log in as a customer, provided you’re assigned to one. This function should only be used for testing and debugging.'
