<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220330133041 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document_data ADD created_at DATETIME NOT NULL, ADD created_by INT NOT NULL, ADD modified_at DATETIME DEFAULT NULL, ADD modified_by INT DEFAULT NULL, CHANGE uuid uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:uuid)\'');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A477FF0D17F50A6 ON document_data (uuid)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX UNIQ_A477FF0D17F50A6 ON document_data');
        $this->addSql('ALTER TABLE document_data DROP created_at, DROP created_by, DROP modified_at, DROP modified_by, CHANGE uuid uuid VARCHAR(255) NOT NULL');
    }
}
