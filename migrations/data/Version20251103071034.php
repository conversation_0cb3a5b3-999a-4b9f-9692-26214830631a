<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251103071034 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE collect_order ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE collect_order ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN collect_order.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN collect_order.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN collect_order.modified_by IS \'\'');
        $this->addSql('ALTER TABLE collecting_place ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE collecting_place ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN collecting_place.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN collecting_place.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN collecting_place.modified_by IS \'\'');
        $this->addSql('ALTER TABLE contract ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE contract ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN contract.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN contract.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN contract.modified_by IS \'\'');
        $this->addSql('ALTER TABLE contract_area ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE contract_area ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN contract_area.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN contract_area.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN contract_area.modified_by IS \'\'');
        $this->addSql('ALTER TABLE contract_area_validity ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE contract_area_validity ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.modified_by IS \'\'');
        $this->addSql('ALTER TABLE document ADD document_data_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE document ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE document ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('ALTER TABLE document ALTER amount TYPE double precision USING (trim(amount)::double precision)');
        $this->addSql('COMMENT ON COLUMN document.additionals IS \'\'');
        $this->addSql('COMMENT ON COLUMN document.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN document.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN document.modified_by IS \'\'');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76939A07E0 FOREIGN KEY (document_data_id) REFERENCES document_data (id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_D8698A76939A07E0 ON document (document_data_id)');
        $this->addSql('ALTER TABLE document_data ADD s3_file_path VARCHAR(1024) DEFAULT NULL');
        $this->addSql('ALTER TABLE document_data DROP file');
        $this->addSql('ALTER TABLE document_data ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE document_data ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN document_data.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN document_data.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN document_data.modified_by IS \'\'');
        $this->addSql('ALTER TABLE document_type ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE document_type ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN document_type.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN document_type.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN document_type.modified_by IS \'\'');
        $this->addSql('ALTER TABLE feature ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE feature ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('ALTER TABLE state ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE state ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN state.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN state.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN state.modified_by IS \'\'');
        $this->addSql('ALTER TABLE system_provider ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE system_provider ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN system_provider.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN system_provider.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN system_provider.modified_by IS \'\'');
        $this->addSql('ALTER TABLE unloading_point ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE unloading_point ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN unloading_point.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN unloading_point.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN unloading_point.modified_by IS \'\'');
        $this->addSql('ALTER TABLE unloading_point_validity ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE unloading_point_validity ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.modified_by IS \'\'');
        $this->addSql('ALTER TABLE "user" ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE "user" ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN "user".roles IS \'\'');
        $this->addSql('COMMENT ON COLUMN "user".uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN "user".created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN "user".modified_by IS \'\'');
        $this->addSql('ALTER TABLE user_access ALTER id DROP DEFAULT');
        $this->addSql('ALTER TABLE user_access ALTER id ADD GENERATED BY DEFAULT AS IDENTITY');
        $this->addSql('COMMENT ON COLUMN user_access.uuid IS \'\'');
        $this->addSql('COMMENT ON COLUMN user_access.created_by IS \'\'');
        $this->addSql('COMMENT ON COLUMN user_access.modified_by IS \'\'');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE collect_order ALTER id SET DEFAULT nextval(\'collect_order_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE collect_order ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN collect_order.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collect_order.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collect_order.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE collecting_place ALTER id SET DEFAULT nextval(\'collecting_place_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE collecting_place ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN collecting_place.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collecting_place.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collecting_place.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE contract ALTER id SET DEFAULT nextval(\'contract_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE contract ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN contract.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE contract_area ALTER id SET DEFAULT nextval(\'contract_area_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE contract_area ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN contract_area.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE contract_area_validity ALTER id SET DEFAULT nextval(\'contract_area_validity_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE contract_area_validity ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE document DROP CONSTRAINT FK_D8698A76939A07E0');
        $this->addSql('DROP INDEX UNIQ_D8698A76939A07E0');
        $this->addSql('ALTER TABLE document DROP document_data_id');
        $this->addSql('ALTER TABLE document ALTER amount TYPE VARCHAR(255)');
        $this->addSql('ALTER TABLE document ALTER id SET DEFAULT nextval(\'document_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE document ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN document.additionals IS \'(DC2Type:json)\'');
        $this->addSql('COMMENT ON COLUMN document.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE document_data ADD file BYTEA DEFAULT NULL');
        $this->addSql('ALTER TABLE document_data DROP s3_file_path');
        $this->addSql('ALTER TABLE document_data ALTER id SET DEFAULT nextval(\'document_data_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE document_data ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN document_data.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_data.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_data.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE document_type ALTER id SET DEFAULT nextval(\'document_type_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE document_type ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN document_type.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_type.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_type.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE feature ALTER id SET DEFAULT nextval(\'feature_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE feature ALTER id DROP IDENTITY');
        $this->addSql('ALTER TABLE state ALTER id SET DEFAULT nextval(\'state_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE state ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN state.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN state.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN state.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE system_provider ALTER id SET DEFAULT nextval(\'system_provider_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE system_provider ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN system_provider.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN system_provider.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN system_provider.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE unloading_point ALTER id SET DEFAULT nextval(\'unloading_point_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE unloading_point ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN unloading_point.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE unloading_point_validity ALTER id SET DEFAULT nextval(\'unloading_point_validity_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE unloading_point_validity ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE "user" ALTER id SET DEFAULT nextval(\'user_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE "user" ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN "user".roles IS \'(DC2Type:json)\'');
        $this->addSql('COMMENT ON COLUMN "user".uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN "user".created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN "user".modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE user_access ALTER id SET DEFAULT nextval(\'user_access_id_seq\'::regclass)');
        $this->addSql('ALTER TABLE user_access ALTER id DROP IDENTITY');
        $this->addSql('COMMENT ON COLUMN user_access.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN user_access.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN user_access.modified_by IS \'(DC2Type:uuid)\'');
    }
}
