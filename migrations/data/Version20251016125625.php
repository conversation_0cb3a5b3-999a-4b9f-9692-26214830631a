<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20251016125625 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE collect_order (id SERIAL NOT NULL, system_provider INT DEFAULT NULL, collecting_place INT NOT NULL, unloading_point INT NOT NULL, contract_area_id INT NOT NULL, contract_id INT NOT NULL, order_id VARCHAR(255) NOT NULL, date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, transfered TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, canceled TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, transfer_status VARCHAR(1) DEFAULT NULL, driver_message VARCHAR(255) DEFAULT NULL, dispo_message VARCHAR(255) DEFAULT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_E9FAAA46D17F50A6 ON collect_order (uuid)');
        $this->addSql('CREATE INDEX IDX_E9FAAA46FB2EC21E ON collect_order (system_provider)');
        $this->addSql('CREATE INDEX IDX_E9FAAA466AD7AED3 ON collect_order (collecting_place)');
        $this->addSql('CREATE INDEX IDX_E9FAAA46D5788941 ON collect_order (unloading_point)');
        $this->addSql('CREATE INDEX IDX_E9FAAA46B55E3D54 ON collect_order (contract_area_id)');
        $this->addSql('CREATE INDEX IDX_E9FAAA462576E0FD ON collect_order (contract_id)');
        $this->addSql('COMMENT ON COLUMN collect_order.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collect_order.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collect_order.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE collecting_place (id SERIAL NOT NULL, state INT NOT NULL, collecting_place_id VARCHAR(255) NOT NULL, dsd_id VARCHAR(255) NOT NULL, name_1 VARCHAR(255) NOT NULL, name_2 VARCHAR(255) DEFAULT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) DEFAULT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(60) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, locked BOOLEAN DEFAULT false NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6AD7AED3D17F50A6 ON collecting_place (uuid)');
        $this->addSql('CREATE INDEX IDX_6AD7AED3A393D2FB ON collecting_place (state)');
        $this->addSql('COMMENT ON COLUMN collecting_place.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collecting_place.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN collecting_place.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE contract (id SERIAL NOT NULL, collecting_place_id INT DEFAULT NULL, unloading_point_id INT DEFAULT NULL, contract_area_id INT DEFAULT NULL, contract_id VARCHAR(255) NOT NULL, contract_number VARCHAR(10) NOT NULL, valid_from TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, valid_to TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_E98F2859D17F50A6 ON contract (uuid)');
        $this->addSql('CREATE INDEX IDX_E98F2859C1BD9823 ON contract (collecting_place_id)');
        $this->addSql('CREATE INDEX IDX_E98F2859A386F81E ON contract (unloading_point_id)');
        $this->addSql('CREATE INDEX IDX_E98F2859B55E3D54 ON contract (contract_area_id)');
        $this->addSql('COMMENT ON COLUMN contract.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE contract_system_provider (contract_id INT NOT NULL, system_provider_id INT NOT NULL, PRIMARY KEY(contract_id, system_provider_id))');
        $this->addSql('CREATE INDEX IDX_2870BFE42576E0FD ON contract_system_provider (contract_id)');
        $this->addSql('CREATE INDEX IDX_2870BFE455E3F3B2 ON contract_system_provider (system_provider_id)');
        $this->addSql('CREATE TABLE contract_area (id SERIAL NOT NULL, state INT NOT NULL, contract_area_id VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, valid_from TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, valid_to TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_BFE17300D17F50A6 ON contract_area (uuid)');
        $this->addSql('CREATE INDEX IDX_BFE17300A393D2FB ON contract_area (state)');
        $this->addSql('COMMENT ON COLUMN contract_area.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE contract_area_validity (id SERIAL NOT NULL, contract_area INT DEFAULT NULL, validity_id VARCHAR(255) NOT NULL, amount_day INT NOT NULL, amount_week INT NOT NULL, valid_from TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, valid_to TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1A54E0B3D17F50A6 ON contract_area_validity (uuid)');
        $this->addSql('CREATE INDEX IDX_1A54E0B3BFE17300 ON contract_area_validity (contract_area)');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN contract_area_validity.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE document (id SERIAL NOT NULL, document_type_id INT NOT NULL, contract_id INT DEFAULT NULL, number VARCHAR(255) NOT NULL, date TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, amount VARCHAR(255) NOT NULL, unit VARCHAR(255) NOT NULL, visible BOOLEAN NOT NULL, active BOOLEAN NOT NULL, additionals JSON DEFAULT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_D8698A76D17F50A6 ON document (uuid)');
        $this->addSql('CREATE INDEX IDX_D8698A7661232A4F ON document (document_type_id)');
        $this->addSql('CREATE INDEX IDX_D8698A762576E0FD ON document (contract_id)');
        $this->addSql('COMMENT ON COLUMN document.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE document_data (id SERIAL NOT NULL, file BYTEA DEFAULT NULL, mime_type VARCHAR(63) NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A477FF0D17F50A6 ON document_data (uuid)');
        $this->addSql('COMMENT ON COLUMN document_data.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_data.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_data.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE document_type (id SERIAL NOT NULL, name VARCHAR(255) NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_2B6ADBBAD17F50A6 ON document_type (uuid)');
        $this->addSql('COMMENT ON COLUMN document_type.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_type.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN document_type.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE feature (id SERIAL NOT NULL, name VARCHAR(50) NOT NULL, active BOOLEAN NOT NULL, created TIMESTAMP(0) WITH TIME ZONE NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE state (id SERIAL NOT NULL, name VARCHAR(255) NOT NULL, short_name VARCHAR(255) NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_A393D2FBD17F50A6 ON state (uuid)');
        $this->addSql('COMMENT ON COLUMN state.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN state.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN state.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE system_provider (id SERIAL NOT NULL, material_id VARCHAR(40) NOT NULL, name VARCHAR(255) NOT NULL, system_provider_id VARCHAR(30) NOT NULL, long_text VARCHAR(255) DEFAULT NULL, dsd_fraction_id VARCHAR(30) NOT NULL, dsd_fraction_name VARCHAR(30) NOT NULL, transport BOOLEAN NOT NULL, mono_fraction BOOLEAN NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_FB2EC21ED17F50A6 ON system_provider (uuid)');
        $this->addSql('COMMENT ON COLUMN system_provider.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN system_provider.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN system_provider.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE unloading_point (id SERIAL NOT NULL, state INT NOT NULL, unloading_point_id VARCHAR(255) NOT NULL, dsd_id VARCHAR(255) NOT NULL, name_1 VARCHAR(255) NOT NULL, name_2 VARCHAR(255) DEFAULT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, street VARCHAR(60) NOT NULL, house_number VARCHAR(10) DEFAULT NULL, postal_code VARCHAR(10) NOT NULL, city VARCHAR(60) NOT NULL, district VARCHAR(40) DEFAULT NULL, country VARCHAR(3) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_D5788941D17F50A6 ON unloading_point (uuid)');
        $this->addSql('CREATE INDEX IDX_D5788941A393D2FB ON unloading_point (state)');
        $this->addSql('COMMENT ON COLUMN unloading_point.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE unloading_point_validity (id SERIAL NOT NULL, validity_id VARCHAR(255) NOT NULL, amount_day INT NOT NULL, amount_week INT NOT NULL, valid_from TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, valid_to TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, unloadingPoint INT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_6E29E0A6D17F50A6 ON unloading_point_validity (uuid)');
        $this->addSql('CREATE INDEX IDX_6E29E0A62B261667 ON unloading_point_validity (unloadingPoint)');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN unloading_point_validity.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE "user" (id SERIAL NOT NULL, email VARCHAR(180) NOT NULL, roles JSON NOT NULL, password VARCHAR(255) NOT NULL, password_date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, reset_password BOOLEAN DEFAULT NULL, password_reset_hash VARCHAR(255) DEFAULT NULL, locale VARCHAR(10) NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, locked BOOLEAN DEFAULT false NOT NULL, deleted BOOLEAN DEFAULT false NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8D93D649E7927C74 ON "user" (email)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_8D93D649D17F50A6 ON "user" (uuid)');
        $this->addSql('COMMENT ON COLUMN "user".uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN "user".created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN "user".modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('CREATE TABLE user_access (id SERIAL NOT NULL, user_id INT NOT NULL, contract_area_id INT NOT NULL, collecting_place_id INT NOT NULL, uuid UUID NOT NULL, created_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, created_by UUID NOT NULL, modified_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, modified_by UUID DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_633B3069D17F50A6 ON user_access (uuid)');
        $this->addSql('CREATE INDEX IDX_633B3069A76ED395 ON user_access (user_id)');
        $this->addSql('CREATE INDEX IDX_633B3069B55E3D54 ON user_access (contract_area_id)');
        $this->addSql('CREATE INDEX IDX_633B3069C1BD9823 ON user_access (collecting_place_id)');
        $this->addSql('COMMENT ON COLUMN user_access.uuid IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN user_access.created_by IS \'(DC2Type:uuid)\'');
        $this->addSql('COMMENT ON COLUMN user_access.modified_by IS \'(DC2Type:uuid)\'');
        $this->addSql('ALTER TABLE collect_order ADD CONSTRAINT FK_E9FAAA46FB2EC21E FOREIGN KEY (system_provider) REFERENCES system_provider (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE collect_order ADD CONSTRAINT FK_E9FAAA466AD7AED3 FOREIGN KEY (collecting_place) REFERENCES collecting_place (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE collect_order ADD CONSTRAINT FK_E9FAAA46D5788941 FOREIGN KEY (unloading_point) REFERENCES unloading_point (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE collect_order ADD CONSTRAINT FK_E9FAAA46B55E3D54 FOREIGN KEY (contract_area_id) REFERENCES contract_area (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE collect_order ADD CONSTRAINT FK_E9FAAA462576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE collecting_place ADD CONSTRAINT FK_6AD7AED3A393D2FB FOREIGN KEY (state) REFERENCES state (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F2859C1BD9823 FOREIGN KEY (collecting_place_id) REFERENCES collecting_place (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F2859A386F81E FOREIGN KEY (unloading_point_id) REFERENCES unloading_point (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract ADD CONSTRAINT FK_E98F2859B55E3D54 FOREIGN KEY (contract_area_id) REFERENCES contract_area (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract_system_provider ADD CONSTRAINT FK_2870BFE42576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract_system_provider ADD CONSTRAINT FK_2870BFE455E3F3B2 FOREIGN KEY (system_provider_id) REFERENCES system_provider (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract_area ADD CONSTRAINT FK_BFE17300A393D2FB FOREIGN KEY (state) REFERENCES state (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE contract_area_validity ADD CONSTRAINT FK_1A54E0B3BFE17300 FOREIGN KEY (contract_area) REFERENCES contract_area (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A7661232A4F FOREIGN KEY (document_type_id) REFERENCES document_type (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A762576E0FD FOREIGN KEY (contract_id) REFERENCES contract (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE unloading_point ADD CONSTRAINT FK_D5788941A393D2FB FOREIGN KEY (state) REFERENCES state (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE unloading_point_validity ADD CONSTRAINT FK_6E29E0A62B261667 FOREIGN KEY (unloadingPoint) REFERENCES unloading_point (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_access ADD CONSTRAINT FK_633B3069A76ED395 FOREIGN KEY (user_id) REFERENCES "user" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_access ADD CONSTRAINT FK_633B3069B55E3D54 FOREIGN KEY (contract_area_id) REFERENCES contract_area (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE user_access ADD CONSTRAINT FK_633B3069C1BD9823 FOREIGN KEY (collecting_place_id) REFERENCES collecting_place (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');

        $this->addSql(<<<'SQL'
            DROP TABLE feature
        SQL);

        $this->addSql('ALTER TABLE collect_order DROP CONSTRAINT FK_E9FAAA46FB2EC21E');
        $this->addSql('ALTER TABLE collect_order DROP CONSTRAINT FK_E9FAAA466AD7AED3');
        $this->addSql('ALTER TABLE collect_order DROP CONSTRAINT FK_E9FAAA46D5788941');
        $this->addSql('ALTER TABLE collect_order DROP CONSTRAINT FK_E9FAAA46B55E3D54');
        $this->addSql('ALTER TABLE collect_order DROP CONSTRAINT FK_E9FAAA462576E0FD');
        $this->addSql('ALTER TABLE collecting_place DROP CONSTRAINT FK_6AD7AED3A393D2FB');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F2859C1BD9823');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F2859A386F81E');
        $this->addSql('ALTER TABLE contract DROP CONSTRAINT FK_E98F2859B55E3D54');
        $this->addSql('ALTER TABLE contract_system_provider DROP CONSTRAINT FK_2870BFE42576E0FD');
        $this->addSql('ALTER TABLE contract_system_provider DROP CONSTRAINT FK_2870BFE455E3F3B2');
        $this->addSql('ALTER TABLE contract_area DROP CONSTRAINT FK_BFE17300A393D2FB');
        $this->addSql('ALTER TABLE contract_area_validity DROP CONSTRAINT FK_1A54E0B3BFE17300');
        $this->addSql('ALTER TABLE document DROP CONSTRAINT FK_D8698A7661232A4F');
        $this->addSql('ALTER TABLE document DROP CONSTRAINT FK_D8698A762576E0FD');
        $this->addSql('ALTER TABLE unloading_point DROP CONSTRAINT FK_D5788941A393D2FB');
        $this->addSql('ALTER TABLE unloading_point_validity DROP CONSTRAINT FK_6E29E0A62B261667');
        $this->addSql('ALTER TABLE user_access DROP CONSTRAINT FK_633B3069A76ED395');
        $this->addSql('ALTER TABLE user_access DROP CONSTRAINT FK_633B3069B55E3D54');
        $this->addSql('ALTER TABLE user_access DROP CONSTRAINT FK_633B3069C1BD9823');
        $this->addSql('DROP TABLE collect_order');
        $this->addSql('DROP TABLE collecting_place');
        $this->addSql('DROP TABLE contract');
        $this->addSql('DROP TABLE contract_system_provider');
        $this->addSql('DROP TABLE contract_area');
        $this->addSql('DROP TABLE contract_area_validity');
        $this->addSql('DROP TABLE document');
        $this->addSql('DROP TABLE document_data');
        $this->addSql('DROP TABLE document_type');
        $this->addSql('DROP TABLE feature');
        $this->addSql('DROP TABLE state');
        $this->addSql('DROP TABLE system_provider');
        $this->addSql('DROP TABLE unloading_point');
        $this->addSql('DROP TABLE unloading_point_validity');
        $this->addSql('DROP TABLE "user"');
        $this->addSql('DROP TABLE user_access');
    }
}
