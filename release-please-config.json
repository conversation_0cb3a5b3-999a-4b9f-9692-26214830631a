{"packages": {".": {"changelog-path": "CHANGELOG.md", "release-type": "simple", "bump-minor-pre-major": false, "bump-patch-for-minor-pre-major": false, "draft": false, "prerelease": false, "changelog-sections": [{"type": "feat", "section": "Features", "hidden": false}, {"type": "fix", "section": "Bug Fixes", "hidden": false}, {"type": "perf", "section": "Performance Improvements", "hidden": false}, {"type": "revert", "section": "Reverts", "hidden": false}, {"type": "chore", "section": "Miscellaneous Chores", "hidden": false}, {"type": "docs", "section": "Documentation", "hidden": false}, {"type": "style", "section": "Styles", "hidden": false}, {"type": "refactor", "section": "Code Refactoring", "hidden": false}, {"type": "test", "section": "Tests", "hidden": false}, {"type": "build", "section": "Build System", "hidden": false}, {"type": "ci", "section": "Continuous Integration", "hidden": false}]}}, "$schema": "https://raw.githubusercontent.com/googleapis/release-please/main/schemas/config.json"}