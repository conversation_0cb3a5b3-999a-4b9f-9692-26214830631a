set -o allexport # export all defined variables

source .env # load dev parameters
source .env.test # override with test parameters (only those needed)

php bin/console cache:clear -e "test" --no-debug
php bin/console doctrine:database:drop -e "test" --force --no-debug --connection=default
php bin/console doctrine:database:create -e "test" --no-debug --connection=default
php bin/console doctrine:migration:migrate -e "test" -n --no-debug --em=default
php bin/console doctrine:schema:update --force --em=default

php bin/console doctrine:fixtures:load -e "test" -n  --em=default

# ... and more

php bin/phpunit | tee tests.txt