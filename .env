# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/best_practices.html#use-environment-variables-for-infrastructure-configuration

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=3a841577cc9e5fc90fc95c3f64560b34
STDOUT_LOG_LEVEL=debug
#TRUSTED_PROXIES=*********/8,10.0.0.0/8,**********/12,***********/16
#TRUSTED_HOSTS='^(localhost|example\.com)$'
###< symfony/framework-bundle ###

###> symfony/mailer ###
MAILER_DSN=smtp://mailcatcher:1025
###< symfony/mailer ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# For an SQLite database, use: "sqlite:///%kernel.project_dir%/var/data.db"
# For a PostgreSQL database, use: "postgresql://db_user:db_password@127.0.0.1:5432/db_name?serverVersion=11&charset=utf8"
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
###< doctrine/doctrine-bundle ###
###> Additional Mail Configuration ###
MAIL_BASE_LINK=http://localhost:8000
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Mengenmeldung
EMAIL_REPLY_TO=<EMAIL>

# ADMIN
EMAIL_MONOLOG=<EMAIL> # WARNUNG: ONLY ONE E-MAIL SUPPORTED!!!
EMAIL_ADMIN=admin@localhost

# APP
EMAIL_MANAGER=manger@localhost
EMAIL_ORDER=order@localhost
EMAIL_STORNO=storno@localhost
EMAIL_QUESTION=question@localhost
###< Additional Mail Configuration ###

SAP_API_URL=moapi.toensmeier.de:10443/test/tbd/
SAP_API_USER=user-not-set
SAP_API_PASSWORD=password-not-set

API_USER=apiuser
API_PASSWORD='981.ao345!'

###> ENV-Data ###
APP_NAME=lvp-portal
DB_ROOT_PWD=rootpwd
DB_NAME=lvp-portal
DB_USER=user
DB_USER_PWD=userpwd

DATABASE_URL_DATA='mysql://root:rootpwd@db:3306/lvp-portal?serverVersion=10.11.14-MariaDB&charset=utf8'
DATABASE_URL_FILES='mysql://root:rootpwd@dbfiles:3307/lvp-portal?serverVersion=10.11.14-MariaDB&charset=utf8'
DATABASE_URL='pgsql://user:userpwd@postgres:5432/lvp-portal?serverVersion=17&charset=utf8'

###< ENV-Data ###

###> symfony/messenger ###
# Choose one of the transports below
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_TRANSPORT_DSN=doctrine://default?auto_setup=0
###< symfony/messenger ###

TRUSTED_PROXIES=
TRUSTED_HOSTS=

###> prezero npm token ###
PREZERO_NPM_TOKEN=<YOURTOKEN>
###< prezero npm token ###

# Docker
FIXTURES_PASS=$78Nine.

# language switch
APP_LANGUAGE_SWITCH=true
###> symfony/lock ###
# Choose one of the stores below
# postgresql+advisory://db_user:db_password@localhost/db_name
LOCK_DSN=flock
###< symfony/lock ###
CAPTCHA_SECRET=
CAPTCHA_VERIFY_URL=
CAPTCHA_SITE_KEY=
