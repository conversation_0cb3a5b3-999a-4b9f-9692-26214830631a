*,
*:before,
*:after {
  box-sizing: border-box;
}

html,
body {
  font-size: $fontSize;
  margin: 0px;
  padding: 0px;
  width: 100%;
}

body {
  font-family: $font;
  font-weight: 100;
  line-height: 1.2;
  color: $baseColor;
  background: $white;

  &.noscroll {
    overflow: hidden;
  }

  @media only screen and (max-width: 479px) {
    font-size: $fontSizeMobile;
  }
}

p {
  margin-top: 0;
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  &:empty {
    display: none;
    margin: 0;
  }

  &.lead {
    font-size: 1.222222rem;
    color: lighten($baseColor, 50%);
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-weight: 100;
  line-height: 1.25em;
}

h1 {
  font-size: 2.25rem;
}

h2 {
  font-size: 1.5rem;
}

h3 {
  font-size: 1.125rem;
}

img {
  max-width: 100%;
}

b,
strong {
  font-weight: 500;
}

nav.blackgrid {
  select {
    width: 150px;
  }
}

aside:not(.blackgrid) {
  padding: 25px 20px;
  background: $petrol;
  border-radius: 30px 0 30px 0;
  margin-bottom: 50px;
  color: $white;
  overflow: hidden;

  ul {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      padding: 8px 0;

      i {
        margin-right: 10px;
      }

      a,
      a:hover {
        font-weight: 100;
        color: $white !important;
      }

      &.active a {
        font-weight: 500;
      }
    }
  }

  address {
    border-left: 4px solid $white;
    padding: 0 0 0 10px;
  }
}

table {
  th {
    font-weight: 500;
  }

  td {
    font-weight: 100;
  }
}

a,
a:hover,
a:focus,
a:active,
a:visited,
button,
input[type="submit"] {
  color: $darkblue;
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  text-decoration: none;
  outline: 0;
  border-width: 0;
  cursor: pointer;

  &.green {
    color: $lightgreen;
    font-weight: 100;
  }

  &.grey {
    color: $darkgrey;
    font-weight: 100;
  }

  &.disabled {
    pointer-events: none;
    opacity: 0.6;
    cursor: no-drop;
  }

  &.btn-prime {
    display: inline-block;
    padding: 15px 20px;
    background: $darkblue;
    color: $white;
    margin: 0 auto;
    border-radius: 5px;
    text-align: center;
    transform: translate3d(0, 0, 0);

    &:hover:enabled {
      box-shadow: 0 10px 30px rgba(0, 80, 201, 0.2), 0 0 30px rgba(0, 80, 201, 0.22);
    }

    &:disabled {
      background: #dddddd;
      cursor: not-allowed;
    }
  }

  &.btn-secondary {
    display: inline-block;
    padding: 15px 20px;
    background: $lightgreen;
    color: $white;
    margin: 0 auto;
    border-radius: 5px;
    text-align: center;
    transform: translate3d(0, 0, 0);

    &:hover {
      background-color: $petrol;
    }
  }

  &.btn-text-only {
    padding: 0px 20px;

    &:hover {
      background-color: $lightgreen;
    }
  }

  &.btn-grey {
    display: inline-block;
    padding: 15px 20px;
    background: lighten($darkgrey, 20%);
    color: $white;
    margin: 0 auto;
    border-radius: 5px;
    text-align: center;
    transform: translate3d(0, 0, 0);

    &:hover {
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 0 30px rgba(0, 0, 0, 0.12);
    }
  }

  &.btn-red {
    display: inline-block;
    padding: 15px 20px;
    background: $error;
    color: $white;
    margin: 0 auto;
    border-radius: 5px;
    text-align: center;
    transform: translate3d(0, 0, 0);

    &:hover {
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 0 30px rgba(0, 0, 0, 0.12);
    }
  }

  &.btn-center {
    display: table;
  }

  &.btn-icon {
    font-size: 18px;
    margin: 20px 20px 0 0;

    i {
      transform: scale(1.5);
      margin-right: 20px;
    }
  }
}

hr {
  margin: 2rem 0;
  border: 1px solid $grey;
}

input,
select,
textarea {
  font-family: $font;
  font-weight: 100 !important;
}

input[type="text"],
input[type="date"],
input[type="password"],
input[type="email"],
textarea,
select {
  width: 100%;
  font-weight: 100;
  appearance: none;
  outline: 0;
  border: 1px solid $lightgrey;
  background-color: $white;
  border-radius: 10px !important;
  padding: 10px 20px;
  resize: none;
}

select {
  background-image: linear-gradient(45deg, transparent 50%, gray 50%), linear-gradient(135deg, gray 50%, transparent 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 20px) calc(1em + 2px), calc(100% - 15px) calc(1em + 2px), calc(100% - 2.5em) 0.5em;
  background-size: 5px 5px, 5px 5px, 1px 1.5em;
  background-repeat: no-repeat;
  -moz-appearance: none;
  text-indent: 0.01px;
  text-overflow: '';
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none;
  margin: 0;
}

input[type="number"] {
  appearance: textfield !important;
}

input[type="checkbox"],
input[type="radio"] {
  position: relative;
  appearance: none;
  outline: 0;
  width: 30px;
  height: 30px;
  background: $lightgrey;

  &:checked {
    background: $darkblue;

    &:after {
      background: $darkblue;
    }
  }

  &:after {
    position: absolute;
    content: "";
    width: 24px;
    height: 24px;
    border: 7px solid $white;
    background: $lightgrey;
    top: 3px;
    right: 3px;
    bottom: 3px;
    left: 3px;
  }

  &.color-petrol {
    &:checked {
      background: $lightgreen;

      &:after {
        background: $lightgreen;
      }
    }

    &:after {
      border-color: $petrol;
    }
  }
}

input[type="checkbox"]+label {
  font-size: 0.777778rem;
}

input[type="radio"] {
  border-radius: 100%;

  &:after {
    border-radius: 100%;
  }
}

/**
 * Box
**/

div.box {
  box-shadow: 0px 10px 35px rgba(0, 0, 0, 0.15);
  padding: 35px 45px;
  background: #fff;
  border-radius: 30px 0 30px 0;
  margin-bottom: 50px;
  overflow: hidden;
}

div.box-rounded {
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.15);
  padding: 35px 45px;
  background: #fff;
  border-radius: 10px;
  margin-bottom: 30px;
  overflow: hidden;
}

/**
 * Loader
**/

div.loader {
  position: fixed;
  width: 100vw;
  max-height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(20, 61, 69, 0.95);
  z-index: 9999;
  display: none;

  div.inner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    div.la-square-loader.la-2x {
      width: 55px;
      height: 50px;
      color: $lightgreen;
      margin: 0 auto 20px;

      >div {
        border-width: 4px;
        border-radius: 15px 0 15px 0;
        animation-duration: 3s;

        &:after {
          animation-duration: 3s;
        }
      }
    }

    p {
      text-align: center;
      color: $white;
    }
  }
}

/**
 * Popup
**/

div.popup {
  position: fixed;
  width: 100vw;
  max-height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(20, 61, 69, 0.95);
  z-index: 9999;
  display: none;

  div.inner {
    position: relative;
    background: $white;
    border-radius: 30px 0 30px 0;
    margin: 5vh auto;
    max-height: 90vh;
    width: 400px;
    max-width: 90%;
    overflow-y: auto;

    div.header {
      position: relative;
      font-size: 1.125rem;
      background: $lightgreen;
      padding: 20px 20px;
      color: $white;
      font-weight: 500;

      @include clearfix();

      i.close {
        position: absolute;
        top: 0px;
        right: 0px;
        cursor: pointer;
        padding: 10px;
        color: $white;
        opacity: 1;
      }

      div.info {
        float: left;

        b {
          font-size: 1.5rem;
        }
      }

      div.date {
        font-size: 2.5rem;
        float: right;
        background: $white;
        border-radius: 10px;
        color: $baseColor;
        padding: 10px 15px;
        margin: 0 10%;
        text-align: center;
        line-height: 1;

        span {
          display: block;
          font-size: 1.125rem;
        }
      }
    }

    div.content {
      padding: 30px 20px;

      @include clearfix();

      button {
        font-size: 1rem;
        padding: 10px 15px;

        i {
          margin-right: 8px;
        }
      }
    }
  }
}

div.popup-thanks, div.popup-error, div.popup-transfer, div.popup-cancelOrder, div.popup-successCancel, div.popup-errorCancel {
  position: fixed;
  width: 100vw;
  max-height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(20, 61, 69, 0.95);
  z-index: 9999;
  display: none;

  div.inner {
    position: relative;
    background: $white;
    border-radius: 30px 0 30px 0;
    margin: 5vh auto;
    max-height: 90vh;
    width: 350px;
    max-width: 90%;
    overflow-y: auto;

    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.15);
    padding: 30px;
    border-radius: 10px;

    h3 {
      color: $lightgreen;
    }
    h3.error {
      color: $error;
    }
  }
}

/**
 * Noty
 */

div.noty_layout {
  top: 96px !important;

  >div {
    border-bottom: 0 !important;
    background-color: transparent !important;
  }

  div.noty_body {
    background: $petrol;
    color: $white;
    font-weight: 500;
    border-radius: 10px 0 10px 0 !important;
    padding: 20px 20px 18px 20px !important;
    font-size: 1rem !important;
  }

  div.noty_progressbar {
    display: none;
  }
}

/**
 * Tippy
 */

.tippy-tooltip.error-theme {
  background-color: $error;
  font-weight: 500;
  color: $white;
}

.tippy-tooltip.error-theme[data-placement^="top"] .tippy-arrow {
  border-top-color: $error;
}

.tippy-tooltip.error-theme[data-placement^="bottom"] .tippy-arrow {
  border-bottom-color: $error;
}

.tippy-tooltip.error-theme[data-placement^="left"] .tippy-arrow {
  border-left-color: $error;
}

.tippy-tooltip.error-theme[data-placement^="right"] .tippy-arrow {
  border-right-color: $error;
}

.browsererror {
  padding: 15px;
  background-color: $warning;
  color: #161616;
  text-align: center;
  border-radius: 12px;
  box-shadow: 16px 0px 4px rgba(0, 0, 0, 0.1);
  font-size: 16px;

  i {
    color: #FF4D2E;
    margin-right: 10px;
    margin-left: 10px;
  }
}
