@import '@fortawesome/fontawesome-free/scss/fontawesome';
@import '@fortawesome/fontawesome-free/scss/solid';
@import '@fortawesome/fontawesome-free/scss/regular';
@import '@fortawesome/fontawesome-free/scss/brands';
@import '@fortawesome/fontawesome-free/scss/v4-shims';
@import "~noty/src/noty.scss";
@import "~noty/src/themes/mint.scss";
$fa-font-path: '~@fortawesome/fontawesome-free/webfonts';

* {
  box-sizing: border-box;
}

html {
  font-size: 18px;
}

::placeholder, input, select, textarea {
  font-weight: 100;
}

@font-face {
  font-family: Helsinki;
  src: url(../fonts/Helsinki-Thin_f1e3650a.woff) format("woff");
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: Helsinki;
  src: url(../fonts/Helsinki-Extralight_2f4c514c.woff) format("woff");
  font-weight: 200;
  font-style: normal;
}

@font-face {
  font-family: Helsinki;
  src: url(../fonts/Helsinki-Regular_a3f86407.woff) format("woff");
  font-weight: 400;
  font-style: normal;
}

.account-button:hover .account-dropdown {
    display: block;
    z-index: 9999;
}

#noty_layout__topRight.noty_layout {
  width: 450px;
  top: 5px;
  right: 10px;
}

#noty_layout__topRight.noty_layout .noty_bar {
  border-radius: 0.375rem !important;
  border-bottom: 0;
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__success {
  background: #6BA43A
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__success::before {
  position: absolute;
  content: url('data:image/svg+xml;utf8,<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="check-circle" class="svg-inline--fa fa-check-circle fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M504 256c0 136.967-111.033 248-248 248S8 392.967 8 256 119.033 8 256 8s248 111.033 248 248zM227.314 387.314l184-184c6.248-6.248 6.248-16.379 0-22.627l-22.627-22.627c-6.248-6.249-16.379-6.249-22.628 0L216 308.118l-70.059-70.059c-6.248-6.248-16.379-6.248-22.628 0l-22.627 22.627c-6.248 6.248-6.248 16.379 0 22.627l104 104c6.249 6.249 16.379 6.249 22.628.001z"></path></svg>');
  left: 20px;
  top: 18px;
  width: 22px;
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__error {
  background: #FF432E
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__error::before {
  position: absolute;
  content: url('data:image/svg+xml;utf8,<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="times-circle" class="svg-inline--fa fa-times-circle fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M256 8C119 8 8 119 8 256s111 248 248 248 248-111 248-248S393 8 256 8zm121.6 313.1c4.7 4.7 4.7 12.3 0 17L338 377.6c-4.7 4.7-12.3 4.7-17 0L256 312l-65.1 65.6c-4.7 4.7-12.3 4.7-17 0L134.4 338c-4.7-4.7-4.7-12.3 0-17l65.6-65-65.6-65.1c-4.7-4.7-4.7-12.3 0-17l39.6-39.6c4.7-4.7 12.3-4.7 17 0l65 65.7 65.1-65.6c4.7-4.7 12.3-4.7 17 0l39.6 39.6c4.7 4.7 4.7 12.3 0 17L312 256l65.6 65.1z"></path></svg>');
  left: 20px;
  top: 18px;
  width: 22px;
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__info {
  background: #5AC5F2
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__info::before {
  position: absolute;
  content: url('data:image/svg+xml;utf8,<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="info-circle" class="svg-inline--fa fa-info-circle fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"></path></svg>');
  left: 20px;
  top: 18px;
  width: 22px;
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__warning,
#noty_layout__topRight.noty_layout .noty_bar.noty_type__alert {
  background: #FF6D2E
}

#noty_layout__topRight.noty_layout .noty_bar.noty_type__warning::before,
#noty_layout__topRight.noty_layout .noty_bar.noty_type__alert::before{
  position: absolute;
  content: url('data:image/svg+xml;utf8,<svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="exclamation-circle" class="svg-inline--fa fa-exclamation-circle fa-w-16" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M504 256c0 136.997-111.043 248-248 248S8 392.997 8 256C8 119.083 119.043 8 256 8s248 111.083 248 248zm-248 50c-25.405 0-46 20.595-46 46s20.595 46 46 46 46-20.595 46-46-20.595-46-46-46zm-43.673-165.346l7.418 136c.347 6.364 5.609 11.346 11.982 11.346h48.546c6.373 0 11.635-4.982 11.982-11.346l7.418-136c.375-6.874-5.098-12.654-11.982-12.654h-63.383c-6.884 0-12.356 5.78-11.981 12.654z"></path></svg>');
  left: 20px;
  top: 18px;
  width: 22px;
}

#noty_layout__topRight.noty_layout .noty_body {
  font-size: 1.2rem;
  padding: 20px 60px;
  line-height: 1;
}

#noty_layout__topRight.noty_layout .noty_progressbar {
  display: none;
}

#noty_layout__topRight.noty_layout .noty_close_button {
  font-size: 30px;
  right: 20px;
  top: 20px;
  background: none;
}
