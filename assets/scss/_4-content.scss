main {
  position: relative;
  padding: 50px 0 150px;
  background: transparent linear-gradient(180deg, #F5F5F500 0%, #EBEBEB 100%) 0% 0% no-repeat padding-box;
  border-bottom-right-radius: 100px;
  overflow: hidden;
  min-height: calc(100vh - 350px);

  &.background-blue {
    background: radial-gradient(ellipse at center, #5ac5f2 0%, #2a545b 80%);
  }

  &.background-person:after {
    position: absolute;
    content: ' ';
    right: 0;
    bottom: 0;
    background: url(../img/person.png) no-repeat;
    background-size: contain;
    width: 300px;
    height: 300px;
  }

  @media screen and (max-width: 767px) {
    border-bottom-right-radius: 0;

    &.background-person:after {
      content: none;
    }
  }

  #order-overview {
    th {
      min-width: 140px;
    }
    th.transfered {
      min-width: 100px;
    }
  }



  table.table-calendar {
    border-bottom: 1px solid $grey;

    [data-status] {
      border-radius: 20px 0;
      padding: 20px;

      @include clearfix();

      h6 {
        font-weight: 500;
        margin-bottom: 5px;
      }

      p {
        margin-bottom: 5px;
      }

      i {
        float: right;
      }
    }

    [data-disabled="true"] {
      opacity: 0.5;
    }

    [data-status="pending"] {
      background: $yellow;
    }

    [data-status="accepted"] {
      background: $lightgreen;
    }

    [data-status="cancel"] {
      background: $error;
    }

    [data-status="undefined"] {
      background: $grey;
    }

    thead {
      background: $grey;

      th {
        font-size: 2.5rem;
        border: 0;
        text-align: center;
        vertical-align: middle;

        i {
          color: $darkgrey;
        }

        span {
          display: block;
          font-size: 1.125rem;
        }
      }
    }

    tbody {

      td {
        border: 0;
        min-width: 3rem;
        max-width: 9rem;

        .publicHoliday {
          color: $darkgrey;
        }

        div.item {
          cursor: pointer;
          margin: auto;
        }

        div.add {
          cursor: pointer;
          font-size: 2.5rem;
          color: $lightgrey;
          text-align: center;

          span {
            font-size: 0.875rem;
            font-weight: 100 !important;
            background: $lightgrey;
          }
        }
      }
    }
  }

  div.legend {
    text-align: right;

    ul {
      margin: 0 0 15px 0;
      padding: 0;
      list-style: none;

      li {
        margin: 0 10px;
      }

      li[data-status="pending"]:before {
        display: inline-block;
        content: ' ';
        width: 10px;
        height: 10px;
        background: $yellow;
        margin-right: 5px;
      }

      li[data-status="accepted"]:before {
        display: inline-block;
        content: ' ';
        width: 10px;
        height: 10px;
        background: $lightgreen;
        margin-right: 5px;
      }

      li[data-status="cancel"]:before {
        display: inline-block;
        content: ' ';
        width: 10px;
        height: 10px;
        background: $error;
        margin-right: 5px;
      }

      li {
        display: inline-block;
      }
    }
  }
}
