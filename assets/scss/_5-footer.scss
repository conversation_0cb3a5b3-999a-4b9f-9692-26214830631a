footer {

  div.footer-top {
    font-size: 1.25rem;
    text-align: center;
    transform: translateY(-50%);

    div.container {
      border-radius: 20px;
      box-shadow: 0px 0px 30px #00000029;
      padding: 20px 0;
      background: $white;

      p {
        padding: 20px;
        margin: 0;
      }

      div.row>div:nth-of-type(2) {
        border-right: 1px solid $lightgrey;
        border-left: 1px solid $lightgrey;
      }

      div div p a {
        color: #212529;
        font-weight: 100;
      }
    }

    @media screen and (max-width: 575px) {
      transform: translateY(-15%);

      div.container {
        margin: 0 15px;
        padding: 10px 0;
        max-width: calc(100% - 30px);
        overflow: hidden;

        p {
          padding: 10px;
          margin: 0;
        }

        div.row>div:nth-of-type(2) {
          border-top: 1px solid $lightgrey;
          border-bottom: 1px solid $lightgrey;
        }
      }
    }
  }

  div.footer-middle {
    padding: 0 0 50px 0;

    img {
      width: 120px;
    }

    p {
      padding: 10px 0;
    }

    @media screen and (max-width: 767px) {
      padding: 15px 0;
    }
  }

  div.footer-bottom {
    background: $grey;
    padding-top: 10px;

    ul {
      margin: 0;
      padding: 0;

      li {
        display: inline-block;
        margin: 0 50px 10px 0;
      }
    }
  }
}
