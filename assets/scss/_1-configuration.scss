@font-face {
  font-family: Helsinki;
  src: url(../fonts/Helsinki-Medium_532c63ab.woff) format("woff");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: Helsinki;
  src: url(../fonts/Helsinki-Thin_f1e3650a.woff) format("woff");
  font-weight: 100;
  font-style: normal;
}

$font: "Helsinki",
sans-serif;
$fontSize: 16px;
$fontSizeMobile: 16px;

$baseColor: #212529;
$grey: #efefef;
$lightgrey: #b4b3b5;
$darkgrey: #7d7d7d;
$white: #fff;
$black: #000;

$darkblue: #003cc1;
$lightgreen: #8bba13;
$petrol: #143d45;
$turquoise: #a8d7ce;
$yellow: #FFF95A;
$error: #ff432e;
$warning: #FFF3EE;

$boxShadowSmall: 0px 3px 6px rgba(0, 0, 0, 0.29);
$boxShadow: 0px 10px 35px rgba(0, 0, 0, 0.05);
$boxShadowBig: 0px 0px 30px rgba(0, 0, 0, 0.29);

/**
 * Mixin
**/

@mixin clearfix {

  &:before,
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin shadow-border {
  div.container {
    &:before {
      position: absolute;
      display: block;
      content: "";
      left: -2%;
      bottom: -2%;
      width: 100%;
      height: 100%;
      background: linear-gradient(40deg, rgba(0, 0, 0, 0.045), transparent 27%);
      border-radius: 0 0 0 30px;

      @media only screen and (max-width: 767px) {
        content: none;
      }
    }
  }
}