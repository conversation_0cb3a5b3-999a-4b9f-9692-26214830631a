document.addEventListener('DOMContentLoaded', function () {
    const contactForm = document.querySelector('form[name="contact"]');

    if (!contactForm) {
        return;
    }

    const requestTypeSelect = document.getElementById('contact_request_types');
    const disponumberInput = document.getElementById('contact_disponumber');
    const disponumberRequiredSpan = document.getElementById('disponummer_required');
    const collectingPlaceInput = document.getElementById('contact_collecting_place');
    const senderEmailInput = document.getElementById('contact_sender_email');
    const messageInput = document.getElementById('contact_message');
    const captchaWidget = document.getElementById('captcha');

    const updateDisponumberRequirement = () => {
        if (!requestTypeSelect || !disponumberInput || !disponumberRequiredSpan) {
            return;
        }
        const isRequired = requestTypeSelect.value !== 'general_request';
        disponumberInput.required = isRequired;
        disponumberRequiredSpan.textContent = isRequired ? '*' : '';
    };


    const removeAllErrorMessages = () => {
        const errorMessages = contactForm.querySelectorAll('.invalid-feedback');
        errorMessages.forEach(msg => msg.remove());

        const invalidFields = contactForm.querySelectorAll('.is-invalid');
        invalidFields.forEach(field => field.classList.remove('is-invalid'));
    };

    const addErrorMessage = (field, message) => {
        if (!field) return;

        field.classList.add('is-invalid');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentElement.appendChild(errorDiv);
    };


    const validateEmail = (email) => {
        const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    };

    const handleFormSubmit = (event) => {
        event.preventDefault();
        removeAllErrorMessages();
        let isFormValid = true;

        const fieldsToValidate = [
            { element: requestTypeSelect, required: true, message: 'Bitte wählen Sie eine Anfrageart.' },
            { element: collectingPlaceInput, required: true, message: 'Bitte geben Sie eine Anfallstelle an.' },
            { element: disponumberInput, checkDynamicRequirement: true, message: 'Bitte geben Sie eine Disponummer an.' },
            {
                element: senderEmailInput,
                required: true,
                message: 'Bitte geben Sie Ihre E-Mail-Adresse an.',
                validator: (value) => validateEmail(value),
                invalidMessage: 'Bitte geben Sie eine gültige E-Mail-Adresse ein.'
            },
            {
                element: messageInput,
                required: true,
                message: 'Bitte geben Sie eine Nachricht ein.',
                minLength: 10,
                minLengthMessage: 'Die Nachricht muss mindestens 10 Zeichen lang sein.'
            }
        ];

        for (const field of fieldsToValidate) {
            if (!field.element) continue;

            const value = field.element.value.trim();

            let isFieldRequired = field.required;
            if (field.checkDynamicRequirement) {
                isFieldRequired = field.element.required;
            }

            if (isFieldRequired && value === '') {
                addErrorMessage(field.element, field.message);
                isFormValid = false;
                continue;
            }

            if (field.validator && value !== '' && !field.validator(value)) {
                addErrorMessage(field.element, field.invalidMessage);
                isFormValid = false;
            }

            if (field.minLength && value !== '' && value.length < field.minLength) {
                addErrorMessage(field.element, field.minLengthMessage);
                isFormValid = false;
            }
        }

        const captchaResponse = contactForm.querySelector('[name="h-captcha-response"]');
        if (!captchaResponse || captchaResponse.value === '') {
            addErrorMessage(captchaWidget, 'Bitte bestätigen Sie, dass Sie kein Roboter sind.');
            isFormValid = false;
        }

        if (isFormValid) {
            contactForm.submit();
        }
    };

    contactForm.addEventListener('submit', handleFormSubmit);

    if (requestTypeSelect) {
        requestTypeSelect.addEventListener('change', updateDisponumberRequirement);
        updateDisponumberRequirement();
    }
});
