/*
*   JS functions for order new clearance list page.
*
*/

$(document).ready(function () {

    if ($("table#order-overview").length) {
        // dt4()
        $("#order-overview").DataTable({
            language: {
                url: "//cdn.datatables.net/plug-ins/1.10.19/i18n/German.json"
            }
        });
    }

    $('div.popup-transfer button[type="save"]').on("click", function (e) {
        e.preventDefault();

        $('#transfer-save-button').prop('disabled', true);
        setTimeout(function () { 
            $('#transfer-save-button').prop('disabled', false); // enable the button after 2 seconds
         }, 2000);

        let data = {
            "placeId": $(this).data('place-id'),
            "from": $(this).data('from'),
            "to": $(this).data('to')
        }

        $.ajax({
            url:  '/ajax/transfer-orders',
            type: 'POST',
            data: { json:JSON.stringify(data) },
            async: true,

            success: function(data, status) {
                // console.log(data);
                if ('undefined' == typeof data.errors) {
                    $("div.popup-error").fadeIn();
                } else if (0 < data.errors.length) {
                    $("div.popup-error").fadeIn();
                } else {
                    $("div.popup-thanks").fadeIn();
                }
            },
            error : function(xhr, textStatus, errorThrown) {
                console.log('ajax post request failed.');
                $("div.popup-error").fadeIn();
            }
        });

    });

    $('button[type="transfer"]').on("click", function () {
        $("div.popup-transfer").fadeIn();
    });

    $('div.popup-transfer button[type="ok"]').on("click", function () {
        window.location.reload();
    });

    $('div.popup-thanks button[type="ok"]').on("click", function () {
        window.location.reload();
    });

    $('div.popup-errorCancel button[type="ok"]').on("click", function () {
        window.location.reload();
    });

    $('div.popup-error button[type="ok"]').on("click", function () {
        window.location.reload();
    });

    $("div.popup i.close").on("click", function () {
        $("div.popup").fadeOut(function () {
            $('div.popup button[type="delete"]').addClass("d-none");
        });
    });

    /*$('div.popup button[type="submit"]').on("click", function () {
        // with post and reload from symfony implemented
        window.location.reload();
    });*/

    $('div.add').on("click", function (event) {
        if ($(this).data('isorderable')) {
            $("#popup-dayinfo")
                .text($(this).data('day'))
                .append('<span>'+$(this).data('dayname')+'</span>');
            $("#order_collecting_systemProvider")
                .attr('disabled', false)
                .attr('required', true);
            $("#order_collecting_dateTime")
                .attr('value', $(this).data('date'));
            $("#order_collecting_disposalNumber")
                .attr('disabled', true)
                .attr('value', 'Wird systemseitig generiert.');

            $("#order_collecting_driverMessage")
                .attr('disabled', false)
                .text('');
    
            $("#order_collecting_dispoMessage")
                .attr('disabled', false)
                .text('');

            $('div.popup div[id="contact-text"]').addClass("d-none").hide();
            $('div.popup div[id="cancel-text"]').addClass("d-none").hide();
            $('div.popup div[id="cancel-info"]').addClass("d-none").hide();
            $('div.popup button[id="contact-button"]').addClass("d-none").hide();
            $('div.popup button[type="cancelPopup-button"]').addClass("d-none").hide(); 
            $('div.popup button[id="delete-button"]').addClass("d-none").hide();
            $('div.popup button[id="submit-button"]').removeClass("d-none").show();
            $("div.popup").fadeIn();
        }
    });

    $('div.popup button[id="delete-button"]').on("click", function () {
        const id = $(this).attr('data-collect-registration-id');
        $.ajax({
            url:  '/ajax/delete-collect-order/'+id,
            type: 'DELETE',
            async: false,

               success: function(data, status) {
                console.log(data);
                window.location.reload();
            },
            error : function(xhr, textStatus, errorThrown) {
                console.log('ajax delete request failed.');
            }
        
        });
    });


    $('button[id="cancel-button"]').on("click", function () {

        //console.log($(this));

        $('#popupCancelId').text($(this).data('collect-registration-id'));
        $('#popupCancelDispoNum').text($(this).data('disposal-number'));
        $('#popupCancelSystemproviderName').text($(this).data('systemprovider-name'));

        let dateToCancel = new Date(($(this).data('date-to-cancel')));
        let now = new Date();
        // 48 hours limit in milliseconds
        let dateDiffCancelToOrder = dateToCancel.valueOf() - now.valueOf();
        // #days for chargeable cancellation
        let cancelLimit = 2;
        let millisecondsOfCancelLimit = cancelLimit * 24 * 60 * 60 * 1000;
        // formating the date
        let formatOptionWeek = { year: 'numeric', month: '2-digit', day: '2-digit' };
        let formatOptionDay = { weekday: 'long' }; 
        let chargeableBool = (dateDiffCancelToOrder <= millisecondsOfCancelLimit);
        // formatted
        let formattedWeek = dateToCancel.toLocaleString('de-DE', formatOptionWeek);
        let formattedDay = dateToCancel.toLocaleString('de-DE', formatOptionDay);

        $('#popupCancelDate').text(formattedWeek);
        $('#popupCancelDay').text(formattedDay);

        if(dateDiffCancelToOrder <= millisecondsOfCancelLimit)
            $('#popupCancelChargeable').text('kostenpflichtig');

        $('#cancelOrder-button')
        .attr('data-collect-registration-id', $(this).data('collect-registration-id'))
        .attr('data-disposal-number', $(this).data('disposal-number'))
        .attr('data-systemprovider-name', $(this).data('systemprovider-name'))
        .attr('data-date-to-cancel', formattedWeek)
        .attr('data-date-to-cancel-day', formattedDay)
        .attr('data-timelimit-to-cancel', chargeableBool);

        $("div.popup-cancelOrder").fadeIn();     
    });

    $('div.popup-cancelOrder button[type="ok"]').on("click", function () {
        window.location.reload();
    });

    $('div.popup-successCancel button[type="ok"]').on("click", function () {
        window.location.reload();
    });

    $('div.popup-cancelOrder button[type="cancel"]').on("click", function (e) {
        e.preventDefault();

        //console.log($(this));

        let data = {
            "placeId": $(this).data('place-id')
        }

        const id = $(this).data('collect-registration-id');
        let dateToCancel = $(this).data('date-to-cancel');
        let dateToCancelDay = $(this).data('date-to-cancel-day');
        let cancelLimit = $(this).data('timelimit-to-cancel');
        
        $.ajax({
            url:  '/ajax/cancel-order/'+id,
            type: 'DELETE',
            data: { json:JSON.stringify(data) },
            async: false,
            
       
            success: function(data, status) {
                console.log(data);
             if(data.success){
                $('#popupSuccessCancelDate').text(dateToCancel);
                $('#popupSuccessCancelDay').text(dateToCancelDay);

                if(cancelLimit)
                    $('#popupCancelSuccessChargeable').text('kostenpflichtige');

                $("div.popup-successCancel").fadeIn();
             }    
            else{
                if ('undefined' == typeof data.errors) {
                    $('#popupErrorCancelDate').text(dateToCancel);
                    $('#popupErrorCancelDay').text(dateToCancelDay);
                    $("div.popup-errorCancel").fadeIn();
                } else if (0 < data.errors.length) {
                    $('#popupErrorCancelDate').text(dateToCancel);
                    $('#popupErrorCancelDay').text(dateToCancelDay);
                    $("div.popup-errorCancel").fadeIn();
                }
            }
            },
            error : function(xhr, textStatus, errorThrown) {
                console.log('ajax delete request failed.');
                $('#popupErrorCancelDate').text(dateToCancel);
                $('#popupErrorCancelDay').text(dateToCancelDay);
                $("div.popup-errorCancel").fadeIn();
            }
        });

    });

    $('div.item[data-status="pending"]').on("click", function () {

        let disabled = $(this).attr('data-disabled');

        if ('true' === disabled) {
            return;
        }

        $("#delete-button")
            .attr('data-collect-registration-id', $(this).data('collect-registration-id'));
        
        $("#contact-button")
            .attr('data-disposal-number', $(this).data('disposal-number'));

        $("#cancel-button")
            .attr('data-collect-registration-id', $(this).data('collect-registration-id'))
            .attr('data-disposal-number', $(this).data('disposal-number'))
            .attr('data-systemprovider-name', $(this).data('systemprovider'))
            .attr('data-date-to-cancel', $(this).data('date'))
            .attr('data-date-to-cancel-day', $(this).data('day'));

        $("#order_collecting_dateTime")
            .attr('value', $(this).data('date'));

        $("#popup-dayinfo")
            .text( $(this).data('day') )
            .append('<span>'+$(this).data('dayname')+'</span>');

        $("#order_collecting_systemProvider")
            .attr('required', true)
            .attr('disabled', !$(this).data('transferable'))
            .val( $(this).data('systemprovider-id') ).change();
        console.log($("#order_collecting_disposalNumber"));
        $("#order_collecting_disposalNumber")
            .attr('disabled', !$(this).data('transferable'))
            .attr('value', $(this).data('disposal-number'));

        $("#order_collecting_driverMessage")
            .attr('disabled', !$(this).data('transferable'))
            .text($(this).data('driver-message'));

        $("#order_collecting_dispoMessage")
            .attr('disabled', !$(this).data('transferable'))
            .text($(this).data('dispo-message'));

        let transferable = $(this).attr('data-transferable');
        let status = $(this).attr('data-status');

        if ('true' === transferable && 'pending' === status) {
            $('div.popup div[id="contact-text"]').addClass("d-none").hide();
            $('div.popup div[id="cancel-text"]').addClass("d-none").hide();
            $('div.popup div[id="cancel-info"]').addClass("d-none").hide();
            $('div.popup button[id="contact-button"]').addClass("d-none").hide();
            $('div.popup button[id="cancel-button"]').addClass("d-none").hide(); 
            $('div.popup button[id="delete-button"]').removeClass("d-none").show();
            $('div.popup button[id="submit-button"]').removeClass("d-none").show();
        } else {
            $('div.popup div[id="contact-text"]').removeClass("d-none").show();
            $('div.popup div[id="cancel-text"]').addClass("d-none").hide();
            $('div.popup div[id="cancel-info"]').addClass("d-none").hide();
            $('div.popup button[id="contact-button"]').removeClass("d-none").show();
            $('div.popup button[id="cancel-button"]').removeClass("d-none").show(); 
            $('div.popup button[id="delete-button"]').addClass("d-none").hide();
            $('div.popup button[id="submit-button"]').addClass("d-none").hide();
        }

        $("div.popup").fadeIn();
    });

    $("#contact-button").on("click", function () {
        window.location.href = '/contact?disponumber=' + $(this).data('disposal-number');
    });

    $("#contact-button-list").on("click", function () {
        window.location.href = '/contact?collectingplace=' + $(this).data('collectingplace') + '&contractarea=' + $(this).data('contractarea');
    });

    $('div.item[data-status="accepted"], div.item[data-status="cancel"]').on("click", function () {

        $("#delete-button")
            .attr('data-collect-registration-id', $(this).data('collect-registration-id'));

         $("#cancel-button")
            .attr('data-collect-registration-id', $(this).data('collect-registration-id'))
            .attr('data-disposal-number', $(this).data('disposal-number'))
            .attr('data-systemprovider-name', $(this).data('systemprovider'))
            .attr('data-date-to-cancel', $(this).data('date'))
            .attr('data-date-to-cancel-day', $(this).data('day'));

        $("#contact-button")
            .attr('data-disposal-number', $(this).data('disposal-number'));

        $("#order_collecting_dateTime")
            .attr('value', $(this).data('date'));
        $("#popup-dayinfo")
            .text($(this).data('day'))
            .append('<span>'+$(this).data('dayname')+'</span>');

        $("#order_collecting_systemProvider")
            .attr('required', true)
            .attr('disabled', 'disabled')
            .val($(this)
            .data('systemprovider-id')).change();

        $("#order_collecting_disposalNumber")
            .attr('disabled', 'disabled')
            .attr('value', $(this).data('disposal-number'));

        $("#order_collecting_driverMessage")
            .attr('disabled', 'disabled')
            .text($(this).data('driver-message'));

        $("#order_collecting_dispoMessage")
            .attr('disabled', 'disabled')
            .text($(this).data('dispo-message'));

        let status = $(this).attr('data-status');
        if('cancel' == status) {
            $('div.popup div[id="cancel-info"]').removeClass("d-none").show();
            $('div.popup div[id="cancel-text"]').removeClass("d-none").show(); 
            $('div.popup div[id="contact-text"]').addClass("d-none").hide();    
            $('div.popup button[id="cancel-button"]').addClass("d-none").hide(); 
            
        }
        else if('cancel' != status){
            $('div.popup div[id="cancel-info"]').addClass("d-none").hide();
            $('div.popup div[id="cancel-text"]').addClass("d-none").hide(); 
            $('div.popup button[id="cancel-button"]').removeClass("d-none").show(); 
            $('div.popup div[id="contact-text"]').removeClass("d-none").show();

        }

        $('div.popup button[id="contact-button"]').removeClass("d-none").show();
        $('div.popup button[id="delete-button"]').addClass("d-none").hide();
        $('div.popup button[id="submit-button"]').addClass("d-none").hide();

        $("div.popup").fadeIn();
    });

});
