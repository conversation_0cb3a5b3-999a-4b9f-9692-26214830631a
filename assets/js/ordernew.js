/*
*   JS functions for order new page (form before order calendar).
*
*/

$(document).ready(function () {

    function setAreas() {
        const id = $("#collecting_entrence_collectingPlace option:selected").val();
        // console.log('select_changed', id);
        $.ajax({
            url:  '/ajax/get-collecting-place/'+id,
            type: 'GET',
            async: true,

            success: function(data, status) {
                let json=data;
                $("#collect-place-dsdid").text(json.dsdId);
                $("#collect-place-name").text(json.name);
                $("#collect-place-street").text(json.street);
                $("#collect-place-housenumber").text(json.houseNumber);
                $("#collect-place-postalcode").text(json.postalCode);
                $("#collect-place-city").text(json.city);

                $('#collecting_entrence_contract_contractArea').empty();
                $.each(json.contractList, function(key, value) {
                    $('#collecting_entrence_contract_contractArea')
                        .append($('<option>', { value : key })
                            .text(value ));
                });
            },
            error : function(xhr, textStatus, errorThrown) {
                console.log('ajax request failed.');
            }
        });
    }
    setAreas();
    $("#collecting_entrence_collectingPlace").on("change", function(event){ setAreas(); });
});
