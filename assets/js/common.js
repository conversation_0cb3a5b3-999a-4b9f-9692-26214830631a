import '@prezero/blackgrid/dist/style.css';
import { BlackGrid } from "@prezero/blackgrid";

window.Noty = require('noty');

export const defaultBlackgridOptions = {
    i18n: 'de_DE',
    theme: 'prezero',
    dragable: false,
    hideable: false,
    sortable: true,
    reloadable: true,
    multiselection: false,
    details: false,
    searchable: {
        global: true,
        columns: false
    },
    navigation: {
        entries: true,
        pagination: true,
        itemsPerPage: 25,
        itemsPerPageDropdown: [5, 10, 50, 100]
    },
    config: {},
    header: [],
    actions: [],    
    onCreate () {
        console.table('Event: Create')
    },
    onLoadBefore() {
        console.table('Event: Load Before')
    },
    onLoadAfter(loadingTime) {
        console.table('Event: Load After', loadingTime)
    },
    onReload() {
        console.table('Event: Reload')
    },
    onReloadable() {
        console.table('Event: Reloading')
    },
    onDragging(oldIndex, newIndex) {
        console.table('Event: Dragging', oldIndex, newIndex)
    },
    onHidding(value, column) {
        console.table('Event: Hidding', value, column)
    },
    onSorting (order, column, conditions, next) {
        console.table('Event: Sorting', order, column, conditions)
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onSearchingCol (value, column, conditions, next) {
        console.table('Event: Searching Col', value, column, conditions)
        next()
    },
    onSearchingGlob (value, conditions, next) {
        console.table('Event: Searching Glob', value, conditions)
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onPagination (currentPage, newPage, conditions, next) {
        console.table('Event: Pagination', currentPage, newPage, conditions)
        // conditions wieviele elemente pro seite
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onItemsPerPageDropdown(oldItems, newItems, conditions, next) {
        console.table('Event: onItemsPerPageDropdown', oldItems, newItems, conditions)
        next()
    },
    onCustomizable () {
        console.table('Event: Customizabling')
    },
    onCustomizableSave (config) {
        console.table('Event: Customizabling Save', config)
    },
    onCustomizableClose () {
        console.table('Event: Customizabling Close')
    },
    onCustomizableClear () {
        console.table('Event: Customizabling Clear')
    },
    onFilterable (filter, conditions, next) {
        console.table('Event: Filtering', filter, conditions)
    }
};

export const smallBlackgridOptions = {    
    i18n: 'de_DE',
    theme: 'prezero',
    dragable: false,
    hideable: false,
    sortable: true,
    reloadable: false,
    multiselection: false,
    details: false,
    searchable: {
        global: false,
        columns: false
    },
    navigation: {
        entries: false,
        pagination: false,
        //itemsPerPage: 5,
        //itemsPerPageDropdown: [10, 25, 50]
    },
    config: {},
    header: [],
    actions: [],    
    onCreate () {
        console.table('Event: Create')
    },
    onLoadBefore() {
        console.table('Event: Load Before')
    },
    onLoadAfter(loadingTime) {
        console.table('Event: Load After', loadingTime)
    },
    onReload() {
        console.table('Event: Reload')
    },
    onReloadable() {
        console.table('Event: Reloading')
    },
    onDragging(oldIndex, newIndex) {
        console.table('Event: Dragging', oldIndex, newIndex)
    },
    onHidding(value, column) {
        console.table('Event: Hidding', value, column)
    },
    onSorting (order, column, conditions, next) {
        console.table('Event: Sorting', order, column, conditions)
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onSearchingCol (value, column, conditions, next) {
        console.table('Event: Searching Col', value, column, conditions)
        next()
    },
    onSearchingGlob (value, conditions, next) {
        console.table('Event: Searching Glob', value, conditions)
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onPagination (currentPage, newPage, conditions, next) {
        console.table('Event: Pagination', currentPage, newPage, conditions)
        // conditions wieviele elemente pro seite
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onItemsPerPageDropdown(oldItems, newItems, conditions, next) {
        console.table('Event: onItemsPerPageDropdown', oldItems, newItems, conditions)
        next()
    },
    onCustomizable () {
        console.table('Event: Customizabling')
    },
    onCustomizableSave (config) {
        console.table('Event: Customizabling Save', config)
    },
    onCustomizableClose () {
        console.table('Event: Customizabling Close')
    },
    onCustomizableClear () {
        console.table('Event: Customizabling Clear')
    },
    onFilterable (filter, conditions, next) {
        console.table('Event: Filtering', filter, conditions)
    }
};

export const multiselectBlackgridOptions = {    
    i18n: 'de_DE',
    theme: 'prezero',
    dragable: false,
    hideable: false,
    sortable: true,
    reloadable: false,
    multiselection: true,
    details: false,
    searchable: {
        global: true,
        columns: false
    },
    navigation: {
        entries: false,
        pagination: false,
        //itemsPerPage: 5,
        //itemsPerPageDropdown: [10, 25, 50]
    },
    config: {},
    header: [],
    actions: [],    
    onCreate () {
        console.table('Event: Create')
    },
    onLoadBefore() {
        console.table('Event: Load Before')
    },
    onLoadAfter(loadingTime) {
        console.table('Event: Load After', loadingTime)
    },
    onReload() {
        console.table('Event: Reload')
    },
    onReloadable() {
        console.table('Event: Reloading')
    },
    onDragging(oldIndex, newIndex) {
        console.table('Event: Dragging', oldIndex, newIndex)
    },
    onHidding(value, column) {
        console.table('Event: Hidding', value, column)
    },
    onSorting (order, column, conditions, next) {
        console.table('Event: Sorting', order, column, conditions)
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onSearchingCol (value, column, conditions, next) {
        console.table('Event: Searching Col', value, column, conditions)
        next()
    },
    onSearchingGlob (value, conditions, next) {
        console.table('Event: Searching Glob', value, conditions)
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onPagination (currentPage, newPage, conditions, next) {
        console.table('Event: Pagination', currentPage, newPage, conditions)
        // conditions wieviele elemente pro seite
        // next([
        //   { id: { value: '1' }, name: { value: 'Hans' }, mail: { value: '<EMAIL>'}, phone: { value: '123' } },
        //   { id: { value: '2' }, name: { value: 'Peter' }, mail: { value: '<EMAIL>'}, phone: { value: '456' } }
        // ])
        next()
    },
    onItemsPerPageDropdown(oldItems, newItems, conditions, next) {
        console.table('Event: onItemsPerPageDropdown', oldItems, newItems, conditions)
        next()
    },
    onCustomizable () {
        console.table('Event: Customizabling')
    },
    onCustomizableSave (config) {
        console.table('Event: Customizabling Save', config)
    },
    onCustomizableClose () {
        console.table('Event: Customizabling Close')
    },
    onCustomizableClear () {
        console.table('Event: Customizabling Clear')
    },
    onFilterable (filter, conditions, next) {
        console.table('Event: Filtering', filter, conditions)
    }
};

function getFormattedDisplayValueForType(columns, column, value) {
    let columnType = columns[column]?.type ?? 'string';

    switch (columnType) {
        case 'percent':
            return `${value}%`;
        case 'date':
            return value ? new Date(value).toLocaleDateString() : undefined;
        default:
            return value;
    }
}

export function initializeLocalizedBlackgrids(classname, options) {
    Array.from(document.getElementsByClassName(classname)).forEach(elem => {

        let gridId = elem.getAttribute('id');
        let translation = elem.getAttribute('data-translation') ?? 'de_DE';
        let columns = JSON.parse(elem.getAttribute('data-columns') ?? "{}");
        let source = JSON.parse(elem.getAttribute('data-source') ?? "[]");
        
        let gridConfig = {};
        let gridHeader = [{}];

        Object.keys(columns).forEach(key => {
            gridConfig[key] = columns[key].visible ?? false;
            gridHeader[0][key] = {
                label: columns[key].label
            };
        });
        
        let tableData = source.map(item => {

            Object.keys(item).map(key => item[key] = {value: getFormattedDisplayValueForType(columns, key, item[key])});

            return item;
        });


        let grid = new BlackGrid(`#${gridId}`, {
            ...options,
            i18n: `${translation}`,
            config: gridConfig,
            header: gridHeader,            
            data: tableData
        });

        grid.create();
    });
}

export function initializeMultiselectBlackgrids() {

    Array.from(document.getElementsByClassName('blackgridwithoptions')).forEach(elem => {

        let gridId = elem.getAttribute('id');
        let translation = elem.getAttribute('data-translation') ?? 'de_DE.yaml';
        let key = elem.getAttribute('data-key-column');
        let columns = JSON.parse(elem.getAttribute('data-columns') ?? "{}");
        let source = JSON.parse(elem.getAttribute('data-source') ?? "[]");

        let gridConfig = {};
        let gridHeader = [{}];

        let tableData = source.map(item => {

            Object.keys(item).map(key => item[key] = {value: item[key]});

            return item;
        });

        Object.keys(columns).forEach(key => {
            gridConfig[key] = columns[key].visible ?? false;
            gridHeader[0][key] = {
                label: columns[key].label
            };
        });

        let grid = new BlackGrid(`#${gridId}`, {
            ...multiselectBlackgridOptions,
            i18n: `/build/i18n/${translation}`, 
            config: gridConfig,
            header: gridHeader,
            data: tableData,
            onSelectionChanged: (grid, selection) => {
                let selectedKeys = Object.keys(selection).map(index => {
                    let selectedItem = selection[index];

                    return selectedItem[key].value;
                });

                let selectElement = document.querySelector(`[data-id='${grid.wrapper.substring(1)}']`);

                for (let i = 0; i < selectElement.options.length; i++) {
                    selectElement.options[i].selected = selectedKeys.indexOf(selectElement.options[i].value) >= 0;
                }
            }
        });

        grid.create();
    });
}
