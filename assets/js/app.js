/*
 * Welcome to your app's main JavaScript file!
 *
 * We recommend including the built version of this JavaScript file
 * (and its CSS file) in your base layout (base.html.twig).
 */

// any CSS you import will output into a single css file (app.css in this case)
// import '../css/app.css';

// Need jQuery? Install it with "yarn add jquery", then uncomment to import it.
import $ from 'jquery';
import browser from "./browser";

// create global $ and jQuery variables
global.$ = global.jQuery = $;

// console.log('Hello Webpack Encore! Edit me in assets/js/app.js');

$(document).ready(function () {
    var browserName = 
            browser.getBrowserName() == 'Chrome' ? 'Google Chrome' : 
            browser.getBrowserName() == 'Firefox' ? 'Mozilla Firefox' : 
            browser.getBrowserName() == 'IE' ? 'Internet Explorer' :
            browser.getBrowserName() == 'Safari' ? 'Apple Safari' : 
            browser.getBrowserName() == 'Netscape' ? 'Netscape Navigator' : 
            browser.getBrowserName() ;

    if ((browser.getBrowserName() === 'Chrome' && browser.getBrowserVersion() <= 60)
        || (browser.getBrowserName() === 'IE' && browser.getBrowserVersion() <= 11)
        || (browser.getBrowserName() === 'Firefox' && browser.getBrowserVersion() <= 50)) {
            $("header").prepend("<div class='browsererror'><i class='far fa-bell'></i>Sie verwenden einen veralteten Webbrowser [" + browserName + " " + browser.getBrowserVersion() + "]. Bitte aktualisieren Sie Ihren Browser, um den vollen Funktionsumfang des Portals nutzen zu können.</div>");

    }
});