import {defaultBlackgridOptions, initializeLocalizedBlackgrids, multiselectBlackgridOptions} from '../common';
import { initializeDialogEvents, showDialog } from '../pzdialog';

import $ from 'jquery';

$(function() {
    $('#add_user_button').on('click', () => showDialog('#edit_user_popup'));
    initializeDialogEvents('#edit_user_popup');

    initializeLocalizedBlackgrids('blackgrid', {
        ...defaultBlackgridOptions,
        actions: [
            {
                location: 'row',
                icon: 'fa-solid fa-info',
                tooltip: 'Detailansicht',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    window.location.href = data.detailLink.value;
                }
            },
            {
                location: 'row',
                icon: 'fa-solid fa-key',
                tooltip: 'Berechtigungen verwalten',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    window.location.href = data.permissionLink.value;
                }
            }
        ],
    });
});
