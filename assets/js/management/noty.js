import $ from "jquery";
import * as Noty from "noty";

$(function() {
    const searchParams = new URLSearchParams(window.location.search)

    if (searchParams.has('noty')) {
        let message = 'Es ist ein Fehler aufgetreten.'
        let type = 'alert'

        if (searchParams.has('noty')) {
            type = searchParams.get('noty')
        }

        if (searchParams.has('message')) {
            message = searchParams.get('message')
        }

        new Noty({
            type: type,
            timeout: 3000,
            progressBar: false,
            layout: 'topRight',
            closeWith: ['click', 'button'],
            text: message,
        }).show();
    }
})
