import $ from 'jquery';
import { initializeDialogEvents, showDialog } from "../pzdialog";

$(function() {
    $('#edit_user_button').on('click', () => showDialog('#edit_user_popup'));
    $('#delete_user_button').on('click', () => showDialog('#delete_user_popup'));
    $('#lock_user_button').on('click', () => showDialog('#lock_user_popup'));
    $('#unlock_user_button').on('click', () => showDialog('#unlock_user_popup'));
    $('#reset_user_button').on('click', () => showDialog('#reset_user_popup'));

    initializeDialogEvents('#edit_user_popup');
    initializeDialogEvents('#delete_user_popup');
    initializeDialogEvents('#lock_user_popup');
    initializeDialogEvents('#unlock_user_popup');
    initializeDialogEvents('#reset_user_popup');
});
