import {defaultBlackgridOptions, initializeLocalizedBlackgrids, multiselectBlackgridOptions} from '../common';
import { initializeDialogEvents, showDialog } from '../pzdialog';

import $ from 'jquery';

$(function() {
    $('#add_user_button').on('click', () => showDialog('#edit_user_popup'));
    initializeDialogEvents('#edit_user_popup');
    
    initializeLocalizedBlackgrids('blackgrid-users', {
        ...multiselectBlackgridOptions,
        actions: [
            {
                location: 'head',
                icon: 'fa-solid fa-user-plus',
                tooltip: '<PERSON><PERSON><PERSON> zuweisen',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    let userIds = Object.values(data).map(row => {
                        let uuid = row?.id?.value.match('[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[89aAbB][a-f0-9]{3}-[a-f0-9]{12}');
                        return uuid[0];
                    }).filter(value => value);

                    let url = '/management/contractArea/'+data[Object.keys(data)[0]]?.contractArea?.value+'/permission/users/add';

                    if(data[Object.keys(data)[0]]?.collectingPlace?.value) {
                        url = '/management/contractArea/'+data[Object.keys(data)[0]]?.contractArea?.value+'/permission/collectingplaces/'+data[Object.keys(data)[0]]?.collectingPlace?.value+'/users/add';
                    }

                    $.ajax({
                        url: url,
                        type: 'post',
                        data: JSON.stringify(userIds),
                        success: function(response){
                            window.location = window.location.href.split("?")[0] + '?noty=success&message=Benutzer wurden erfolgreich zugewiesen.';
                        }
                    });
                }
            },
            {
                location: 'head',
                icon: 'fa-solid fa-user-minus',
                tooltip: 'Zuweisung aufheben',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    let userIds = Object.values(data).map(row => {
                        let uuid = row?.id?.value.match('[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[89aAbB][a-f0-9]{3}-[a-f0-9]{12}');
                        return uuid[0];
                    }).filter(value => value);

                    let url = '/management/contractArea/'+data[Object.keys(data)[0]]?.contractArea?.value+'/permission/users/delete';

                    if(data[Object.keys(data)[0]]?.collectingPlace?.value) {
                        url = '/management/contractArea/'+data[Object.keys(data)[0]]?.contractArea?.value+'/permission/collectingplaces/'+data[Object.keys(data)[0]]?.collectingPlace?.value+'/users/delete';
                    }

                    $.ajax({
                        url: url,
                        type: 'post',
                        data: JSON.stringify(userIds),
                        success: function(response){
                            window.location = window.location.href.split("?")[0] + '?noty=success&message=Benutzer wurden erfolgreich abgewählt.';
                        }
                    });
                }
            }
        ],
    });

    initializeLocalizedBlackgrids('blackgrid-collectingplaces', {
        ...defaultBlackgridOptions,
        actions: [
            {
                location: 'row',
                icon: 'fa-solid fa-info',
                tooltip: 'Detailansicht',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    window.location.href = data.detailLink.value;
                }
            },
            {
                location: 'row',
                icon: 'fa-solid fa-key',
                tooltip: 'Berechtigungen verwalten',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    window.location.href = data.permissionLink.value;
                }
            }
        ],
    });
});
