import { defaultBlackgridOptions, initializeLocalizedBlackgrids } from '../common';

import $ from 'jquery';
import {initializeDialogEvents, showDialog} from "../pzdialog";

$(function() {
    $('#add_user_button').on('click', () => showDialog('#edit_user_popup'));
    initializeDialogEvents('#edit_user_popup');

    initializeLocalizedBlackgrids('blackgrid', {
        ...defaultBlackgridOptions,
        actions: [
            {
                location: 'row',
                icon: 'fa-solid fa-pen-to-square',
                tooltip: 'Benutzer bearbeiten',
                init: (data, show) => {
                    show()
                },
                handler: (data) => {
                    window.location.href = data.detailLink.value;
                }
            }
        ],
    });
});
