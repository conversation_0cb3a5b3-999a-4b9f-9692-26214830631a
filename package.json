{"devDependencies": {"@babel/core": "7.28.5", "@babel/preset-env": "7.28.5", "@symfony/webpack-encore": "5.2.0", "@tailwindcss/forms": "0.5.10", "autoprefixer": "10.4.21", "core-js": "3.46.0", "file-loader": "6.2.0", "postcss": "8.5.6", "postcss-loader": "8.2.0", "regenerator-runtime": "0.14.1", "sass": "1.93.2", "tailwindcss": "4.1.16", "webpack-cli": "6.0.1", "webpack-notifier": "1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"@fortawesome/fontawesome-free": "7.1.0", "@popperjs/core": "2.11.8", "@prezero/blackgrid": "1.12.0", "@tailwindcss/postcss": "^4.1.16", "bootstrap": "5.3.8", "copy-webpack-plugin": "13.0.1", "jquery": "3.7.1", "noty": "3.2.0-beta-deprecated", "postcss-import": "16.1.1", "sass-loader": "16.0.6", "webpack": "5.102.1"}}