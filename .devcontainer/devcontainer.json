{"name": "LVP-Portal", "dockerComposeFile": "../docker-compose.yml", "service": "app", "workspaceFolder": "/app", "settings": {"terminal.integrated.shell.linux": "/bin/bash"}, "extensions": ["github.vscode-pull-request-github", "editorconfig.editorconfig", "mblode.twig-language-2", "bmewburn.vscode-intelephense-client", "<PERSON><PERSON><PERSON><PERSON>.composer", "redhat.vscode-yaml", "calebporzio.better-phpunit", "xdebug.php-debug"]}