name: Build Release Image

on:
  release:
    types:
      - created

run-name: Build Release Image (${{ github.event.release.tag_name }})

jobs:
  build-release-images:
    name: Build release images
    uses: prezero/workflows/.github/workflows/docker-build.yaml@e02b1adc51e0cf889c3a5aa64a178d541067539c # v1.40.4
    with:
      ENV: prod
      DOCKERFILE: Dockerfile
      IMAGE: ghcr.io/${{ github.repository }}
      TAG: ${{ github.event.release.tag_name }}
      RELEASE_CREATED: true
    secrets: inherit
