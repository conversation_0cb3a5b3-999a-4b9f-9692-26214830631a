name: Tag Deployment K8s

on:
    workflow_dispatch:
        inputs:
            env:
                description: "Environment to deploy the tag to (qa|prod)"
                required: false
                type: choice
                options:
                    - qa
                    - prod

jobs:
    setup-env-and-resolve-tag:
        runs-on: prezero-github-runner
        environment: ${{ inputs.env }}
        outputs:
            IMAGE_NAME: ${{ steps.setup-env.outputs.IMAGE_NAME }}
            IMAGE_TAG: ${{ steps.tag.outputs.tag }}
            HEALTHCHECK_URL: ${{ steps.setup-env.outputs.HEALTHCHECK_URL }}
            ARGOCD_URL: ${{ steps.setup-env.outputs.ARGOCD_URL }}
            ENV: ${{ steps.setup-env.outputs.ENV }}
        steps:
            - name: Check if current github ref is a tag
              id: tag
              run: |
                  if [[ ${{ github.ref }} =~ ^refs/tags/v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                      tag=${{ github.ref }}
                      tag=${tag:11}
                      echo "tag=$tag" >> $GITHUB_OUTPUT
                  else
                      echo "This action requires to be executed on a tag"
                      exit 1
                  fi

            - name: Setup Environment
              id: setup-env
              run: |
                  echo "IMAGE_NAME=${{env.IMAGE_NAME}}" >> $GITHUB_OUTPUT;
                  echo "DOCKER_IMAGE=${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT;
                  echo "HEALTHCHECK_URL=${{ vars.HEALTHCHECK_URL }}" >> $GITHUB_OUTPUT;
                  echo "ARGOCD_URL=${{ vars.ARGOCD_URL }}" >> $GITHUB_OUTPUT;
                  echo "ENV=${{ inputs.env }}" >> $GITHUB_OUTPUT;

    build-and-deploy:
        needs: [setup-env-and-resolve-tag]
        uses: prezero/workflows/.github/workflows/deployment.yaml@e02b1adc51e0cf889c3a5aa64a178d541067539c # v1.40.4
        secrets: inherit
        with:
            ENV: ${{ needs.setup-env-and-resolve-tag.outputs.ENV }}
            DOCKERFILE: docker/Dockerfile
            IMAGE: ${{ needs.setup-env-and-resolve-tag.outputs.IMAGE_NAME }}
            TAG: ${{ needs.setup-env-and-resolve-tag.outputs.IMAGE_TAG }}
            deployment_repository: "prezero/lvpportal-deployment"
            deployment_repository_branch: "main"
            component: "backend"
            kubernetes_type_name: "backend"
            project_short_name: "lvpportal"
            ARGOCD_URL: ${{ needs.setup-env-and-resolve-tag.outputs.ARGOCD_URL }}
            ARGOCD_APP_NAME: "lvpportal-${{ needs.setup-env-and-resolve-tag.outputs.ENV }}"
            HEALTHCHECK_URL: ${{ needs.setup-env-and-resolve-tag.outputs.HEALTHCHECK_URL }}
            execute_php_tests: false
            execute_migrations: true
            # On tags the image is built using the release tag
            build_image: false
            deploy_tag: true
