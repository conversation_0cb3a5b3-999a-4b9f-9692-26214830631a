name: Releases

on:
    push:
        branches:
            - master
            - release
    workflow_dispatch:

jobs:
    release-please:
        runs-on: prezero-github-runner
        outputs:
            release_created: ${{ steps.release.outputs.release_created }}
            tag_name: ${{ steps.release.outputs.tag_name }}
        steps:
            - uses: googleapis/release-please-action@16a9c90856f42705d54a6fda1823352bdc62cf38 # v4
              id: release
              with:
                  token: ${{ secrets.PREZERO_GITHUB_TOKEN }}
                  target-branch: ${{ github.ref_name }}
