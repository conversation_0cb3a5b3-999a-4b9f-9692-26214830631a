include .env.local

GIT_EMAIL = $(shell git config --get user.email)

# Compose files
COMPOSE_FILES=-f docker-compose.yml
# Maybe some local overrides will come in the future ?
#COMPOSE_FILES=-f docker-compose.yml -f infrastructure/local/docker-local-overrides.yml

## —— Makefile operations ————————————————————————————————
.PHONY: help
help: ## Outputs this help screen
	@grep -E '(^[a-zA-Z0-9_-]+:.*?##.*$$)|(^##)' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}{printf "\033[32m%-30s\033[0m %s\n", $$1, $$2}' | sed -e 's/\[32m##/[33m/'

## —— Project operations ————————————————————————————————
## Using the step 'make setup' led to massive problems while provisioning the project for the first time. The cause is not known yet.
## Hence, devs should run make setup-containers and setup-composer separately while provisioning the project first time as mentioned in the root README.md file
.PHONY: setup
setup: ## Set up project containers and install composer packages
	$(MAKE) setup-containers
	$(MAKE) setup-composer

.PHONY: setup-containers
setup-containers: ## Set up project containers and start them
	$(MAKE) docker-login
	$(MAKE) docker-build
	$(MAKE) docker-up

.PHONY: setup-composer
setup-composer: ## Set up composer packagaes and dependencies
	$(MAKE) composer-install
	$(MAKE) cache-clear
	$(MAKE) oauth-keys-generate

.PHONY: start
start: ## Start project
	$(MAKE) docker-up

## —— Symfony ————————————————————————————————
.PHONY: setup-data
setup-data: ## Create a new database and create demo data
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:database:drop -f"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:database:create"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:migrations:migrate --em=default --configuration=./config/doctrine_migrations_data.yaml"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:migrations:migrate --em=default --configuration=./config/doctrine_migrations_files.yaml"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "yarn install"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "yarn dev"
	$(MAKE) cache-clear

.PHONY: cache-clear
cache-clear: ## cache-clear
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console cache:clear"

.PHONY: assets
assets: ## Install yarn and assets
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "yarn install"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "yarn dev"

.PHONY: composer-install
composer-install: ## Install all composer dependencies
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "composer install"

.PHONY: composer-update
composer-update: ## Update all composer dependencies
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "composer update"

.PHONY: oauth-keys-generate
oauth-keys-generate: ## Generating public and private keys for OAuth2
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "openssl genrsa -out config/jwt/private.key 2048"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "openssl rsa -in config/jwt/private.key -pubout -out config/jwt/public.key"

## —— Code Quality ————————————————————————————————
.PHONY: php-stan
php-stan: ## Analyze php code TODO PHPSTAN configuration file for project
	vendor/bin/phpstan analyze --configuration=phpstan.neon --memory-limit=2G --no-progress

.PHONY: php-cs-fix
php-cs-fix: ## Fix php code
	PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix --config=.php-cs-fixer.dist.php

.PHONY: php-test-cleanup
php-test-cleanup: ## drop, create, migrate database for env test
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:database:drop -f --env=test"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:database:create --env=test"
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "php bin/console doctrine:migrations:migrate -n --env=test"

.PHONY: php-test
php-test: ## Execute tests
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "vendor/bin/phpunit --configuration phpunit.xml.dist"

.PHONY: php-test-coverage
php-test-coverage: ## Execute tests with code coverage report
	docker exec --user=root $(APP_NAME)-backend /bin/sh -c "XDEBUG_MODE=coverage vendor/bin/phpunit --configuration phpunit.xml.dist --coverage-html reports/"

.PHONY: grumphp
grumphp: ## Execute tests
	vendor/bin/grumphp run

## —— Docker operations ————————————————————————————————
.PHONY: docker-login
docker-login: ## Login in to docker
	docker login ghcr.io

.PHONY: docker-login-credentials
docker-login-credentials: ## Login in to docker with credentials
	@if [ -n "$(GIT_EMAIL)" ]; then \
  		echo 'Logging in to Github container registry as "$(GIT_EMAIL)"'; \
  		echo "Please enter your personal access token as your password below (NOT YOUR PLAIN GITHUB PASSWORD !!!) "; \
		docker login ghcr.io -u "$(GIT_EMAIL)"; \
	else \
		echo "Error !!! GIT_EMAIL is not configured yet. To set your commit name and email address, run the following git config command in project root folder."; \
		echo "'git config user.name YOUR NAME' and then 'git config user.email <EMAIL>"; \
		echo "To set your global commit name and email address, run the same command above but with the --global option. "; \
		echo "For example 'git config --global user.name YOUR NAME' and then 'git config --global user.email <EMAIL>"; \
	fi

.PHONY: docker-build
docker-build: ## Build docker container
	docker compose ${COMPOSE_FILES} build

.PHONY: docker-up
docker-up: ## Start docker container
	docker compose ${COMPOSE_FILES} -p $(APP_NAME) up -d --remove-orphans

.PHONY: docker-down
docker-down: ## Stop all containers and also remove them.
	docker compose ${COMPOSE_FILES} -p $(APP_NAME) down --remove-orphans -v

.PHONY: docker-stop
docker-stop: ## Stop all containers but not remove them.
	docker compose ${COMPOSE_FILES} -p $(APP_NAME) stop

.PHONY: docker-ssh
docker-ssh: ## SSH into php docker container
	docker exec -it --user=root $(APP_NAME)-backend /bin/sh

.PHONY: docker-remove-images
docker-remove-images: ## Remove docker images
	docker image prune -a -f

.PHONY: docker-remove-volumes
docker-remove-volumes: ## Remove docker volumes
	docker volume prune -f

.PHONY: docker-clean
docker-clean: ## Remove all docker container volumes and images
	@if $(MAKE) -s confirm-docker-clean ; then \
    	     make docker-down; \
    	     make docker-remove-images; \
    	     make docker-remove-volumes; \
	fi

.PHONY: confirm-docker-clean
confirm-docker-clean: ## Ask for user interaction for purging docker
	@if [[ -z "$(CI)" ]]; then \
		REPLY="" ; \
		read -p "⚠ Are you sure? This will delete all containers, images and volumes. \
Also the images from other projects will be lost !!!! Use at your own risk !!!! [y/n] " -r ; \
		if [[ ! $$REPLY =~ ^[Yy]$$ ]]; then \
			printf $(_ERROR) "OK" "Aborting" ; \
			exit 1 ; \
		else \
			printf $(_TITLE) "OK" "Continuing" ; \
			exit 0; \
		fi \
	fi

_WARN := "\033[33m[%s]\033[0m %s\n"  # Yellow text for "printf"
_TITLE := "\033[32m[%s]\033[0m %s\n" # Green text for "printf"
_ERROR := "\033[31m[%s]\033[0m %s\n" # Red text for "printf"
