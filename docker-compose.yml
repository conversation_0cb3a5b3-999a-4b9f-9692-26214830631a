services:
  app:
    container_name: ${APP_NAME}-backend
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    env_file:
      - .env
      - .env.local
    volumes:
      - ${APP_VOLUME:-.}:/app
      - var:/app/var
      - caddy_data:/data
      - caddy_config:/config
    ports:
      - target: 8080
        published: ${HTTP_PORT:-8000}
        protocol: tcp
    restart: unless-stopped
    environment:
      MERCURE_EXTRA_DIRECTIVES: demo
      MERCURE_PUBLISHER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
      MERCURE_SUBSCRIBER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
      TRUSTED_PROXIES: ${TRUSTED_PROXIES:-*********/8,10.0.0.0/8,**********/12,***********/16}
      TRUSTED_HOSTS: ^${SERVER_NAME:-example\.com|localhost|127.0.0.1}|app$$
      PHP_IDE_CONFIG: serverName=${XDEBUG_SERVER_NAME:-lvpportal}
      XDEBUG_MODE: debug
    extra_hosts:
      - host.docker.internal:host-gateway
    tty: true
    networks: [ lvpportal_network ]

  postgres:
    image: postgres:17-alpine3.21@sha256:849377bbbdb1143b82d1b87646eb8ab48241fe32b85ab6667e02e0af139aaa74
    env_file: [ { path: .env, required: true }, { path: .env.local, required: false } ]
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lvp-portal}
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-userpwd}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ${POSTGRES_VOLUME:-.devcontainer/data/postdb}:/var/lib/postgresql/data
    ports:
      - '8001:5432'
    networks: [ lvpportal_network ]

  db:
    container_name: ${APP_NAME}-db
    image: mariadb:10.11@sha256:dbe56e20372fc6d6b8e0e396866ba89c4c7f128c38c4f59aaa54d957db95790c
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PWD:-rootpwd}
      MYSQL_DATABASE: ${DB_NAME:-lvp-portal}
      MYSQL_USER: ${DB_USER:-user}
      MYSQL_PASSWORD: ${DB_USER_PWD:-userpwd}
    volumes:
      - ${DB_VOLUME:-.devcontainer/data/db}:/var/lib/mysql
    ports:
      - ${DB_PORT:-3306}:3306
    networks: [ lvpportal_network ]

  dbfiles:
    container_name: ${APP_NAME}-db-files
    image: mariadb:10.11@sha256:dbe56e20372fc6d6b8e0e396866ba89c4c7f128c38c4f59aaa54d957db95790c
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PWD:-rootpwd}
      MYSQL_DATABASE: ${DB_NAME:-lvp-portal}
      MYSQL_USER: ${DB_USER:-user}
      MYSQL_PASSWORD: ${DB_USER_PWD:-userpwd}
    volumes:
      - ${DB_VOLUME:-.devcontainer/data/dbfiles}:/var/lib/mysql
    ports:
      - ${DB_PORT:-3307}:3306
    networks: [ lvpportal_network ]

  s3storage:
    image: quay.io/minio/minio:latest@sha256:14cea493d9a34af32f524e538b8346cf79f3321eff8e708c1e2960462bd8936e
    ports:
      - '8004:7550'
      - '8005:7551'
    env_file:
      - .env.local
    user: ${USER_UID:-1000}:${USER_GID:-1000}
    volumes:
      - .devcontainer/data/s3storage:/data
    command:
      - 'server'
      - '/data'
      - '--address'
      - ':7550'
      - '--console-address'
      - ':7551'
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
      MINIO_CONSOLE_PORT_NUMBER: 7551
      MINIO_API_PORT_NUMBER: 7550
    networks: [ lvpportal_network ]

  mailcatcher:
    container_name: ${APP_NAME}-mailcatcher
    image: dockage/mailcatcher:0.9.0@sha256:5aaa41f9f45b9af5cd3e694cd4682dfaddb54b894211d3661ddcd0d4267a77ad
    ports:
      - ${MAIL_CATCHER_PORT_UI:-1080}:1080
    networks: [ lvpportal_network ]

networks:
  lvpportal_network:
    driver: bridge
    name: lvpportal_network

volumes:
  var:
  caddy_data:
  caddy_config:
